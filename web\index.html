<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="google-signin-client_id" content="299372626077-9t0vnmomck2ipg36diprn1r87ibs1era.apps.googleusercontent.com">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="budget">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/pg" href="favicon/png"/>
  <link rel="icon" type="image/x-icon" href="favicon.ico"/>

  <meta name="theme-color" media="(prefers-color-scheme: light)" content="#FFFFFF">
  <meta name="theme-color" media="(prefers-color-scheme: dark)"  content="#000000">

  <!-- Permissions Policy -->
  <meta http-equiv="Permissions-Policy" content="camera=(self), microphone=(self), geolocation=(self), interest-cohort=()">

  <!-- Content Security Policy -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self' https://*.gstatic.com https://*.googleapis.com https://*.firebaseapp.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.gstatic.com https://*.googleapis.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https://*.gstatic.com; connect-src 'self' https://*.googleapis.com https://*.firebaseio.com wss://*.firebaseio.com https://*.cloudfunctions.net https://*.app-budget-flutter.firebaseapp.com; font-src 'self' data:; media-src 'self' blob:;">

  <!-- Network Access -->
  <meta http-equiv="Access-Control-Allow-Origin" content="*">
  <meta http-equiv="Access-Control-Allow-Methods" content="GET, POST, PUT, DELETE, OPTIONS">
  <meta http-equiv="Access-Control-Allow-Headers" content="Origin, X-Requested-With, Content-Type, Accept, Authorization">

  <title>Cashew</title>
  <link rel="manifest" href="manifest.json">

  <meta name="title" content="Cashew" />
  <meta name="description" content="A budget and financial tracking application designed for you" />

  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://cashewapp.web.app/assets/preview.png" />
  <meta property="og:title" content="Cashew" />
  <meta property="og:description" content="A budget and financial tracking application designed for you" />
  <meta property="og:image" content="https://cashewapp.web.app/assets/preview.png" />

  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://cashewapp.web.app/assets/preview.png" />
  <meta property="twitter:title" content="Cashew" />
  <meta property="twitter:description" content="A budget and financial tracking application designed for you" />
  <meta property="twitter:image" content="https://cashewapp.web.app/assets/preview.png" />

  <script>
    // The value below is injected by flutter build, do not touch.
    const serviceWorkerVersion = "{{flutter_service_worker_version}}";
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>
</head>
<body oncontextmenu="return false;">
  <style>
    @media (prefers-color-scheme: dark) {
      body {
        background-color: black;
      }
    }
  </style>
  <script type="module">
    // Import the functions you need from the SDKs you need
    import { initializeApp } from "https://www.gstatic.com/firebasejs/10.12.4/firebase-app.js";
    import { getAuth } from "https://www.gstatic.com/firebasejs/10.12.4/firebase-auth.js";
    import { getFirestore } from "https://www.gstatic.com/firebasejs/10.12.4/firebase-firestore.js";

    // TODO: Add SDKs for Firebase products that you want to use
    // https://firebase.google.com/docs/web/setup#available-libraries

    // Your web app's Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyCeTK6PXAgexFnMILrg2QOWawYr9RcMgfQ",
      authDomain: "saini-budget-app.firebaseapp.com",
      projectId: "saini-budget-app",
      storageBucket: "saini-budget-app.firebasestorage.app",
      messagingSenderId: "299372626077",
      appId: "1:299372626077:web:f35e2fd833ba3eba2f1474"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
  </script>
  <script defer src="sql-wasm.js"></script>
  <div id="loading">
    <style>
      body {
        inset: 0;
        overflow: hidden;
        margin: 0;
        padding: 0;
        position: fixed;
      }

      #loading {
        align-items: center;
        display: flex;
        height: 100%;
        justify-content: center;
        width: 100%;
      }

      #loading .loading-spinner, #loading .loading-icon {
        transition: opacity 0.5s;
      }

      #loading.main_done .loading-spinner {
        opacity: 0;
      }

      #loading.init_done .loading-spinner, #loading.init_done .loading-icon {
        /* animation: .33s ease-in-out 0s 1 forwards zoom; */
        opacity: 0;
      }

      .loading-spinner {
        margin: 40px 10px;
        display: inline-block;
        width: 45px;
        height: 45px;
        border: 4px solid rgba(255,255,255,.3);
        border-radius: 50%;
        border-top-color: #e3e7ff;
        animation: spin 1s linear infinite;
        opacity: .66;
      }

      .column{
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .loading-icon{
        margin: 40px 10px;
        width: min(30vh, 30vw);
        height: min(30vh, 30vw);
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
    <div class="column">
      <img src="./icons/Icon.png" class="loading-icon"/>
      <div class="loading-spinner"></div>
    </div>
  </div>
  <script>
    window.addEventListener('load', function(ev) {
      var loading = document.querySelector('#loading');
      // Download main.dart.js
      _flutter.loader.load({
        serviceWorkerSettings: {
          serviceWorkerVersion: serviceWorkerVersion,
        }
      }).then(function(engineInitializer) {
        loading.classList.add('main_done');
        return engineInitializer.initializeEngine({
          useColorEmoji: true,
        });
      }).then(function(appRunner) {
        loading.classList.add('init_done');
        return appRunner.runApp();
      }).then(function (_) {
        // Wait a few milliseconds so users can see the "zoom" animation
        // before getting rid of the "loading" div.
        window.setTimeout(function () {
          loading.remove();
        }, 200);

        // https://github.com/flutter/flutter/issues/55913#issuecomment-1248816491

        const flutterRoot = document.getElementsByTagName('flt-glass-pane').item(0);
        flutterRoot.addEventListener('touchstart', (e) => {
          // is not near edge of view, exit
          if (e.pageX > 24 && e.pageX < window.innerWidth - 24) return;
          // prevent swipe to navigate gesture
          e.preventDefault();
        });
      });
    });
  </script>
</body>
</html>
