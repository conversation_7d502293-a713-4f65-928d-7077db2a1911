@echo off
echo ========================================
echo  Budget App - Production Web Server
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter and add it to your PATH
    pause
    exit /b 1
)

echo Flutter version:
flutter --version
echo.

REM Clean and get dependencies
echo Cleaning and getting dependencies...
flutter clean
flutter pub get
echo.

echo.
echo ========================================
echo Building production web version...
echo ========================================
echo.

REM Build for production
flutter build web --release
if %errorlevel% neq 0 (
    echo ERROR: Production build failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo Production build completed successfully!
echo ========================================
echo.
echo Build output location: build\web\
echo.
echo To serve the production build locally, you can:
echo 1. Use Python: python -m http.server 8080 (from build\web\ directory)
echo 2. Use Node.js: npx serve build\web\ -p 8080
echo 3. Use any other static file server
echo.

REM Ask if user wants to serve the build
set /p serve="Do you want to serve the production build now? (y/n): "
if /i "%serve%"=="y" (
    echo.
    echo Starting production server on localhost:8080...
    echo Press Ctrl+C to stop the server
    echo.
    cd build\web
    python -m http.server 8080
    cd ..\..
)

echo.
echo Production build process completed.
pause
