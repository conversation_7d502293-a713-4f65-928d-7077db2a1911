[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: armeabi-v7a", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]