[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: armeabi-v7a", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a\\android_gradle_build.json due to:", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a'", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a'", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\App\\\\Android Studio\\\\FlutterSDK\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=26\" ^\n  \"-DANDROID_PLATFORM=android-26\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DANDROID_NDK=D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\App\\\\Android Studio\\\\Projects\\\\budget\\\\build\\\\app\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\3x194437\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\App\\\\Android Studio\\\\Projects\\\\budget\\\\build\\\\app\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\3x194437\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-BD:\\\\App\\\\Android Studio\\\\Projects\\\\budget\\\\build\\\\.cxx\\\\RelWithDebInfo\\\\3x194437\\\\armeabi-v7a\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\App\\\\Android Studio\\\\FlutterSDK\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=26\" ^\n  \"-DANDROID_PLATFORM=android-26\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DANDROID_NDK=D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\App\\\\Android Studio\\\\AndoirdSDK\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\App\\\\Android Studio\\\\Projects\\\\budget\\\\build\\\\app\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\3x194437\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\App\\\\Android Studio\\\\Projects\\\\budget\\\\build\\\\app\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\3x194437\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-BD:\\\\App\\\\Android Studio\\\\Projects\\\\budget\\\\build\\\\.cxx\\\\RelWithDebInfo\\\\3x194437\\\\armeabi-v7a\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a\\compile_commands.json.bin existed but not D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a\\compile_commands.json", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]