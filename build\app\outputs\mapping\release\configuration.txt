# The proguard configuration file for the following section is D:\App\Android Studio\Projects\budget\build\app\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.core.widget.RemoteViewsCompatService { <init>(); }
-keep class androidx.credentials.playservices.CredentialProviderMetadataHolder { <init>(); }
-keep class androidx.credentials.playservices.HiddenActivity { <init>(); }
-keep class androidx.glance.appwidget.GlanceRemoteViewsService { <init>(); }
-keep class androidx.glance.appwidget.MyPackageReplacedReceiver { <init>(); }
-keep class androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver { <init>(); }
-keep class androidx.glance.appwidget.action.ActionTrampolineActivity { <init>(); }
-keep class androidx.glance.appwidget.action.InvisibleActionTrampolineActivity { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivity { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivityV2 { <init>(); }
-keep class com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver { <init>(); }
-keep class com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.RevocationBoundService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.internal.SignInHubActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementJobService { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementReceiver { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementService { <init>(); }
-keep class com.google.android.gms.metadata.ModuleDependencies { <init>(); }
-keep class com.google.android.play.core.common.PlayCoreDialogWrapperActivity { <init>(); }
-keep class com.google.firebase.auth.internal.GenericIdpActivity { <init>(); }
-keep class com.google.firebase.auth.internal.RecaptchaActivity { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.saini.budget.MainActivity { <init>(); }
-keep class com.saini.budget.NetWorthPlusWidgetProvider { <init>(); }
-keep class com.saini.budget.NetWorthWidgetProvider { <init>(); }
-keep class com.saini.budget.PlusWidgetProvider { <init>(); }
-keep class com.saini.budget.TransferWidgetProvider { <init>(); }
-keep class dev.fluttercommunity.plus.share.ShareFileProvider { <init>(); }
-keep class dev.fluttercommunity.plus.share.SharePlusPendingIntent { <init>(); }
-keep class io.flutter.plugins.imagepicker.ImagePickerFileProvider { <init>(); }
-keep class io.flutter.plugins.urllauncher.WebViewActivity { <init>(); }
-keep class notification.listener.service.NotificationListener { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SwitchCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.preference.UnPressableLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.preference.internal.PreferenceImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from D:\App\Android Studio\Projects\budget\build\app\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is D:\App\Android Studio\Projects\budget\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from D:\App\Android Studio\Projects\budget\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0
# The proguard configuration file for the following section is D:\App\Android Studio\FlutterSDK\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# Build the ephemeral app in a module project.
# Prevents: Warning: library class <plugin-package> depends on program class io.flutter.plugin.**
# This is due to plugins (libraries) depending on the embedding (the program jar)
-dontwarn io.flutter.plugin.**

# The android.** package is provided by the OS at runtime.
-dontwarn android.**

# In some cases, R8 is incorrectly stripping plugin classes. Keep
# all implementations of FlutterPlugin until we can determine
# why this is the case.
# See https://github.com/flutter/flutter/issues/154580.
-if class * implements io.flutter.embedding.engine.plugins.FlutterPlugin
-keep,allowshrinking,allowobfuscation class <1>

# End of content from D:\App\Android Studio\FlutterSDK\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# The proguard configuration file for the following section is D:\App\Android Studio\Projects\budget\build\file_picker\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
#TIKA PROGUARD RULES
-keep class org.apache.tika.** { *; }
-keep class javax.xml.stream.XMLResolver.** { *; }
-dontwarn javax.xml.stream.XMLInputFactory
-dontwarn javax.xml.stream.XMLResolver
-dontwarn org.osgi.**
-dontwarn aQute.bnd.annotation.**
# End of content from D:\App\Android Studio\Projects\budget\build\file_picker\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ffe1e81574cd6a0f2eba27d21a19b9cc\transformed\jetified-firebase-firestore-25.1.3\proguard.txt
# Needed for DNS resolution.  Present in OpenJDK, but not Android
-dontwarn javax.naming.**

# Don't warn about checkerframework
#
# Guava uses the checkerframework and the annotations
# can safely be ignored at runtime.
-dontwarn org.checkerframework.**

# Guava warnings:
-dontwarn java.lang.ClassValue
-dontwarn com.google.j2objc.annotations.Weak
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.lang.model.element.Modifier

# Okhttp warnings.
-dontwarn okio.**
-dontwarn com.google.j2objc.annotations.**

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ffe1e81574cd6a0f2eba27d21a19b9cc\transformed\jetified-firebase-firestore-25.1.3\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\Projects\budget\build\app_links\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# SPDX-FileCopyrightText: 2016, microG Project Team
# SPDX-License-Identifier: CC0-1.0

# Keep AutoSafeParcelables
-keep public class * extends org.microg.safeparcel.AutoSafeParcelable {
    @org.microg.safeparcel.SafeParcelable.Field *;
    @org.microg.safeparcel.SafeParceled *;
}

# Keep asInterface method cause it's accessed from SafeParcel
-keepattributes InnerClasses
-keepclassmembers interface * extends android.os.IInterface {
    public static class *;
}
-keep public class * extends android.os.Binder { public static *; }

# End of content from D:\App\Android Studio\Projects\budget\build\app_links\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\Projects\budget\build\flutter_plugin_android_lifecycle\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The point of this package is to specify that a dependent plugin intends to
# use the AndroidX lifecycle classes. Make sure no R8 heuristics shrink classes
# brought in by the embedding's pom.
#
# This isn't strictly needed since by definition, plugins using Android
# lifecycles should implement DefaultLifecycleObserver and therefore keep it
# from being shrunk. But there seems to be an R8 bug so this needs to stay
# https://issuetracker.google.com/issues/142778206.
-keep class androidx.lifecycle.DefaultLifecycleObserver

# End of content from D:\App\Android Studio\Projects\budget\build\flutter_plugin_android_lifecycle\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f6db9cf6b7493ae7224f937fd1f5223b\transformed\jetified-glance-appwidget-1.0.0\proguard.txt
#  Copyright (C) 2021 The Android Open Source Project
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

# libproto uses reflection to deserialize a Proto, which Proguard can't accurately detect.
# Keep all the class members of any generated messages to ensure we can deserialize properly inside
# these classes.
-keepclassmembers class * extends androidx.glance.appwidget.protobuf.GeneratedMessageLite {
  <fields>;
}
-keep public class * extends androidx.glance.appwidget.action.ActionCallback
# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f6db9cf6b7493ae7224f937fd1f5223b\transformed\jetified-glance-appwidget-1.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\799d99e76c150c0d18b57ca68234d758\transformed\jetified-firebase-auth-23.2.0\proguard.txt
-dontwarn com.google.appengine.api.**
-dontwarn okio.**
-dontwarn org.apache.**
-dontwarn retrofit.android.**
-dontwarn retrofit.appengine.**
-dontwarn retrofit.client.**
-dontwarn rx.**

# This is necessary for keeping SecureTokenHttpApi and IdentityToolkitHttpApi
# Otherwise those classes get stripped out, as they are only being used
# reflectively.

-keepclasseswithmembernames interface * {
    @retrofit.http.* <methods>;
}

# This is necessary for parsing JSON responses, since the JSON converter uses reflection to figure out the class/type of response.
# We mainly need the *Response.classes to not be stripped out. All the firebase-auth classes are proguarded into "com.google.android.gms.internal.firebase-auth-api*".

-keep class com.google.android.gms.internal.** { *; }

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.firebase-auth-api.zzakg {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\799d99e76c150c0d18b57ca68234d758\transformed\jetified-firebase-auth-23.2.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\360f520b1fd94e46852e32542f950379\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\proguard.txt
-if class androidx.credentials.CredentialManager
-keep class androidx.credentials.playservices.** {
  *;
}
# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\360f520b1fd94e46852e32542f950379\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f1f926d53c819e7d33b2f5379c5b68cc\transformed\biometric-1.1.0\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.AuthenticationCallbackProvider$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.BiometricFragment$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.BiometricManager$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.CancellationSignalProvider$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.CryptoObjectUtils$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.FingerprintDialogFragment$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.KeyguardUtils$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.PackageUtils$Api* {
    <methods>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f1f926d53c819e7d33b2f5379c5b68cc\transformed\biometric-1.1.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\030ef254abd294976f59e7a697258de0\transformed\jetified-billing-7.1.1\proguard.txt
# Keep the AIDL interface
-keep class com.android.vending.billing.** { *; }
-keep class com.google.android.apps.play.billingtestcompanion.aidl.** { *; }

-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.android.apps.common.proguard.UsedByReflection

-keepnames class com.android.billingclient.api.ProxyBillingActivity
-keepnames class com.android.billingclient.api.ProxyBillingActivityV2

# Avoids Proguard warning at build time due to Protobuf use of sun.misc.Unsafe
# and libcore.io.Memory which are available at runtime.
-dontwarn libcore.io.Memory
-dontwarn sun.misc.Unsafe


# For Phenotype
# An unused P/H transitive dependency: com.google.android.libraries.phenotype.registration.PhenotypeResourceReader is stripped out from all Granular normal deps and "can't find reference..." DepsVersionCompat test warning
# is suppressed by ProGuard -dontwarn config.
-dontwarn com.google.android.libraries.phenotype.registration.PhenotypeResourceReader
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Uses reflection to determine if these classes are present and has a graceful
# fallback if they aren't. The test failure it fixes appears to be caused by flogger.
-dontwarn dalvik.system.VMStack
-dontwarn com.google.common.flogger.backend.google.GooglePlatform
-dontwarn com.google.common.flogger.backend.system.DefaultPlatform
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.play_billing.zzhk {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\030ef254abd294976f59e7a697258de0\transformed\jetified-billing-7.1.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e46fddab4d610204f3eeab01e64de068\transformed\jetified-recaptcha-18.7.0\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# Ignore the warning becuse ClassValueCtorCache is never used on Android.
-dontwarn kotlinx.coroutines.internal.ClassValueCtorCache

# Ignore warning due to the usage from Guava and kotlinx.coroutines.internal.ClassValueCtorCache
-dontwarn java.lang.ClassValue

# Ignore warning to accommodate the missing injar of kotlinx.coroutines.flow for
# androidx.slidingpanelayout.widget.
-dontwarn kotlinx.coroutines.flow.**

# This prevents the SDK to be obfuscated again when building the android app.
-keep class com.google.android.recaptcha.** { *; }

# This is required for recaptcha mobile to function properly.
# See: https://cloud.google.com/recaptcha-enterprise/docs/instrument-android-apps
-keep class com.google.android.play.core.integrity.** { *; }
-keep class com.google.android.gms.tasks.** {*;}

# To keep okhttp3 generated files which are used in our NetworkModule which is
# used widely across the app.
-dontwarn com.squareup.okhttp3.**
-dontwarn okhttp3.**
-keep class com.squareup.okhttp3.* { *;}
-keep class okhttp3.**

# Uses reflection to determine if these classes are present and has a graceful
# fallback if they aren't.
-dontwarn dalvik.system.VMStack
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.recaptcha.internal.zzon {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e46fddab4d610204f3eeab01e64de068\transformed\jetified-recaptcha-18.7.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\bd455ce8fc1b616cf7295553675aabf8\transformed\jetified-play-services-auth-base-18.0.10\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.auth.zzev {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\bd455ce8fc1b616cf7295553675aabf8\transformed\jetified-play-services-auth-base-18.0.10\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1bc4c398f578bbdf031437a54583ee44\transformed\jetified-play-services-recaptchabase-16.1.0\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1bc4c398f578bbdf031437a54583ee44\transformed\jetified-play-services-recaptchabase-16.1.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1e0186d6bc8a59e737d4e0a9c8f9c50c\transformed\jetified-play-services-measurement-22.4.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1e0186d6bc8a59e737d4e0a9c8f9c50c\transformed\jetified-play-services-measurement-22.4.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\326044aff86348c3f9a433f1a6682e43\transformed\jetified-play-services-measurement-sdk-22.4.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\326044aff86348c3f9a433f1a6682e43\transformed\jetified-play-services-measurement-sdk-22.4.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\10194e377059f2215011bd5593f6a561\transformed\jetified-play-services-measurement-impl-22.4.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\10194e377059f2215011bd5593f6a561\transformed\jetified-play-services-measurement-impl-22.4.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ac987222f87da4df87deb95cd2cb1ecc\transformed\jetified-play-services-fido-20.1.0\proguard.txt
# Methods enable and disable in this class are complained as unresolved
# references, but they are system APIs and are not used by Fido client apps.
-dontwarn android.nfc.NfcAdapter

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ac987222f87da4df87deb95cd2cb1ecc\transformed\jetified-play-services-fido-20.1.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d7d81e4eedadb04afe270eb5a954785e\transformed\jetified-play-services-base-18.5.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d7d81e4eedadb04afe270eb5a954785e\transformed\jetified-play-services-base-18.5.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\77c133aba392f7d6c8cfd3db365f91bd\transformed\preference-1.2.1\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Preference objects are inflated via reflection
-keep public class androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keep public class * extends androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\77c133aba392f7d6c8cfd3db365f91bd\transformed\preference-1.2.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\35a0b14b32946c892dcc20a2d874884d\transformed\appcompat-1.2.0\proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# aapt is not able to read app::actionViewClass and app:actionProviderClass to produce proguard
# keep rules. Add a commonly used SearchView to the keep list until b/109831488 is resolved.
-keep class androidx.appcompat.widget.SearchView { <init>(...); }

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\35a0b14b32946c892dcc20a2d874884d\transformed\appcompat-1.2.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c50b65fd477dbd062fedfc230c09d684\transformed\jetified-play-services-measurement-api-22.4.0\proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c50b65fd477dbd062fedfc230c09d684\transformed\jetified-play-services-measurement-api-22.4.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\cdc0cd0159344d4961f497b0bb7e6643\transformed\jetified-firebase-auth-interop-20.0.0\proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\cdc0cd0159344d4961f497b0bb7e6643\transformed\jetified-firebase-auth-interop-20.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7c4914fc661d564ac0144dac3614835f\transformed\jetified-firebase-common-21.0.0\proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7c4914fc661d564ac0144dac3614835f\transformed\jetified-firebase-common-21.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2d4eabbe26fa628f25f941abcf11103b\transformed\recyclerview-1.0.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2d4eabbe26fa628f25f941abcf11103b\transformed\recyclerview-1.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b524b25c53bdab712787c6225ebf7af1\transformed\jetified-savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b524b25c53bdab712787c6225ebf7af1\transformed\jetified-savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\74716c935175f8d7c73d642438826c4d\transformed\work-runtime-2.7.1\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker (also marked with @Keep)
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\74716c935175f8d7c73d642438826c4d\transformed\work-runtime-2.7.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ec11c32e4134f31e784b304a7322372e\transformed\jetified-lifecycle-viewmodel-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ec11c32e4134f31e784b304a7322372e\transformed\jetified-lifecycle-viewmodel-release\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a450c90567a15b29b7ac6aa43ca5e6e1\transformed\jetified-lifecycle-process-2.8.7\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a450c90567a15b29b7ac6aa43ca5e6e1\transformed\jetified-lifecycle-process-2.8.7\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\51427771e1e992b63ecbc78e0420bde8\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\51427771e1e992b63ecbc78e0420bde8\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\96f470fe990b1e1b9ad1f3b56721b7a0\transformed\jetified-lifecycle-runtime-release\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# The deprecated `android.app.Fragment` creates `Fragment` instances using reflection.
# See: b/338958225, b/341537875
-keepclasseswithmembers,allowobfuscation public class androidx.lifecycle.ReportFragment {
    public <init>();
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\96f470fe990b1e1b9ad1f3b56721b7a0\transformed\jetified-lifecycle-runtime-release\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a6bb94663fa58fa290e8e1bf76506075\transformed\jetified-window-1.2.0\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A rule that will keep classes that implement SidecarInterface$SidecarCallback if Sidecar seems
# be used. See b/157286362 and b/165268619 for details.
# TODO(b/208543178) investigate how to pass header jar to R8 so we don't need this rule
-if class androidx.window.layout.adapter.sidecar.SidecarCompat {
  public *** setExtensionCallback(androidx.window.layout.adapter.sidecar.ExtensionInterfaceCompat$ExtensionCallbackInterface);
}
-keep class androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback,
 androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback {
  public *** onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState);
  public *** onWindowLayoutChanged(android.os.IBinder, androidx.window.sidecar.SidecarWindowLayoutInfo);
}
# Required for window area API reflection guard
-keep interface androidx.window.area.reflectionguard.* {*;}
# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a6bb94663fa58fa290e8e1bf76506075\transformed\jetified-window-1.2.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a82ffda1bba940459d3a57f57eda9264\transformed\jetified-runtime-1.2.1\proguard.txt
-assumenosideeffects public class androidx.compose.runtime.ComposerKt {
    void sourceInformation(androidx.compose.runtime.Composer,java.lang.String);
    void sourceInformationMarkerStart(androidx.compose.runtime.Composer,int,java.lang.String);
    void sourceInformationMarkerEnd(androidx.compose.runtime.Composer);
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a82ffda1bba940459d3a57f57eda9264\transformed\jetified-runtime-1.2.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\857e9724ce64f8a83f02f5bdc2ce5d7a\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\857e9724ce64f8a83f02f5bdc2ce5d7a\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2e427dd01d106776291d8581cc115e03\transformed\rules\lib\META-INF\proguard\androidx.datastore_datastore-preferences-core.pro
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2e427dd01d106776291d8581cc115e03\transformed\rules\lib\META-INF\proguard\androidx.datastore_datastore-preferences-core.pro
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7cbf1331fdfa0bfd6e08e6a39c45fee5\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7cbf1331fdfa0bfd6e08e6a39c45fee5\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7a2f90473ee5d6c67044ece09383463b\transformed\jetified-play-services-tasks-18.2.0\proguard.txt


# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7a2f90473ee5d6c67044ece09383463b\transformed\jetified-play-services-tasks-18.2.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e6b35f365df304d882ae4d84ff96f250\transformed\jetified-play-services-measurement-sdk-api-22.4.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e6b35f365df304d882ae4d84ff96f250\transformed\jetified-play-services-measurement-sdk-api-22.4.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c18c9cb05fd8077635aafdc5f385974f\transformed\jetified-play-services-measurement-base-22.4.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c18c9cb05fd8077635aafdc5f385974f\transformed\jetified-play-services-measurement-base-22.4.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6b26f1f52c8f1c8f0d7dde318506ccd9\transformed\jetified-play-services-basement-18.5.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.annotations.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6b26f1f52c8f1c8f0d7dde318506ccd9\transformed\jetified-play-services-basement-18.5.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\fbcce704827b86f123581a78e8805be4\transformed\fragment-1.7.1\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\fbcce704827b86f123581a78e8805be4\transformed\fragment-1.7.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8c5223efd9a9e23427a2d2ca9d035da8\transformed\media-1.1.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8c5223efd9a9e23427a2d2ca9d035da8\transformed\media-1.1.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7dc761a8fa3062437420de41b9616513\transformed\coordinatorlayout-1.0.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7dc761a8fa3062437420de41b9616513\transformed\coordinatorlayout-1.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\edc4cb8b23db9b881966656aa09c58e1\transformed\transition-1.4.1\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\edc4cb8b23db9b881966656aa09c58e1\transformed\transition-1.4.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b3cd440ec2b84a727b44347ee5a1955f\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b3cd440ec2b84a727b44347ee5a1955f\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8f32ad1e84eea09473cc783b71dcb560\transformed\core-1.15.0\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8f32ad1e84eea09473cc783b71dcb560\transformed\core-1.15.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\5580a3719458ea6c03dad10d1092da3b\transformed\jetified-googleid-1.1.0\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\5580a3719458ea6c03dad10d1092da3b\transformed\jetified-googleid-1.1.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\72ef6cc4eaa0e72dbf52e580d7969a8e\transformed\rules\lib\META-INF\proguard\okio.pro
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\72ef6cc4eaa0e72dbf52e580d7969a8e\transformed\rules\lib\META-INF\proguard\okio.pro
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9aaf3401104026d67784e912b8abb72d\transformed\rules\lib\META-INF\proguard\protobuf.pro
# Recently Protobuf Javalite introduced a change that relies on reflection,
# which doesn't work with Proguard. This rule keeps the reflection usages in
# (shaded) Protobuf classes in Tink as-is.
# The location of this file is determined by
# - https://developer.android.com/studio/build/shrink-code#configuration-files
# - https://docs.bazel.build/versions/master/be/java.html#java_library.resources
# See also:
# - https://github.com/google/tink/issues/361
# - https://github.com/protocolbuffers/protobuf/issues/6463
# WARNING: the shaded package name com.google.crypto.tink.shaded.protobuf must
# be kept in sync with jar_jar_rules.txt.
-keepclassmembers class * extends com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9aaf3401104026d67784e912b8abb72d\transformed\rules\lib\META-INF\proguard\protobuf.pro
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3573334920de1109ddd4976b5b76459d\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3573334920de1109ddd4976b5b76459d\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1cfbb8b28e87b470894a79dc05ea48a8\transformed\jetified-firebase-components-18.0.0\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1cfbb8b28e87b470894a79dc05ea48a8\transformed\jetified-firebase-components-18.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7d4728e0d5ee4c6e4a2ae914a01f5844\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7d4728e0d5ee4c6e4a2ae914a01f5844\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e9db547e7e916be65b7f728fb9d59b0\transformed\jetified-transport-backend-cct-3.1.8\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e9db547e7e916be65b7f728fb9d59b0\transformed\jetified-transport-backend-cct-3.1.8\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ac0579998c919fbab142055385b74882\transformed\jetified-transport-api-3.0.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ac0579998c919fbab142055385b74882\transformed\jetified-transport-api-3.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4704b4a227b631b38b2328f84b7c657d\transformed\room-runtime-2.2.5\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4704b4a227b631b38b2328f84b7c657d\transformed\room-runtime-2.2.5\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\141dda5f7c9215b12b54622c4e667c99\transformed\jetified-firebase-encoders-json-18.0.0\proguard.txt

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\141dda5f7c9215b12b54622c4e667c99\transformed\jetified-firebase-encoders-json-18.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1e74d73f47278f34f5ab904f6d30e93a\transformed\jetified-core-1.0.0\proguard.txt
# Copyright (C) 2022 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# These interfaces must be kept for the client library to invoke methods in extensions.
-keep interface androidx.window.extensions.core.util.function.Consumer {
  public void accept(***);
}
-keep interface androidx.window.extensions.core.util.function.Predicate {
  public boolean test(***);
}
-keep interface androidx.window.extensions.core.util.function.Function {
  public *** apply(***);
}
# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1e74d73f47278f34f5ab904f6d30e93a\transformed\jetified-core-1.0.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\cd627a04a210943d25fd9f8aaae3109e\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\cd627a04a210943d25fd9f8aaae3109e\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9b0a531cba1613c842390ad2d7e28aff\transformed\rules\lib\META-INF\proguard\gson.pro
### Gson ProGuard and R8 rules which are relevant for all users
### This file is automatically recognized by ProGuard and R8, see https://developer.android.com/build/shrink-code#configuration-files
###
### IMPORTANT:
### - These rules are additive; don't include anything here which is not specific to Gson (such as completely
###   disabling obfuscation for all classes); the user would be unable to disable that then
### - These rules are not complete; users will most likely have to add additional rules for their specific
###   classes, for example to disable obfuscation for certain fields or to keep no-args constructors
###

# Keep generic signatures; needed for correct type resolution
-keepattributes Signature

# Keep Gson annotations
# Note: Cannot perform finer selection here to only cover Gson annotations, see also https://stackoverflow.com/q/47515093
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

### The following rules are needed for R8 in "full mode" which only adheres to `-keepattribtues` if
### the corresponding class or field is matches by a `-keep` rule as well, see
### https://r8.googlesource.com/r8/+/refs/heads/main/compatibility-faq.md#r8-full-mode

# Keep class TypeToken (respectively its generic signature) if present
-if class com.google.gson.reflect.TypeToken
-keep,allowobfuscation class com.google.gson.reflect.TypeToken

# Keep any (anonymous) classes extending TypeToken
-keep,allowobfuscation class * extends com.google.gson.reflect.TypeToken

# Keep classes with @JsonAdapter annotation
-keep,allowobfuscation,allowoptimization @com.google.gson.annotations.JsonAdapter class *

# Keep fields with any other Gson annotation
# Also allow obfuscation, assuming that users will additionally use @SerializedName or
# other means to preserve the field names
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.Expose <fields>;
  @com.google.gson.annotations.JsonAdapter <fields>;
  @com.google.gson.annotations.Since <fields>;
  @com.google.gson.annotations.Until <fields>;
}

# Keep no-args constructor of classes which can be used with @JsonAdapter
# By default their no-args constructor is invoked to create an adapter instance
-keepclassmembers class * extends com.google.gson.TypeAdapter {
  <init>();
}
-keepclassmembers class * implements com.google.gson.TypeAdapterFactory {
  <init>();
}
-keepclassmembers class * implements com.google.gson.JsonSerializer {
  <init>();
}
-keepclassmembers class * implements com.google.gson.JsonDeserializer {
  <init>();
}

# Keep fields annotated with @SerializedName for classes which are referenced.
# If classes with fields annotated with @SerializedName have a no-args
# constructor keep that as well. Based on
# https://issuetracker.google.com/issues/150189783#comment11.
# See also https://github.com/google/gson/pull/2420#discussion_r1241813541
# for a more detailed explanation.
-if class *
-keepclasseswithmembers,allowobfuscation class <1> {
  @com.google.gson.annotations.SerializedName <fields>;
}
-if class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
-keepclassmembers,allowobfuscation,allowoptimization class <1> {
  <init>();
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9b0a531cba1613c842390ad2d7e28aff\transformed\rules\lib\META-INF\proguard\gson.pro
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f50d69ba020c4fd132da6fe551fe1fb9\transformed\jetified-safe-parcel-1.7.0\proguard.txt
# SPDX-FileCopyrightText: 2016, microG Project Team
# SPDX-License-Identifier: CC0-1.0

# Keep AutoSafeParcelables
-keep public class * extends org.microg.safeparcel.AutoSafeParcelable {
    @org.microg.safeparcel.SafeParcelable.Field *;
    @org.microg.safeparcel.SafeParceled *;
}

# Keep asInterface method cause it's accessed from SafeParcel
-keepattributes InnerClasses
-keepclassmembers interface * extends android.os.IInterface {
    public static class *;
}
-keep public class * extends android.os.Binder { public static *; }

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f50d69ba020c4fd132da6fe551fe1fb9\transformed\jetified-safe-parcel-1.7.0\proguard.txt
# The proguard configuration file for the following section is D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8fa616a7c56377e678718ea697555cd6\transformed\jetified-protolite-well-known-types-18.0.1\proguard.txt
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# protobuf-javalite has a bug that requires this workaround rule
# https://github.com/protocolbuffers/protobuf/issues/6463#issuecomment-553183215
-keepclassmembers class * extends com.google.protobuf.GeneratedMessageLite {
  <fields>;
}

# End of content from D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8fa616a7c56377e678718ea697555cd6\transformed\jetified-protolite-well-known-types-18.0.1\proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>