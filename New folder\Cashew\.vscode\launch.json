{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Flutter",
      "request": "launch",
      "type": "dart",
      "args": ["--web-port", "5000"],
      // "flutterMode": "profile",
    },
    // {
    //   "name": "Flutter",
    //   "request": "launch",
    //   "type": "dart",
    //   "flutterMode": "profile"
    // },
    // {
    //   "name": "budget",
    //   "cwd": "budget",
    //   "request": "launch",
    //   "type": "dart"
    // },
    // {
    //   "name": "budget (profile mode)",
    //   "cwd": "budget",
    //   "request": "launch",
    //   "type": "dart",
    //   "flutterMode": "profile"
    // },
  ]
}