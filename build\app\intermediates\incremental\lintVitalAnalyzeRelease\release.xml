<variant
    name="release"
    package="com.saini.budget"
    minSdkVersion="26"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="D:\App\Android Studio\Projects\budget\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="D:\App\Android Studio\Projects\budget\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0;D:\App\Android Studio\FlutterSDK\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="D:\App\Android Studio\Projects\budget\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\99d77ac6f293bddf1564c7fe8d66bf44\transformed\desugar_jdk_libs_configuration-2.1.4-desugar-lint.txt;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\********************************\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="D:\App\Android Studio\Projects\budget\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\App\Android Studio\Projects\budget\build\app\tmp\kotlin-classes\release;D:\App\Android Studio\Projects\budget\build\app\kotlinToolingMetadata;D:\App\Android Studio\Projects\budget\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.saini.budget"
      generatedSourceFolders="D:\App\Android Studio\Projects\budget\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\App\Android Studio\Projects\budget\build\app\generated\res\google-services\release;D:\App\Android Studio\Projects\budget\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\99d77ac6f293bddf1564c7fe8d66bf44\transformed\desugar_jdk_libs_configuration-2.1.4-desugar-lint.txt;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\********************************\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
