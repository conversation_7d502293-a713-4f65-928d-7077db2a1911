@echo off
"D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HD:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=26" ^
  "-DANDROID_PLATFORM=android-26" ^
  "-DANDROID_ABI=armeabi-v7a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a" ^
  "-DANDROID_NDK=D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973" ^
  "-DCMAKE_ANDROID_NDK=D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973" ^
  "-DCMAKE_TOOLCHAIN_FILE=D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\3x194437\\obj\\armeabi-v7a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\3x194437\\obj\\armeabi-v7a" ^
  "-DCMAKE_BUILD_TYPE=RelWithDebInfo" ^
  "-BD:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a" ^
  -GNinja ^
  -Wno-dev ^
  --no-warn-unused-cli
