{"buildFiles": ["D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}