@echo off
echo Starting Budget App Web Server...
echo.

REM Quick start without verbose output
flutter pub get >nul 2>&1
echo Dependencies updated.

echo Building web version...
flutter build web --release >nul 2>&1
if %errorlevel% neq 0 (
    echo Build failed! Check for errors.
    pause
    exit /b 1
)
echo Build completed.

echo Starting local server on localhost:8080...
echo Press Ctrl+C to stop
echo.

REM Try Python first, then Node.js
python --version >nul 2>&1
if %errorlevel% equ 0 (
    cd build\web
    start http://localhost:8080
    python -m http.server 8080
    cd ..\..
) else (
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        start http://localhost:8080
        npx serve build\web -p 8080
    ) else (
        echo Error: Need Python or Node.js to serve files
        pause
    )
)
