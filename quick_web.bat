@echo off
echo Starting Budget App Web Server...
echo.

REM Quick start without verbose output
flutter pub get >nul 2>&1
echo Dependencies updated.

echo Building web version...
flutter build web --release >nul 2>&1
if %errorlevel% neq 0 (
    echo Build failed! Check for errors.
    pause
    exit /b 1
)
echo Build completed.

echo Starting local server on localhost:8080...
echo.
echo Open your browser and go to: http://localhost:8080
echo (For Brave users: Disable Shields if needed)
echo Press Ctrl+C to stop server
echo.

REM Try Python first, then Node.js
python --version >nul 2>&1
if %errorlevel% equ 0 (
    cd build\web
    echo Server running at: http://localhost:8080
    python -m http.server 8080
    cd ..\..
) else (
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo Server running at: http://localhost:8080
        npx serve build\web -p 8080
    ) else (
        echo Error: Need Python or Node.js to serve files
        pause
    )
)
