<libraries>
  <library
      name="__local_aars__:D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
      jars="D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar"
      resolved="__local_aars__:D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar:unspecified"/>
  <library
      name=":@@:file_picker::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\527da7173acd7b01b6d1beecdbfd92a4\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\527da7173acd7b01b6d1beecdbfd92a4\transformed\out\jars\libs\R.jar"
      resolved="com.mr.flutter.plugin.filepicker:file_picker:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\file_picker\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\527da7173acd7b01b6d1beecdbfd92a4\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_timezone::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\73478d85cf8200e12719db931cff4773\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\73478d85cf8200e12719db931cff4773\transformed\out\jars\libs\R.jar"
      resolved="net.wolverinebeach.flutter_timezone:flutter_timezone:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\flutter_timezone\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\73478d85cf8200e12719db931cff4773\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:in_app_review::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e6116fb735671797f62b4b76795f95a\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e6116fb735671797f62b4b76795f95a\transformed\out\jars\libs\R.jar"
      resolved="dev.britannio.in_app_review:in_app_review:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\in_app_review\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e6116fb735671797f62b4b76795f95a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:recaptcha_enterprise_flutter::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3450e58991a4d28dafe39477a07569a3\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3450e58991a4d28dafe39477a07569a3\transformed\out\jars\libs\R.jar"
      resolved="com.google.flutter.recaptcha:recaptcha_enterprise_flutter:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\recaptcha_enterprise_flutter\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3450e58991a4d28dafe39477a07569a3\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:shared_preferences_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\634e46bb0922dc7714a3f142c75ba527\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\634e46bb0922dc7714a3f142c75ba527\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.sharedpreferences:shared_preferences_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\shared_preferences_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\634e46bb0922dc7714a3f142c75ba527\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:22.4.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f859a81e9ee1f941e42516f637d9b8d4\transformed\jetified-firebase-analytics-22.4.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics:22.4.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f859a81e9ee1f941e42516f637d9b8d4\transformed\jetified-firebase-analytics-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:22.4.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c50b65fd477dbd062fedfc230c09d684\transformed\jetified-play-services-measurement-api-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:22.4.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c50b65fd477dbd062fedfc230c09d684\transformed\jetified-play-services-measurement-api-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:local_auth_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\11fca568e5f5a41492d390242fe6442c\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\11fca568e5f5a41492d390242fe6442c\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.localauth:local_auth_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\local_auth_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\11fca568e5f5a41492d390242fe6442c\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:app_links::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3b69ddbc62db4c362caf31141ff9fa28\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3b69ddbc62db4c362caf31141ff9fa28\transformed\out\jars\libs\R.jar"
      resolved="com.llfbandit.app_links:app_links:1.0"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\app_links\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3b69ddbc62db4c362caf31141ff9fa28\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:app_settings::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\811b9e58dc6a8d0f44b19839e22e3cf0\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\811b9e58dc6a8d0f44b19839e22e3cf0\transformed\out\jars\libs\R.jar"
      resolved="com.spencerccf.app_settings:app_settings:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\app_settings\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\811b9e58dc6a8d0f44b19839e22e3cf0\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:cloud_firestore::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8b67d298d4e0f0db7582d8ea3beb523d\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8b67d298d4e0f0db7582d8ea3beb523d\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.cloudfirestore:cloud_firestore:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\cloud_firestore\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8b67d298d4e0f0db7582d8ea3beb523d\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:connectivity_plus::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a69dcec57b9ed558e3d8b65a7f61ff12\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a69dcec57b9ed558e3d8b65a7f61ff12\transformed\out\jars\libs\R.jar"
      resolved="dev.fluttercommunity.plus.connectivity:connectivity_plus:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\connectivity_plus\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a69dcec57b9ed558e3d8b65a7f61ff12\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:device_info_plus::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\99df2e4326c8bd85305d31d1ca57c486\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\99df2e4326c8bd85305d31d1ca57c486\transformed\out\jars\libs\R.jar"
      resolved="dev.fluttercommunity.plus.device_info:device_info_plus:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\device_info_plus\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\99df2e4326c8bd85305d31d1ca57c486\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_auth::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\0dc0d7781edb2a671ec43656334b0741\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\0dc0d7781edb2a671ec43656334b0741\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.auth:firebase_auth:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\firebase_auth\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\0dc0d7781edb2a671ec43656334b0741\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_core::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4367cae2a0a5cfe8d7957b56664a3c01\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4367cae2a0a5cfe8d7957b56664a3c01\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.core:firebase_core:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\firebase_core\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4367cae2a0a5cfe8d7957b56664a3c01\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_charset_detector_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\11887cdfde9580ed844ef193b4a5e474\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\11887cdfde9580ed844ef193b4a5e474\transformed\out\jars\libs\R.jar"
      resolved="com.madlonkay.flutter_charset_detector:flutter_charset_detector_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\flutter_charset_detector_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\11887cdfde9580ed844ef193b4a5e474\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_displaymode::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c024aa7172bec10cfa97a1c3a0b7b3cb\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c024aa7172bec10cfa97a1c3a0b7b3cb\transformed\out\jars\libs\R.jar"
      resolved="com.ajinasokan.flutterdisplaymode:flutter_displaymode:1.0"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\flutter_displaymode\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c024aa7172bec10cfa97a1c3a0b7b3cb\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_local_notifications::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6f601624c679239eb3810fb61ed21755\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6f601624c679239eb3810fb61ed21755\transformed\out\jars\libs\R.jar"
      resolved="com.dexterous.flutterlocalnotifications:flutter_local_notifications:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\flutter_local_notifications\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6f601624c679239eb3810fb61ed21755\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\87fa6f815345d3e2921e38b831912100\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\87fa6f815345d3e2921e38b831912100\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle:1.0"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\flutter_plugin_android_lifecycle\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\87fa6f815345d3e2921e38b831912100\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_secure_storage::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f16766fe4a7c5f0a77a51838b9788bd3\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f16766fe4a7c5f0a77a51838b9788bd3\transformed\out\jars\libs\R.jar"
      resolved="com.it_nomads.fluttersecurestorage:flutter_secure_storage:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\flutter_secure_storage\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f16766fe4a7c5f0a77a51838b9788bd3\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:google_sign_in_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b7bfdbc426f108963d4dfcceccb53e42\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b7bfdbc426f108963d4dfcceccb53e42\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.googlesignin:google_sign_in_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\google_sign_in_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b7bfdbc426f108963d4dfcceccb53e42\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:home_widget::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1140abdbf391836ffb184e69882e3a06\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1140abdbf391836ffb184e69882e3a06\transformed\out\jars\libs\R.jar"
      resolved="es.antonborri.home_widget:home_widget:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\home_widget\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1140abdbf391836ffb184e69882e3a06\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:image_picker_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\40e2c8ef14c5c2bb6118b182d31bfb44\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\40e2c8ef14c5c2bb6118b182d31bfb44\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.imagepicker:image_picker_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\image_picker_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\40e2c8ef14c5c2bb6118b182d31bfb44\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:in_app_purchase_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\250b8a0f3e7aba3cf016e51072be25ef\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\250b8a0f3e7aba3cf016e51072be25ef\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.inapppurchase:in_app_purchase_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\in_app_purchase_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\250b8a0f3e7aba3cf016e51072be25ef\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:notification_listener_service::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9e9bd2700e919b0ed8ce5b8c337a3a42\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9e9bd2700e919b0ed8ce5b8c337a3a42\transformed\out\jars\libs\R.jar"
      resolved="notification.listener.service:notification_listener_service:1.0"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\notification_listener_service\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9e9bd2700e919b0ed8ce5b8c337a3a42\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:package_info_plus::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\91705cccb40c399c75cbe67c91af5ebd\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\91705cccb40c399c75cbe67c91af5ebd\transformed\out\jars\libs\R.jar"
      resolved="dev.fluttercommunity.plus.packageinfo:package_info_plus:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\package_info_plus\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\91705cccb40c399c75cbe67c91af5ebd\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:path_provider_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\aa07f2da227c85953b4c495346746800\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\aa07f2da227c85953b4c495346746800\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.pathprovider:path_provider_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\path_provider_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\aa07f2da227c85953b4c495346746800\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:permission_handler_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b86e61ec7eb58a33836a21cac583d78e\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b86e61ec7eb58a33836a21cac583d78e\transformed\out\jars\libs\R.jar"
      resolved="com.baseflow.permissionhandler:permission_handler_android:1.0"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\permission_handler_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b86e61ec7eb58a33836a21cac583d78e\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:quick_actions_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f9593237a9647756cb6975475af5d69d\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f9593237a9647756cb6975475af5d69d\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.quickactions:quick_actions_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\quick_actions_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f9593237a9647756cb6975475af5d69d\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:share_plus::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8895c0e9e0592a1cb3f129906b729d54\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8895c0e9e0592a1cb3f129906b729d54\transformed\out\jars\libs\R.jar"
      resolved="dev.fluttercommunity.plus.share:share_plus:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\share_plus\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8895c0e9e0592a1cb3f129906b729d54\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:sqlite3_flutter_libs::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6c6d1f0b872b5dcb16b27a69d07be691\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6c6d1f0b872b5dcb16b27a69d07be691\transformed\out\jars\libs\R.jar"
      resolved="eu.simonbinder.sqlite3_flutter_libs:sqlite3_flutter_libs:1.0"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\sqlite3_flutter_libs\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6c6d1f0b872b5dcb16b27a69d07be691\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:system_theme::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b09a21b3cc36151ea0b8024fb7e3cf28\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b09a21b3cc36151ea0b8024fb7e3cf28\transformed\out\jars\libs\R.jar"
      resolved="com.bruno.system_theme:system_theme:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\system_theme\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b09a21b3cc36151ea0b8024fb7e3cf28\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:url_launcher_android::release"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2644c1e6b0d07047242fe3bfeba49c18\transformed\out\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2644c1e6b0d07047242fe3bfeba49c18\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.urllauncher:url_launcher_android:1.0-SNAPSHOT"
      partialResultsDir="D:\App\Android Studio\Projects\budget\build\url_launcher_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2644c1e6b0d07047242fe3bfeba49c18\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea\b9750b7afa5852804d18049641a5c197feff0fbd\flutter_embedding_release-1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea"/>
  <library
      name="androidx.biometric:biometric:1.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f1f926d53c819e7d33b2f5379c5b68cc\transformed\biometric-1.1.0\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f1f926d53c819e7d33b2f5379c5b68cc\transformed\biometric-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:22.4.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1e0186d6bc8a59e737d4e0a9c8f9c50c\transformed\jetified-play-services-measurement-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement:22.4.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1e0186d6bc8a59e737d4e0a9c8f9c50c\transformed\jetified-play-services-measurement-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:22.4.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\326044aff86348c3f9a433f1a6682e43\transformed\jetified-play-services-measurement-sdk-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:22.4.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\326044aff86348c3f9a433f1a6682e43\transformed\jetified-play-services-measurement-sdk-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:22.4.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\10194e377059f2215011bd5593f6a561\transformed\jetified-play-services-measurement-impl-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:22.4.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\10194e377059f2215011bd5593f6a561\transformed\jetified-play-services-measurement-impl-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7ec8bdb6e287f6efe9243452dfc9d5a0\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7ec8bdb6e287f6efe9243452dfc9d5a0\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4d9a393bf2abf5ab6085c2ecb4915c4c\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4d9a393bf2abf5ab6085c2ecb4915c4c\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\bd26d99c8614b91cdbf3e9bbf915d6d4\transformed\jetified-play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\bd26d99c8614b91cdbf3e9bbf915d6d4\transformed\jetified-play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e6b35f365df304d882ae4d84ff96f250\transformed\jetified-play-services-measurement-sdk-api-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:22.4.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e6b35f365df304d882ae4d84ff96f250\transformed\jetified-play-services-measurement-sdk-api-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:22.4.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c18c9cb05fd8077635aafdc5f385974f\transformed\jetified-play-services-measurement-base-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:22.4.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c18c9cb05fd8077635aafdc5f385974f\transformed\jetified-play-services-measurement-base-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8840f06df20c2a3824e631d68599bc86\transformed\jetified-firebase-installations-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8840f06df20c2a3824e631d68599bc86\transformed\jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\88784513617c0d5fbffb9834990d94e3\transformed\jetified-firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\88784513617c0d5fbffb9834990d94e3\transformed\jetified-firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d7d81e4eedadb04afe270eb5a954785e\transformed\jetified-play-services-base-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d7d81e4eedadb04afe270eb5a954785e\transformed\jetified-play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\788269ee55f2a36fb8756f145fb63d1e\transformed\jetified-firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\788269ee55f2a36fb8756f145fb63d1e\transformed\jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7c4914fc661d564ac0144dac3614835f\transformed\jetified-firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7c4914fc661d564ac0144dac3614835f\transformed\jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\fbcce704827b86f123581a78e8805be4\transformed\fragment-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\fbcce704827b86f123581a78e8805be4\transformed\fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\cd80f2dc18fb7ef92dbcb48f2148c6d0\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\cd80f2dc18fb7ef92dbcb48f2148c6d0\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.3@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c5c173c96a5c28a923f20df70dc41cea\transformed\jetified-activity-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.3"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c5c173c96a5c28a923f20df70dc41cea\transformed\jetified-activity-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e460fb0e2c8fc4aee899e4b4deca5c5\transformed\lifecycle-viewmodel-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e460fb0e2c8fc4aee899e4b4deca5c5\transformed\lifecycle-viewmodel-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ec11c32e4134f31e784b304a7322372e\transformed\jetified-lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ec11c32e4134f31e784b304a7322372e\transformed\jetified-lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\96f470fe990b1e1b9ad1f3b56721b7a0\transformed\jetified-lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\96f470fe990b1e1b9ad1f3b56721b7a0\transformed\jetified-lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.7\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.7.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.7"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d62d301067eedfe5f042934cd989055b\transformed\lifecycle-livedata-core-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d62d301067eedfe5f042934cd989055b\transformed\lifecycle-livedata-core-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a450c90567a15b29b7ac6aa43ca5e6e1\transformed\jetified-lifecycle-process-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a450c90567a15b29b7ac6aa43ca5e6e1\transformed\jetified-lifecycle-process-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.7\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.7.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.7"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\51427771e1e992b63ecbc78e0420bde8\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\51427771e1e992b63ecbc78e0420bde8\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a32fcbf00d0889a624e0a1c6b3a6ba33\transformed\jetified-window-java-1.2.0\jars\classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a32fcbf00d0889a624e0a1c6b3a6ba33\transformed\jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a6bb94663fa58fa290e8e1bf76506075\transformed\jetified-window-1.2.0\jars\classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a6bb94663fa58fa290e8e1bf76506075\transformed\jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ffbc25e994eb403dc8f32a8a852d0733\transformed\jetified-ads-adservices-java-1.1.0-beta11\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ffbc25e994eb403dc8f32a8a852d0733\transformed\jetified-ads-adservices-java-1.1.0-beta11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\79ad47d2b942c5f590c66bf37b2f29d9\transformed\jetified-ads-adservices-1.1.0-beta11\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\79ad47d2b942c5f590c66bf37b2f29d9\transformed\jetified-ads-adservices-1.1.0-beta11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7a2f90473ee5d6c67044ece09383463b\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7a2f90473ee5d6c67044ece09383463b\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6ed5e86a7a38368c233722104b3707c9\transformed\jetified-firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6ed5e86a7a38368c233722104b3707c9\transformed\jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.5.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6b26f1f52c8f1c8f0d7dde318506ccd9\transformed\jetified-play-services-basement-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.5.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\6b26f1f52c8f1c8f0d7dde318506ccd9\transformed\jetified-play-services-basement-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.15.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7fb15d6ea7a16f55d392deba7ddb5685\transformed\jetified-core-ktx-1.15.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.15.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7fb15d6ea7a16f55d392deba7ddb5685\transformed\jetified-core-ktx-1.15.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\57b84a88d8813fbc198cf8b01eefecd0\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\57b84a88d8813fbc198cf8b01eefecd0\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\add56fc07640e9e919327c11a61ec4c3\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\add56fc07640e9e919327c11a61ec4c3\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.15.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8f32ad1e84eea09473cc783b71dcb560\transformed\core-1.15.0\jars\classes.jar"
      resolved="androidx.core:core:1.15.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8f32ad1e84eea09473cc783b71dcb560\transformed\core-1.15.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\de09a1dbeb9e581b5eb9c08c3800d03f\transformed\jetified-annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\de09a1dbeb9e581b5eb9c08c3800d03f\transformed\jetified-annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b524b25c53bdab712787c6225ebf7af1\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b524b25c53bdab712787c6225ebf7af1\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7d4728e0d5ee4c6e4a2ae914a01f5844\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7d4728e0d5ee4c6e4a2ae914a01f5844\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.2\bc60b5568a66d765a9fe8e266fd0c6c727e0b50b\collection-jvm-1.4.2.jar"
      resolved="androidx.collection:collection-jvm:1.4.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.0\e000bd084353d84c9e888f6fb341dc1f5b79d948\kotlin-stdlib-jdk8-1.9.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\bf4ac374b1a64d04e6e88c0779663b65\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\bf4ac374b1a64d04e6e88c0779663b65\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8f9334b4e16e3dee448f530492e4d01d\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8f9334b4e16e3dee448f530492e4d01d\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\54372216d3e4bf03b4a91de4c6c90d73\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\54372216d3e4bf03b4a91de4c6c90d73\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.0\f320478990d05e0cfaadd74f9619fd6027adbf37\kotlin-stdlib-jdk7-1.9.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.0\85f8b81009cda5890e54ba67d64b5e599c645020\kotlin-stdlib-2.1.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.0"/>
  <library
      name="com.google.guava:guava:32.1.3-android@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.guava\guava\32.1.3-android\ea090dd85ca2fa12d42d054369df888665230dd7\guava-32.1.3-android.jar"
      resolved="com.google.guava:guava:32.1.3-android"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.flutter\armeabi_v7a_release\1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea\12a27c30316a2e88a786277d8f0c664b2eaee337\armeabi_v7a_release-1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.flutter\arm64_v8a_release\1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea\d7d3ca68c2ddadd5079db74fd15b0e46e54548f2\arm64_v8a_release-1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.flutter\x86_64_release\1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea\1f777733d0e92441a1d0fa9af5dc1da37ddecbe3\x86_64_release-1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea.jar"
      resolved="io.flutter:x86_64_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.37.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.37.0\ba74746d38026581c12166e164bb3c15e90cc4ea\checker-qual-3.37.0.jar"
      resolved="org.checkerframework:checker-qual:3.37.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.36.0\227d4d4957ccc3dc5761bd897e3a0ee587e750a7\error_prone_annotations-2.36.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.36.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3573334920de1109ddd4976b5b76459d\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3573334920de1109ddd4976b5b76459d\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\31d8f1595f2f293a9fce28acbc1022a1\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\31d8f1595f2f293a9fce28acbc1022a1\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1cfbb8b28e87b470894a79dc05ea48a8\transformed\jetified-firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1cfbb8b28e87b470894a79dc05ea48a8\transformed\jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\804e7eaa6988a53625212c939a756350\transformed\jetified-relinker-1.4.5\jars\classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\804e7eaa6988a53625212c939a756350\transformed\jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.j2objc:j2objc-annotations:2.8@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\2.8\c85270e307e7b822f1086b93689124b89768e273\j2objc-annotations-2.8.jar"
      resolved="com.google.j2objc:j2objc-annotations:2.8"
      provided="true"/>
  <library
      name="com.google.firebase:firebase-firestore:25.1.3@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ffe1e81574cd6a0f2eba27d21a19b9cc\transformed\jetified-firebase-firestore-25.1.3\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:25.1.3"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ffe1e81574cd6a0f2eba27d21a19b9cc\transformed\jetified-firebase-firestore-25.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.glance:glance:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\29b6630631713273c2fb962a2f85c316\transformed\jetified-glance-1.0.0\jars\classes.jar"
      resolved="androidx.glance:glance:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\29b6630631713273c2fb962a2f85c316\transformed\jetified-glance-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.glance:glance-appwidget:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f6db9cf6b7493ae7224f937fd1f5223b\transformed\jetified-glance-appwidget-1.0.0\jars\classes.jar;D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f6db9cf6b7493ae7224f937fd1f5223b\transformed\jetified-glance-appwidget-1.0.0\jars\libs\repackaged.jar"
      resolved="androidx.glance:glance-appwidget:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f6db9cf6b7493ae7224f937fd1f5223b\transformed\jetified-glance-appwidget-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth:23.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\799d99e76c150c0d18b57ca68234d758\transformed\jetified-firebase-auth-23.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:23.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\799d99e76c150c0d18b57ca68234d758\transformed\jetified-firebase-auth-23.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.2.0-rc01@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c6645153e50362b5ec224b6d9db611ba\transformed\jetified-credentials-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.2.0-rc01"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c6645153e50362b5ec224b6d9db611ba\transformed\jetified-credentials-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\360f520b1fd94e46852e32542f950379\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.2.0-rc01"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\360f520b1fd94e46852e32542f950379\transformed\jetified-credentials-play-services-auth-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\666afd8a3f7bde6316fe8f15ccdf4621\transformed\jetified-play-services-auth-21.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\666afd8a3f7bde6316fe8f15ccdf4621\transformed\jetified-play-services-auth-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.billingclient:billing:7.1.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\030ef254abd294976f59e7a697258de0\transformed\jetified-billing-7.1.1\jars\classes.jar"
      resolved="com.android.billingclient:billing:7.1.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\030ef254abd294976f59e7a697258de0\transformed\jetified-billing-7.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.7.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e46fddab4d610204f3eeab01e64de068\transformed\jetified-recaptcha-18.7.0\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.7.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e46fddab4d610204f3eeab01e64de068\transformed\jetified-recaptcha-18.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\42297badaa41de8b8fd15ea289d65a93\transformed\jetified-firebase-appcheck-interop-17.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\42297badaa41de8b8fd15ea289d65a93\transformed\jetified-firebase-appcheck-interop-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d771cc5faa3660522c315d3debcec2c9\transformed\jetified-firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d771cc5faa3660522c315d3debcec2c9\transformed\jetified-firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1c06066966ce2a33626a446519f9df3f\transformed\jetified-play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1c06066966ce2a33626a446519f9df3f\transformed\jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\bd455ce8fc1b616cf7295553675aabf8\transformed\jetified-play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\bd455ce8fc1b616cf7295553675aabf8\transformed\jetified-play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-location:19.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4460203b92bd63994f2777ac84690f2b\transformed\jetified-play-services-location-19.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-location:19.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4460203b92bd63994f2777ac84690f2b\transformed\jetified-play-services-location-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-recaptchabase:16.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1bc4c398f578bbdf031437a54583ee44\transformed\jetified-play-services-recaptchabase-16.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-recaptchabase:16.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1bc4c398f578bbdf031437a54583ee44\transformed\jetified-play-services-recaptchabase-16.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ac987222f87da4df87deb95cd2cb1ecc\transformed\jetified-play-services-fido-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ac987222f87da4df87deb95cd2cb1ecc\transformed\jetified-play-services-fido-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\77c133aba392f7d6c8cfd3db365f91bd\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\77c133aba392f7d6c8cfd3db365f91bd\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\35a0b14b32946c892dcc20a2d874884d\transformed\appcompat-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\35a0b14b32946c892dcc20a2d874884d\transformed\appcompat-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\cdc0cd0159344d4961f497b0bb7e6643\transformed\jetified-firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\cdc0cd0159344d4961f497b0bb7e6643\transformed\jetified-firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:review:2.0.2@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\02cd79e1cda59617fe19e0320ee40541\transformed\jetified-review-2.0.2\jars\classes.jar"
      resolved="com.google.android.play:review:2.0.2"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\02cd79e1cda59617fe19e0320ee40541\transformed\jetified-review-2.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:integrity:1.4.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3b47a06e333b7bf4fb7cfcd898f714eb\transformed\jetified-integrity-1.4.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.4.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\3b47a06e333b7bf4fb7cfcd898f714eb\transformed\jetified-integrity-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.7.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\0957c3cbd272b30d3d66f3fe61b679f4\transformed\jetified-fragment-ktx-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.7.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\0957c3cbd272b30d3d66f3fe61b679f4\transformed\jetified-fragment-ktx-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.3@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4608948e4a12a544c53284821f817a46\transformed\jetified-activity-ktx-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.3"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4608948e4a12a544c53284821f817a46\transformed\jetified-activity-ktx-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2d4eabbe26fa628f25f941abcf11103b\transformed\recyclerview-1.0.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2d4eabbe26fa628f25f941abcf11103b\transformed\recyclerview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1c02d0c640852c2afa1b70d98535e660\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1c02d0c640852c2afa1b70d98535e660\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b0618da54ffac6cf1e3cf8804a47565a\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b0618da54ffac6cf1e3cf8804a47565a\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.7.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4eeba8173b9197743c7d32514888842d\transformed\work-runtime-ktx-2.7.1\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.7.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4eeba8173b9197743c7d32514888842d\transformed\work-runtime-ktx-2.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.7.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\74716c935175f8d7c73d642438826c4d\transformed\work-runtime-2.7.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.7.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\74716c935175f8d7c73d642438826c4d\transformed\work-runtime-2.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\fa3e1ec43331cf990080aed5ed2639a9\transformed\jetified-lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\fa3e1ec43331cf990080aed5ed2639a9\transformed\jetified-lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\36ca2d4bee497b9c6c60b6388e313dbb\transformed\jetified-lifecycle-service-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\36ca2d4bee497b9c6c60b6388e313dbb\transformed\jetified-lifecycle-service-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d35d73e36eb49bfbe5a0bfbe0ac1bf40\transformed\lifecycle-livedata-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d35d73e36eb49bfbe5a0bfbe0ac1bf40\transformed\lifecycle-livedata-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\31d265a9cb08182ae61a3fd62ad75bcd\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\31d265a9cb08182ae61a3fd62ad75bcd\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\64729a3d820f259c39d32c9987123aa7\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\64729a3d820f259c39d32c9987123aa7\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\893b16d8fc3f3088379de4cb0d4c2abb\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\893b16d8fc3f3088379de4cb0d4c2abb\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics:1.1.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d8aefe25d91a9532c965a7ccfd534069\transformed\jetified-ui-graphics-1.1.1\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics:1.1.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\d8aefe25d91a9532c965a7ccfd534069\transformed\jetified-ui-graphics-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit:1.1.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\aa94781b562961b6685f1264988a34b9\transformed\jetified-ui-unit-1.1.1\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit:1.1.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\aa94781b562961b6685f1264988a34b9\transformed\jetified-ui-unit-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry:1.1.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c7a7693aece3fdb601c3b01a1bc05bd2\transformed\jetified-ui-geometry-1.1.1\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry:1.1.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\c7a7693aece3fdb601c3b01a1bc05bd2\transformed\jetified-ui-geometry-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime:1.2.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a82ffda1bba940459d3a57f57eda9264\transformed\jetified-runtime-1.2.1\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime:1.2.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\a82ffda1bba940459d3a57f57eda9264\transformed\jetified-runtime-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.3\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.3\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.3\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core-jvm\1.1.3\fb991f11389ccf2a5d5d4c99783ff958bc400\datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\030e30a1907e366a200baea4b01013cd\transformed\jetified-datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\030e30a1907e366a200baea4b01013cd\transformed\jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1efaefef079289ce7b2b4a4b5226405b\transformed\jetified-datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1efaefef079289ce7b2b4a4b5226405b\transformed\jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e73aa1fc7407ca263e6057d6cbc09d85\transformed\jetified-datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e73aa1fc7407ca263e6057d6cbc09d85\transformed\jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-places-placereport:17.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b42ce37c402cd1a9256aad5b84ced143\transformed\jetified-play-services-places-placereport-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-places-placereport:17.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b42ce37c402cd1a9256aad5b84ced143\transformed\jetified-play-services-places-placereport-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8c5223efd9a9e23427a2d2ca9d035da8\transformed\media-1.1.0\jars\classes.jar"
      resolved="androidx.media:media:1.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8c5223efd9a9e23427a2d2ca9d035da8\transformed\media-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\521dfdd1b9bd842178ea8da72f2f692e\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\521dfdd1b9bd842178ea8da72f2f692e\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-remoteviews:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\70a96e62ce425a47fa33ce180f3b1283\transformed\jetified-core-remoteviews-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-remoteviews:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\70a96e62ce425a47fa33ce180f3b1283\transformed\jetified-core-remoteviews-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\da91d50bd88cbf164cd30b958b3bd145\transformed\jetified-appcompat-resources-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\da91d50bd88cbf164cd30b958b3bd145\transformed\jetified-appcompat-resources-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\82be86bc17fac92e12ba0fa88525c3bf\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\82be86bc17fac92e12ba0fa88525c3bf\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7dc761a8fa3062437420de41b9616513\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7dc761a8fa3062437420de41b9616513\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.4.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\edc4cb8b23db9b881966656aa09c58e1\transformed\transition-1.4.1\jars\classes.jar"
      resolved="androidx.transition:transition:1.4.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\edc4cb8b23db9b881966656aa09c58e1\transformed\transition-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b3cd440ec2b84a727b44347ee5a1955f\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\b3cd440ec2b84a727b44347ee5a1955f\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1f59dde7277d455c4f2edc0152734493\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1f59dde7277d455c4f2edc0152734493\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ae56d69158a034b195357dc40cae1e99\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ae56d69158a034b195357dc40cae1e99\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ff45f70ec21adc9945c7e527a7337bc3\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ff45f70ec21adc9945c7e527a7337bc3\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\5580a3719458ea6c03dad10d1092da3b\transformed\jetified-googleid-1.1.0\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\5580a3719458ea6c03dad10d1092da3b\transformed\jetified-googleid-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.62.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.62.2\2d8b802e7fe17c1577527195fd53a2e7355b3541\grpc-okhttp-1.62.2.jar"
      resolved="io.grpc:grpc-okhttp:1.62.2"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="androidx.security:security-crypto:1.1.0-alpha06@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7ef3a31730006fcbfc92afea7dad5a82\transformed\jetified-security-crypto-1.1.0-alpha06\jars\classes.jar"
      resolved="androidx.security:security-crypto:1.1.0-alpha06"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7ef3a31730006fcbfc92afea7dad5a82\transformed\jetified-security-crypto-1.1.0-alpha06"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.crypto.tink:tink-android:1.9.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.crypto.tink\tink-android\1.9.0\4c3bb230542a11ad51492cd9913979466b56fa16\tink-android-1.9.0.jar"
      resolved="com.google.crypto.tink:tink-android:1.9.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4197a439d29a9173593ba2e7583dd04d\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4197a439d29a9173593ba2e7583dd04d\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2e3c3513f3b4cff8c591794a468f771b\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\2e3c3513f3b4cff8c591794a468f771b\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e06ceb9d30e49386a18835a942c7874a\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\e06ceb9d30e49386a18835a942c7874a\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e9db547e7e916be65b7f728fb9d59b0\transformed\jetified-transport-backend-cct-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.8"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\7e9db547e7e916be65b7f728fb9d59b0\transformed\jetified-transport-backend-cct-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\10da8340acaa430e167d6de819cc1107\transformed\jetified-transport-runtime-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.8"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\10da8340acaa430e167d6de819cc1107\transformed\jetified-transport-runtime-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:3.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ac0579998c919fbab142055385b74882\transformed\jetified-transport-api-3.0.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\ac0579998c919fbab142055385b74882\transformed\jetified-transport-api-3.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.2\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.2.jar"
      resolved="androidx.collection:collection-ktx:1.4.2"/>
  <library
      name="androidx.room:room-runtime:2.2.5@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4704b4a227b631b38b2328f84b7c657d\transformed\room-runtime-2.2.5\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.2.5"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\4704b4a227b631b38b2328f84b7c657d\transformed\room-runtime-2.2.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\27eddd272d9f31a16d58dd81948225bc\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\27eddd272d9f31a16d58dd81948225bc\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\141dda5f7c9215b12b54622c4e667c99\transformed\jetified-firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\141dda5f7c9215b12b54622c4e667c99\transformed\jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\726410337c62e538323e9bedfb887c75\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\726410337c62e538323e9bedfb887c75\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1e74d73f47278f34f5ab904f6d30e93a\transformed\jetified-core-1.0.0\jars\classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\1e74d73f47278f34f5ab904f6d30e93a\transformed\jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\068f51b375130ff2c9caf71973fda2ea\transformed\sqlite-framework-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\068f51b375130ff2c9caf71973fda2ea\transformed\sqlite-framework-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.1.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\58f2d2224083b92da1b88fca4b912387\transformed\sqlite-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.1.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\58f2d2224083b92da1b88fca4b912387\transformed\sqlite-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.2.5@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\androidx.room\room-common\2.2.5\f5e3b73a0c2ab5e276e26868e4ce3542baede207\room-common-2.2.5.jar"
      resolved="androidx.room:room-common:2.2.5"/>
  <library
      name="androidx.compose.ui:ui-util:1.1.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\73e1e9846ead9f1d68a7f589e5ed810c\transformed\jetified-ui-util-1.1.1\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util:1.1.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\73e1e9846ead9f1d68a7f589e5ed810c\transformed\jetified-ui-util-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="io.grpc:grpc-android:1.62.2@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\58f20bd264509c39afdae1547980b2d7\transformed\jetified-grpc-android-1.62.2\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.62.2"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\58f20bd264509c39afdae1547980b2d7\transformed\jetified-grpc-android-1.62.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.62.2\9d807d2a0e34bd7284a5336186f57cf241090920\grpc-protobuf-lite-1.62.2.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.62.2"/>
  <library
      name="io.grpc:grpc-stub:1.62.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.62.2\fc1e85697502d96d6c912e8dd2a56f46f1aba050\grpc-stub-1.62.2.jar"
      resolved="io.grpc:grpc-stub:1.62.2"/>
  <library
      name="io.grpc:grpc-util:1.62.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.grpc\grpc-util\1.62.2\98c4138f09fb57c3ad6cbeffb31ed73e302038f7\grpc-util-1.62.2.jar"
      resolved="io.grpc:grpc-util:1.62.2"/>
  <library
      name="io.grpc:grpc-core:1.62.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.grpc\grpc-core\1.62.2\5808049a5e33eba6f248a68d58e75399a68f2784\grpc-core-1.62.2.jar"
      resolved="io.grpc:grpc-core:1.62.2"/>
  <library
      name="io.grpc:grpc-context:1.62.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.grpc\grpc-context\1.62.2\69e86c35140b3b1718d65635bb54ccecc4c12f14\grpc-context-1.62.2.jar"
      resolved="io.grpc:grpc-context:1.62.2"/>
  <library
      name="io.grpc:grpc-api:1.62.2@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.grpc\grpc-api\1.62.2\a93b6ee3761d48edd9a9279f20a58be1a245ad01\grpc-api-1.62.2.jar"
      resolved="io.grpc:grpc-api:1.62.2"/>
  <library
      name="com.google.code.gson:gson:2.12.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.code.gson\gson\2.12.0\10596b68aaca6230f7c40bfd9298b21ff4b84103\gson-2.12.0.jar"
      resolved="com.google.code.gson:gson:2.12.0"/>
  <library
      name="org.microg:safe-parcel:1.7.0@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f50d69ba020c4fd132da6fe551fe1fb9\transformed\jetified-safe-parcel-1.7.0\jars\classes.jar"
      resolved="org.microg:safe-parcel:1.7.0"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\f50d69ba020c4fd132da6fe551fe1fb9\transformed\jetified-safe-parcel-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.apache.tika:tika-core:3.1.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.apache.tika\tika-core\3.1.0\6ba44a9ddf8f6f2b4bc88e8bc64269aea0424607\tika-core-3.1.0.jar"
      resolved="org.apache.tika:tika-core:3.1.0"/>
  <library
      name="com.github.albfernandez:juniversalchardet:2.5.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.github.albfernandez\juniversalchardet\2.5.0\423123a1ddfe458d07948bc09cfa0170037a9e3d\juniversalchardet-2.5.0.jar"
      resolved="com.github.albfernandez:juniversalchardet:2.5.0"/>
  <library
      name="eu.simonbinder:sqlite3-native-library:3.49.1+1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9d4d582f9eee40bfe5c0e417698029bf\transformed\jetified-sqlite3-native-library-3.49.1+1\jars\classes.jar"
      resolved="eu.simonbinder:sqlite3-native-library:3.49.1+1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\9d4d582f9eee40bfe5c0e417698029bf\transformed\jetified-sqlite3-native-library-3.49.1+1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.1@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8fa616a7c56377e678718ea697555cd6\transformed\jetified-protolite-well-known-types-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.1"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\8fa616a7c56377e678718ea697555cd6\transformed\jetified-protolite-well-known-types-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.slf4j:slf4j-api:2.0.16@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.16\172931663a09a1fa515567af5fbef00897d3c04\slf4j-api-2.0.16.jar"
      resolved="org.slf4j:slf4j-api:2.0.16"/>
  <library
      name="commons-io:commons-io:2.18.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\commons-io\commons-io\2.18.0\44084ef756763795b31c578403dd028ff4a22950\commons-io-2.18.0.jar"
      resolved="commons-io:commons-io:2.18.0"/>
  <library
      name="com.google.android.play:core-common:2.0.4@aar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\fdce7be03ad998e8d61782ec853f71ae\transformed\jetified-core-common-2.0.4\jars\classes.jar"
      resolved="com.google.android.play:core-common:2.0.4"
      folder="D:\App\Android Studio\gradle\gradleCache\caches\8.10.2\transforms\fdce7be03ad998e8d61782ec853f71ae\transformed\jetified-core-common-2.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.25.5@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.25.5\272641fe157ed7c4a22f8d4c347bcd7f6eac8887\protobuf-javalite-3.25.5.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.25.5"/>
  <library
      name="io.perfmark:perfmark-api:0.26.0@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\io.perfmark\perfmark-api\0.26.0\ef65452adaf20bf7d12ef55913aba24037b82738\perfmark-api-0.26.0.jar"
      resolved="io.perfmark:perfmark-api:0.26.0"/>
  <library
      name="com.google.android:annotations:*******@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\com.google.android\annotations\*******\a1678ba907bf92691d879fef34e1a187038f9259\annotations-*******.jar"
      resolved="com.google.android:annotations:*******"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
      jars="D:\App\Android Studio\gradle\gradleCache\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.23\3c0daebd5f0e1ce72cc50c818321ac957aeb5d70\animal-sniffer-annotations-1.23.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.23"/>
</libraries>
