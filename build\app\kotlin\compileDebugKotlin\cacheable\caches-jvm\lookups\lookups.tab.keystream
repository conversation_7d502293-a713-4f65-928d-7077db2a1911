  
PendingIntent android.app  AppWidgetManager android.appwidget  updateAppWidget "android.appwidget.AppWidgetManager  	Exception #android.appwidget.AppWidgetProvider  HomeWidgetLaunchIntent #android.appwidget.AppWidgetProvider  Integer #android.appwidget.AppWidgetProvider  MainActivity #android.appwidget.AppWidgetProvider  R #android.appwidget.AppWidgetProvider  RemoteViews #android.appwidget.AppWidgetProvider  Uri #android.appwidget.AppWidgetProvider  android #android.appwidget.AppWidgetProvider  apply #android.appwidget.AppWidgetProvider  forEach #android.appwidget.AppWidgetProvider  getActivity #android.appwidget.AppWidgetProvider  java #android.appwidget.AppWidgetProvider  Context android.content  SharedPreferences android.content  	Exception !android.content.BroadcastReceiver  HomeWidgetLaunchIntent !android.content.BroadcastReceiver  Integer !android.content.BroadcastReceiver  MainActivity !android.content.BroadcastReceiver  R !android.content.BroadcastReceiver  RemoteViews !android.content.BroadcastReceiver  Uri !android.content.BroadcastReceiver  android !android.content.BroadcastReceiver  apply !android.content.BroadcastReceiver  forEach !android.content.BroadcastReceiver  getActivity !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  packageName android.content.Context  	getString !android.content.SharedPreferences  
BitmapFactory android.graphics  Color android.graphics  
parseColor android.graphics.Color  GradientDrawable android.graphics.drawable  Uri android.net  parse android.net.Uri  View android.view  RemoteViews android.widget  HomeWidgetLaunchIntent android.widget.RemoteViews  Integer android.widget.RemoteViews  MainActivity android.widget.RemoteViews  R android.widget.RemoteViews  Uri android.widget.RemoteViews  android android.widget.RemoteViews  apply android.widget.RemoteViews  getActivity android.widget.RemoteViews  java android.widget.RemoteViews  setInt android.widget.RemoteViews  setOnClickPendingIntent android.widget.RemoteViews  setTextViewText android.widget.RemoteViews  AppWidgetManager com.saini.budget  Context com.saini.budget  	Exception com.saini.budget  FlutterFragmentActivity com.saini.budget  HomeWidgetLaunchIntent com.saini.budget  HomeWidgetProvider com.saini.budget  IntArray com.saini.budget  Integer com.saini.budget  MainActivity com.saini.budget  NetWorthPlusWidgetProvider com.saini.budget  NetWorthWidgetProvider com.saini.budget  PlusWidgetProvider com.saini.budget  R com.saini.budget  RemoteViews com.saini.budget  SharedPreferences com.saini.budget  TransferWidgetProvider com.saini.budget  Uri com.saini.budget  android com.saini.budget  apply com.saini.budget  forEach com.saini.budget  getActivity com.saini.budget  java com.saini.budget  HomeWidgetLaunchIntent +com.saini.budget.NetWorthPlusWidgetProvider  Integer +com.saini.budget.NetWorthPlusWidgetProvider  MainActivity +com.saini.budget.NetWorthPlusWidgetProvider  R +com.saini.budget.NetWorthPlusWidgetProvider  RemoteViews +com.saini.budget.NetWorthPlusWidgetProvider  Uri +com.saini.budget.NetWorthPlusWidgetProvider  android +com.saini.budget.NetWorthPlusWidgetProvider  apply +com.saini.budget.NetWorthPlusWidgetProvider  forEach +com.saini.budget.NetWorthPlusWidgetProvider  getActivity +com.saini.budget.NetWorthPlusWidgetProvider  java +com.saini.budget.NetWorthPlusWidgetProvider  HomeWidgetLaunchIntent 'com.saini.budget.NetWorthWidgetProvider  Integer 'com.saini.budget.NetWorthWidgetProvider  MainActivity 'com.saini.budget.NetWorthWidgetProvider  R 'com.saini.budget.NetWorthWidgetProvider  RemoteViews 'com.saini.budget.NetWorthWidgetProvider  Uri 'com.saini.budget.NetWorthWidgetProvider  android 'com.saini.budget.NetWorthWidgetProvider  apply 'com.saini.budget.NetWorthWidgetProvider  forEach 'com.saini.budget.NetWorthWidgetProvider  getActivity 'com.saini.budget.NetWorthWidgetProvider  java 'com.saini.budget.NetWorthWidgetProvider  HomeWidgetLaunchIntent #com.saini.budget.PlusWidgetProvider  Integer #com.saini.budget.PlusWidgetProvider  MainActivity #com.saini.budget.PlusWidgetProvider  R #com.saini.budget.PlusWidgetProvider  RemoteViews #com.saini.budget.PlusWidgetProvider  Uri #com.saini.budget.PlusWidgetProvider  android #com.saini.budget.PlusWidgetProvider  apply #com.saini.budget.PlusWidgetProvider  forEach #com.saini.budget.PlusWidgetProvider  getActivity #com.saini.budget.PlusWidgetProvider  java #com.saini.budget.PlusWidgetProvider  net_worth_amount com.saini.budget.R.id  net_worth_title com.saini.budget.R.id  net_worth_transactions_number com.saini.budget.R.id  plus_button com.saini.budget.R.id  
plus_image com.saini.budget.R.id  transfer_image com.saini.budget.R.id  widget_background com.saini.budget.R.id  widget_container com.saini.budget.R.id  net_worth_plus_widget_layout com.saini.budget.R.layout  net_worth_widget_layout com.saini.budget.R.layout  plus_widget_layout com.saini.budget.R.layout  transfer_widget_layout com.saini.budget.R.layout  HomeWidgetLaunchIntent 'com.saini.budget.TransferWidgetProvider  Integer 'com.saini.budget.TransferWidgetProvider  MainActivity 'com.saini.budget.TransferWidgetProvider  R 'com.saini.budget.TransferWidgetProvider  RemoteViews 'com.saini.budget.TransferWidgetProvider  Uri 'com.saini.budget.TransferWidgetProvider  android 'com.saini.budget.TransferWidgetProvider  apply 'com.saini.budget.TransferWidgetProvider  forEach 'com.saini.budget.TransferWidgetProvider  getActivity 'com.saini.budget.TransferWidgetProvider  java 'com.saini.budget.TransferWidgetProvider  HomeWidgetBackgroundIntent es.antonborri.home_widget  HomeWidgetLaunchIntent es.antonborri.home_widget  HomeWidgetProvider es.antonborri.home_widget  getActivity 0es.antonborri.home_widget.HomeWidgetLaunchIntent  FlutterFragmentActivity io.flutter.embedding.android  Class 	java.lang  	Exception 	java.lang  parseInt java.lang.Integer  	Function1 kotlin  IntArray kotlin  apply kotlin  forEach kotlin.IntArray  forEach kotlin.collections  java 
kotlin.jvm  java kotlin.reflect.KClass  forEach kotlin.sequences  forEach kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                