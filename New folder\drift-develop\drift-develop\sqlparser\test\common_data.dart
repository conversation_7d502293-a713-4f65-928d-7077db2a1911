const createTableStmt = '''
CREATE TABLE IF NOT EXISTS users (
  id INT NOT NULL PRIMARY KEY DESC ON CONFLICT R<PERSON><PERSON><PERSON><PERSON><PERSON> AUTOINCREMENT,
  email VARCHAR NOT NULL UNIQUE ON CONFLICT ABORT,
  score INT CONSTRAINT "score set" NOT NULL DEFAULT 420 CHECK (score > 0),
  display_name VARCHAR NULL COLLATE BINARY 
     REFERENCES some(thing) ON UPDATE CASCADE ON DELETE SET NULL
     DEFERRABLE INITIALLY DEFERRED,
  
  UNIQUE (score, display_name) ON CONFLICT ABORT,
  FOREIGN KEY (id, email) REFERENCES another (a, b)
     ON DELETE NO ACTION ON UPDATE RESTRICT
     NOT DEFERRABLE INITIALLY IMMEDIATE
)
''';
