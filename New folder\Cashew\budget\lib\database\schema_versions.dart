import 'package:drift/internal/versioned_schema.dart' as i0;
import 'package:drift/drift.dart' as i1;
import 'package:drift/drift.dart'; // ignore_for_file: type=lint,unused_import

// GENERATED BY drift_dev, DO NOT MODIFY.
final class Schema34 extends i0.VersionedSchema {
  Schema34({required super.database}) : super(version: 34);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape0 wallets = Shape0(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_0,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape1 categories = Shape1(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_9,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape2 transactions = Shape2(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_12,
          _column_1,
          _column_13,
          _column_14,
          _column_15,
          _column_16,
          _column_4,
          _column_17,
          _column_5,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_31,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape3 budgets = Shape3(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_32,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_16,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape4 categoryBudgetLimits = Shape4(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_45,
          _column_15,
          _column_46,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape5 associatedTitles = Shape5(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_47,
          _column_48,
          _column_15,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape7 scannerTemplates = Shape7(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_53,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_60,
          _column_16,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape8 deleteLogs = Shape8(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_62,
          _column_63,
          _column_64,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape0 extends i0.VersionedTable {
  Shape0({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get walletPk =>
      columnsByName['wallet_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get currency =>
      columnsByName['currency']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get decimals =>
      columnsByName['decimals']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<int> _column_0(String aliasedName) =>
    i1.GeneratedColumn<int>('wallet_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<String> _column_1(String aliasedName) =>
    i1.GeneratedColumn<String>('name', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_2(String aliasedName) =>
    i1.GeneratedColumn<String>('colour', aliasedName, true,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_3(String aliasedName) =>
    i1.GeneratedColumn<String>('icon_name', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<DateTime> _column_4(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('date_created', aliasedName, false,
        type: i1.DriftSqlType.dateTime);
i1.GeneratedColumn<DateTime> _column_5(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('date_time_modified', aliasedName, true,
        type: i1.DriftSqlType.dateTime, defaultValue: Constant(DateTime.now()));
i1.GeneratedColumn<int> _column_6(String aliasedName) =>
    i1.GeneratedColumn<int>('order', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<String> _column_7(String aliasedName) =>
    i1.GeneratedColumn<String>('currency', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_8(String aliasedName) =>
    i1.GeneratedColumn<int>('decimals', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: Constant(2));

class Shape1 extends i0.VersionedTable {
  Shape1({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get categoryPk =>
      columnsByName['category_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<int> _column_9(String aliasedName) =>
    i1.GeneratedColumn<int>('category_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<bool> _column_10(String aliasedName) =>
    i1.GeneratedColumn<bool>('income', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('CHECK ("income" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<int> _column_11(String aliasedName) =>
    i1.GeneratedColumn<int>('method_added', aliasedName, true,
        type: i1.DriftSqlType.int);

class Shape2 extends i0.VersionedTable {
  Shape2({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeCreated =>
      columnsByName['date_time_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<int> _column_12(String aliasedName) =>
    i1.GeneratedColumn<int>('transaction_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<double> _column_13(String aliasedName) =>
    i1.GeneratedColumn<double>('amount', aliasedName, false,
        type: i1.DriftSqlType.double);
i1.GeneratedColumn<String> _column_14(String aliasedName) =>
    i1.GeneratedColumn<String>('note', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_15(String aliasedName) =>
    i1.GeneratedColumn<int>('category_fk', aliasedName, false,
        type: i1.DriftSqlType.int,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES categories (category_pk)'));
i1.GeneratedColumn<int> _column_16(String aliasedName) =>
    i1.GeneratedColumn<int>('wallet_fk', aliasedName, false,
        type: i1.DriftSqlType.int,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES wallets (wallet_pk)'));
i1.GeneratedColumn<DateTime> _column_17(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('date_time_created', aliasedName, true,
        type: i1.DriftSqlType.dateTime, defaultValue: Constant(DateTime.now()));
i1.GeneratedColumn<int> _column_18(String aliasedName) =>
    i1.GeneratedColumn<int>('period_length', aliasedName, true,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<int> _column_19(String aliasedName) =>
    i1.GeneratedColumn<int>('reoccurrence', aliasedName, true,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<bool> _column_20(
        String aliasedName) =>
    i1.GeneratedColumn<bool>(
        'upcoming_transaction_notification', aliasedName, true,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("upcoming_transaction_notification" IN (0, 1))'),
        defaultValue: const Constant(true));
i1.GeneratedColumn<int> _column_21(String aliasedName) =>
    i1.GeneratedColumn<int>('type', aliasedName, true,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<bool> _column_22(String aliasedName) =>
    i1.GeneratedColumn<bool>('paid', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('CHECK ("paid" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<bool> _column_23(
        String aliasedName) =>
    i1.GeneratedColumn<bool>(
        'created_another_future_transaction', aliasedName, true,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("created_another_future_transaction" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<bool> _column_24(String aliasedName) =>
    i1.GeneratedColumn<bool>('skip_paid', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("skip_paid" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<String> _column_25(String aliasedName) =>
    i1.GeneratedColumn<String>('transaction_owner_email', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_26(String aliasedName) =>
    i1.GeneratedColumn<String>(
        'transaction_original_owner_email', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_27(String aliasedName) =>
    i1.GeneratedColumn<String>('shared_key', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_28(String aliasedName) =>
    i1.GeneratedColumn<String>('shared_old_key', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_29(String aliasedName) =>
    i1.GeneratedColumn<int>('shared_status', aliasedName, true,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<DateTime> _column_30(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('shared_date_updated', aliasedName, true,
        type: i1.DriftSqlType.dateTime);
i1.GeneratedColumn<int> _column_31(String aliasedName) =>
    i1.GeneratedColumn<int>('shared_reference_budget_pk', aliasedName, true,
        type: i1.DriftSqlType.int);

class Shape3 extends i0.VersionedTable {
  Shape3({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get budgetPk =>
      columnsByName['budget_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get startDate =>
      columnsByName['start_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get categoryFks =>
      columnsByName['category_fks']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get allCategoryFks =>
      columnsByName['all_category_fks']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get addedTransactionsOnly =>
      columnsByName['added_transactions_only']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get budgetTransactionFilters =>
      columnsByName['budget_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get memberTransactionFilters =>
      columnsByName['member_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedOwnerMember =>
      columnsByName['shared_owner_member']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedMembers =>
      columnsByName['shared_members']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedAllMembersEver =>
      columnsByName['shared_all_members_ever']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<int> _column_32(String aliasedName) =>
    i1.GeneratedColumn<int>('budget_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<DateTime> _column_33(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('start_date', aliasedName, false,
        type: i1.DriftSqlType.dateTime);
i1.GeneratedColumn<DateTime> _column_34(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('end_date', aliasedName, false,
        type: i1.DriftSqlType.dateTime);
i1.GeneratedColumn<String> _column_35(String aliasedName) =>
    i1.GeneratedColumn<String>('category_fks', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<bool> _column_36(String aliasedName) =>
    i1.GeneratedColumn<bool>('all_category_fks', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("all_category_fks" IN (0, 1))'));
i1.GeneratedColumn<bool> _column_37(String aliasedName) =>
    i1.GeneratedColumn<bool>('added_transactions_only', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("added_transactions_only" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<int> _column_38(String aliasedName) =>
    i1.GeneratedColumn<int>('period_length', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<bool> _column_39(String aliasedName) =>
    i1.GeneratedColumn<bool>('pinned', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('CHECK ("pinned" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<String> _column_40(String aliasedName) =>
    i1.GeneratedColumn<String>('budget_transaction_filters', aliasedName, true,
        type: i1.DriftSqlType.string, defaultValue: const Constant(null));
i1.GeneratedColumn<String> _column_41(String aliasedName) =>
    i1.GeneratedColumn<String>('member_transaction_filters', aliasedName, true,
        type: i1.DriftSqlType.string, defaultValue: const Constant(null));
i1.GeneratedColumn<int> _column_42(String aliasedName) =>
    i1.GeneratedColumn<int>('shared_owner_member', aliasedName, true,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<String> _column_43(String aliasedName) =>
    i1.GeneratedColumn<String>('shared_members', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_44(String aliasedName) =>
    i1.GeneratedColumn<String>('shared_all_members_ever', aliasedName, true,
        type: i1.DriftSqlType.string);

class Shape4 extends i0.VersionedTable {
  Shape4({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get categoryLimitPk =>
      columnsByName['category_limit_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get budgetFk =>
      columnsByName['budget_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
}

i1.GeneratedColumn<int> _column_45(String aliasedName) =>
    i1.GeneratedColumn<int>('category_limit_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<int> _column_46(String aliasedName) =>
    i1.GeneratedColumn<int>('budget_fk', aliasedName, false,
        type: i1.DriftSqlType.int,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES budgets (budget_pk)'));

class Shape5 extends i0.VersionedTable {
  Shape5({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get associatedTitlePk =>
      columnsByName['associated_title_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get title =>
      columnsByName['title']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isExactMatch =>
      columnsByName['is_exact_match']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<int> _column_47(String aliasedName) =>
    i1.GeneratedColumn<int>('associated_title_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<String> _column_48(String aliasedName) =>
    i1.GeneratedColumn<String>('title', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<bool> _column_49(String aliasedName) =>
    i1.GeneratedColumn<bool>('is_exact_match', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("is_exact_match" IN (0, 1))'),
        defaultValue: const Constant(false));

class Shape6 extends i0.VersionedTable {
  Shape6({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get settingsPk =>
      columnsByName['settings_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get settingsJSON =>
      columnsByName['settings_j_s_o_n']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateUpdated =>
      columnsByName['date_updated']! as i1.GeneratedColumn<DateTime>;
}

i1.GeneratedColumn<int> _column_50(String aliasedName) =>
    i1.GeneratedColumn<int>('settings_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<String> _column_51(String aliasedName) =>
    i1.GeneratedColumn<String>('settings_j_s_o_n', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<DateTime> _column_52(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('date_updated', aliasedName, false,
        type: i1.DriftSqlType.dateTime);

class Shape7 extends i0.VersionedTable {
  Shape7({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get scannerTemplatePk =>
      columnsByName['scanner_template_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get templateName =>
      columnsByName['template_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get contains =>
      columnsByName['contains']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get titleTransactionBefore =>
      columnsByName['title_transaction_before']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get titleTransactionAfter =>
      columnsByName['title_transaction_after']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get amountTransactionBefore =>
      columnsByName['amount_transaction_before']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get amountTransactionAfter =>
      columnsByName['amount_transaction_after']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get defaultCategoryFk =>
      columnsByName['default_category_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get ignore =>
      columnsByName['ignore']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<int> _column_53(String aliasedName) =>
    i1.GeneratedColumn<int>('scanner_template_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<String> _column_54(String aliasedName) =>
    i1.GeneratedColumn<String>('template_name', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_55(String aliasedName) =>
    i1.GeneratedColumn<String>('contains', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_56(String aliasedName) =>
    i1.GeneratedColumn<String>('title_transaction_before', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_57(String aliasedName) =>
    i1.GeneratedColumn<String>('title_transaction_after', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_58(String aliasedName) =>
    i1.GeneratedColumn<String>('amount_transaction_before', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_59(String aliasedName) =>
    i1.GeneratedColumn<String>('amount_transaction_after', aliasedName, false,
        additionalChecks: i1.GeneratedColumn.checkTextLength(),
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_60(String aliasedName) =>
    i1.GeneratedColumn<int>('default_category_fk', aliasedName, false,
        type: i1.DriftSqlType.int,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES categories (category_pk)'));
i1.GeneratedColumn<bool> _column_61(String aliasedName) =>
    i1.GeneratedColumn<bool>('ignore', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('CHECK ("ignore" IN (0, 1))'),
        defaultValue: const Constant(false));

class Shape8 extends i0.VersionedTable {
  Shape8({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get deleteLogPk =>
      columnsByName['delete_log_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get entryPk =>
      columnsByName['entry_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
}

i1.GeneratedColumn<int> _column_62(String aliasedName) =>
    i1.GeneratedColumn<int>('delete_log_pk', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<int> _column_63(String aliasedName) =>
    i1.GeneratedColumn<int>('type', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<int> _column_64(String aliasedName) =>
    i1.GeneratedColumn<int>('entry_pk', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<DateTime> _column_65(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('date_time_modified', aliasedName, false,
        type: i1.DriftSqlType.dateTime, defaultValue: Constant(DateTime.now()));

final class Schema35 extends i0.VersionedSchema {
  Schema35({required super.database}) : super(version: 35);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape0 wallets = Shape0(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_0,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape1 categories = Shape1(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_9,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape2 transactions = Shape2(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_12,
          _column_1,
          _column_13,
          _column_14,
          _column_15,
          _column_16,
          _column_4,
          _column_17,
          _column_5,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_31,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape9 budgets = Shape9(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_32,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_16,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape4 categoryBudgetLimits = Shape4(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_45,
          _column_15,
          _column_46,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape5 associatedTitles = Shape5(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_47,
          _column_48,
          _column_15,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape7 scannerTemplates = Shape7(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_53,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_60,
          _column_16,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape8 deleteLogs = Shape8(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_62,
          _column_63,
          _column_64,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape9 extends i0.VersionedTable {
  Shape9({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get budgetPk =>
      columnsByName['budget_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get startDate =>
      columnsByName['start_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get categoryFks =>
      columnsByName['category_fks']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get allCategoryFks =>
      columnsByName['all_category_fks']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get addedTransactionsOnly =>
      columnsByName['added_transactions_only']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get budgetTransactionFilters =>
      columnsByName['budget_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get memberTransactionFilters =>
      columnsByName['member_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedOwnerMember =>
      columnsByName['shared_owner_member']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedMembers =>
      columnsByName['shared_members']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedAllMembersEver =>
      columnsByName['shared_all_members_ever']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get isAbsoluteSpendingLimit =>
      columnsByName['is_absolute_spending_limit']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<bool> _column_66(String aliasedName) =>
    i1.GeneratedColumn<bool>('is_absolute_spending_limit', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("is_absolute_spending_limit" IN (0, 1))'),
        defaultValue: const Constant(false));

final class Schema36 extends i0.VersionedSchema {
  Schema36({required super.database}) : super(version: 36);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape0 wallets = Shape0(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_0,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape1 categories = Shape1(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_9,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape10 transactions = Shape10(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_12,
          _column_1,
          _column_13,
          _column_14,
          _column_15,
          _column_16,
          _column_4,
          _column_5,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_31,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape9 budgets = Shape9(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_32,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_16,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape4 categoryBudgetLimits = Shape4(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_45,
          _column_15,
          _column_46,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape5 associatedTitles = Shape5(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_47,
          _column_48,
          _column_15,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape7 scannerTemplates = Shape7(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_53,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_60,
          _column_16,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape8 deleteLogs = Shape8(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_62,
          _column_63,
          _column_64,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape10 extends i0.VersionedTable {
  Shape10({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']! as i1.GeneratedColumn<int>;
}

final class Schema37 extends i0.VersionedSchema {
  Schema37({required super.database}) : super(version: 37);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape11 wallets = Shape11(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape12 categories = Shape12(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape13 transactions = Shape13(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_71,
          _column_4,
          _column_5,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape14 budgets = Shape14(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_71,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape15 categoryBudgetLimits = Shape15(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_71,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape11 extends i0.VersionedTable {
  Shape11({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get walletPk =>
      columnsByName['wallet_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get currency =>
      columnsByName['currency']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get decimals =>
      columnsByName['decimals']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<String> _column_67(String aliasedName) =>
    i1.GeneratedColumn<String>('wallet_pk', aliasedName, false,
        type: i1.DriftSqlType.string);

class Shape12 extends i0.VersionedTable {
  Shape12({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get categoryPk =>
      columnsByName['category_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<String> _column_68(String aliasedName) =>
    i1.GeneratedColumn<String>('category_pk', aliasedName, false,
        type: i1.DriftSqlType.string);

class Shape13 extends i0.VersionedTable {
  Shape13({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']!
          as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_69(String aliasedName) =>
    i1.GeneratedColumn<String>('transaction_pk', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_70(String aliasedName) =>
    i1.GeneratedColumn<String>('category_fk', aliasedName, false,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES categories (category_pk)'));
i1.GeneratedColumn<String> _column_71(String aliasedName) =>
    i1.GeneratedColumn<String>('wallet_fk', aliasedName, false,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES wallets (wallet_pk)'));
i1.GeneratedColumn<String> _column_72(String aliasedName) =>
    i1.GeneratedColumn<String>('shared_reference_budget_pk', aliasedName, true,
        type: i1.DriftSqlType.string);

class Shape14 extends i0.VersionedTable {
  Shape14({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get budgetPk =>
      columnsByName['budget_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get startDate =>
      columnsByName['start_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get categoryFks =>
      columnsByName['category_fks']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get allCategoryFks =>
      columnsByName['all_category_fks']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get addedTransactionsOnly =>
      columnsByName['added_transactions_only']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetTransactionFilters =>
      columnsByName['budget_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get memberTransactionFilters =>
      columnsByName['member_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedOwnerMember =>
      columnsByName['shared_owner_member']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedMembers =>
      columnsByName['shared_members']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedAllMembersEver =>
      columnsByName['shared_all_members_ever']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get isAbsoluteSpendingLimit =>
      columnsByName['is_absolute_spending_limit']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<String> _column_73(String aliasedName) =>
    i1.GeneratedColumn<String>('budget_pk', aliasedName, false,
        type: i1.DriftSqlType.string);

class Shape15 extends i0.VersionedTable {
  Shape15({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get categoryLimitPk =>
      columnsByName['category_limit_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetFk =>
      columnsByName['budget_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
}

i1.GeneratedColumn<String> _column_74(String aliasedName) =>
    i1.GeneratedColumn<String>('category_limit_pk', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_75(String aliasedName) =>
    i1.GeneratedColumn<String>('budget_fk', aliasedName, false,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES budgets (budget_pk)'));

class Shape16 extends i0.VersionedTable {
  Shape16({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get associatedTitlePk =>
      columnsByName['associated_title_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get title =>
      columnsByName['title']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isExactMatch =>
      columnsByName['is_exact_match']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<String> _column_76(String aliasedName) =>
    i1.GeneratedColumn<String>('associated_title_pk', aliasedName, false,
        type: i1.DriftSqlType.string);

class Shape17 extends i0.VersionedTable {
  Shape17({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get scannerTemplatePk =>
      columnsByName['scanner_template_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get templateName =>
      columnsByName['template_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get contains =>
      columnsByName['contains']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get titleTransactionBefore =>
      columnsByName['title_transaction_before']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get titleTransactionAfter =>
      columnsByName['title_transaction_after']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get amountTransactionBefore =>
      columnsByName['amount_transaction_before']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get amountTransactionAfter =>
      columnsByName['amount_transaction_after']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get defaultCategoryFk =>
      columnsByName['default_category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get ignore =>
      columnsByName['ignore']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<String> _column_77(String aliasedName) =>
    i1.GeneratedColumn<String>('scanner_template_pk', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_78(String aliasedName) =>
    i1.GeneratedColumn<String>('default_category_fk', aliasedName, false,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES categories (category_pk)'));

class Shape18 extends i0.VersionedTable {
  Shape18({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get deleteLogPk =>
      columnsByName['delete_log_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get entryPk =>
      columnsByName['entry_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
}

i1.GeneratedColumn<String> _column_79(String aliasedName) =>
    i1.GeneratedColumn<String>('delete_log_pk', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_80(String aliasedName) =>
    i1.GeneratedColumn<String>('entry_pk', aliasedName, false,
        type: i1.DriftSqlType.string);

final class Schema38 extends i0.VersionedSchema {
  Schema38({required super.database}) : super(version: 38);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape11 wallets = Shape11(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape12 categories = Shape12(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape19 transactions = Shape19(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_71,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape14 budgets = Shape14(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_71,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape15 categoryBudgetLimits = Shape15(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_71,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape19 extends i0.VersionedTable {
  Shape19({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get originalDateDue =>
      columnsByName['original_date_due']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']!
          as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<DateTime> _column_81(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('original_date_due', aliasedName, true,
        type: i1.DriftSqlType.dateTime, defaultValue: Constant(DateTime.now()));

final class Schema39 extends i0.VersionedSchema {
  Schema39({required super.database}) : super(version: 39);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape11 wallets = Shape11(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape20 categories = Shape20(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_82,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape19 transactions = Shape19(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_71,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape14 budgets = Shape14(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_71,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape15 categoryBudgetLimits = Shape15(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_71,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape20 extends i0.VersionedTable {
  Shape20({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get categoryPk =>
      columnsByName['category_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get emojiIconName =>
      columnsByName['emoji_icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<String> _column_82(String aliasedName) =>
    i1.GeneratedColumn<String>('emoji_icon_name', aliasedName, true,
        type: i1.DriftSqlType.string);

final class Schema40 extends i0.VersionedSchema {
  Schema40({required super.database}) : super(version: 40);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    objectives,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape11 wallets = Shape11(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape20 categories = Shape20(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_82,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape21 objectives = Shape21(
      source: i0.VersionedTable(
        entityName: 'objectives',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(objective_pk)',
        ],
        columns: [
          _column_83,
          _column_1,
          _column_13,
          _column_6,
          _column_2,
          _column_4,
          _column_5,
          _column_3,
          _column_82,
          _column_10,
          _column_84,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape22 transactions = Shape22(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_71,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
          _column_85,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape14 budgets = Shape14(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_71,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape15 categoryBudgetLimits = Shape15(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_71,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape21 extends i0.VersionedTable {
  Shape21({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get objectivePk =>
      columnsByName['objective_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get emojiIconName =>
      columnsByName['emoji_icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<String> _column_83(String aliasedName) =>
    i1.GeneratedColumn<String>('objective_pk', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<bool> _column_84(String aliasedName) =>
    i1.GeneratedColumn<bool>('pinned', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('CHECK ("pinned" IN (0, 1))'),
        defaultValue: const Constant(true));

class Shape22 extends i0.VersionedTable {
  Shape22({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get originalDateDue =>
      columnsByName['original_date_due']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get objectiveFk =>
      columnsByName['objective_fk']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_85(String aliasedName) =>
    i1.GeneratedColumn<String>('objective_fk', aliasedName, true,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES objectives (objective_pk)'));

final class Schema41 extends i0.VersionedSchema {
  Schema41({required super.database}) : super(version: 41);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    objectives,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape11 wallets = Shape11(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape20 categories = Shape20(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_82,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape21 objectives = Shape21(
      source: i0.VersionedTable(
        entityName: 'objectives',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(objective_pk)',
        ],
        columns: [
          _column_83,
          _column_1,
          _column_13,
          _column_6,
          _column_2,
          _column_4,
          _column_5,
          _column_3,
          _column_82,
          _column_10,
          _column_84,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape22 transactions = Shape22(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_71,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
          _column_85,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape23 budgets = Shape23(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_86,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_71,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape15 categoryBudgetLimits = Shape15(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_71,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape23 extends i0.VersionedTable {
  Shape23({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get budgetPk =>
      columnsByName['budget_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get startDate =>
      columnsByName['start_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get categoryFks =>
      columnsByName['category_fks']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFksExclude =>
      columnsByName['category_fks_exclude']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get addedTransactionsOnly =>
      columnsByName['added_transactions_only']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetTransactionFilters =>
      columnsByName['budget_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get memberTransactionFilters =>
      columnsByName['member_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedOwnerMember =>
      columnsByName['shared_owner_member']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedMembers =>
      columnsByName['shared_members']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedAllMembersEver =>
      columnsByName['shared_all_members_ever']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get isAbsoluteSpendingLimit =>
      columnsByName['is_absolute_spending_limit']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<String> _column_86(String aliasedName) =>
    i1.GeneratedColumn<String>('category_fks_exclude', aliasedName, true,
        type: i1.DriftSqlType.string);

final class Schema42 extends i0.VersionedSchema {
  Schema42({required super.database}) : super(version: 42);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    objectives,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape24 wallets = Shape24(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
          _column_87,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape25 categories = Shape25(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_82,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
          _column_88,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape21 objectives = Shape21(
      source: i0.VersionedTable(
        entityName: 'objectives',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(objective_pk)',
        ],
        columns: [
          _column_83,
          _column_1,
          _column_13,
          _column_6,
          _column_2,
          _column_4,
          _column_5,
          _column_3,
          _column_82,
          _column_10,
          _column_84,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape26 transactions = Shape26(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_89,
          _column_71,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
          _column_85,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape23 budgets = Shape23(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_86,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_71,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape15 categoryBudgetLimits = Shape15(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_71,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape24 extends i0.VersionedTable {
  Shape24({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get walletPk =>
      columnsByName['wallet_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get currency =>
      columnsByName['currency']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get decimals =>
      columnsByName['decimals']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get homePageWidgetDisplay =>
      columnsByName['home_page_widget_display']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_87(String aliasedName) =>
    i1.GeneratedColumn<String>('home_page_widget_display', aliasedName, true,
        type: i1.DriftSqlType.string, defaultValue: const Constant(null));

class Shape25 extends i0.VersionedTable {
  Shape25({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get categoryPk =>
      columnsByName['category_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get emojiIconName =>
      columnsByName['emoji_icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get mainCategoryPk =>
      columnsByName['main_category_pk']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_88(String aliasedName) =>
    i1.GeneratedColumn<String>('main_category_pk', aliasedName, true,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES categories (category_pk)'),
        defaultValue: const Constant(null));

class Shape26 extends i0.VersionedTable {
  Shape26({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get subCategoryFk =>
      columnsByName['sub_category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get originalDateDue =>
      columnsByName['original_date_due']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get objectiveFk =>
      columnsByName['objective_fk']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_89(String aliasedName) =>
    i1.GeneratedColumn<String>('sub_category_fk', aliasedName, true,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES categories (category_pk)'),
        defaultValue: const Constant(null));

final class Schema43 extends i0.VersionedSchema {
  Schema43({required super.database}) : super(version: 43);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    objectives,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape24 wallets = Shape24(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
          _column_87,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape25 categories = Shape25(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_82,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
          _column_88,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape21 objectives = Shape21(
      source: i0.VersionedTable(
        entityName: 'objectives',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(objective_pk)',
        ],
        columns: [
          _column_83,
          _column_1,
          _column_13,
          _column_6,
          _column_2,
          _column_4,
          _column_5,
          _column_3,
          _column_82,
          _column_10,
          _column_84,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape27 transactions = Shape27(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_89,
          _column_71,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
          _column_85,
          _column_90,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape23 budgets = Shape23(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_86,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_71,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape15 categoryBudgetLimits = Shape15(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_71,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape27 extends i0.VersionedTable {
  Shape27({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get subCategoryFk =>
      columnsByName['sub_category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get originalDateDue =>
      columnsByName['original_date_due']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get objectiveFk =>
      columnsByName['objective_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetFksExclude =>
      columnsByName['budget_fks_exclude']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_90(String aliasedName) =>
    i1.GeneratedColumn<String>('budget_fks_exclude', aliasedName, true,
        type: i1.DriftSqlType.string);

final class Schema44 extends i0.VersionedSchema {
  Schema44({required super.database}) : super(version: 44);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    objectives,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape24 wallets = Shape24(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
          _column_87,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape25 categories = Shape25(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_82,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
          _column_88,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape28 objectives = Shape28(
      source: i0.VersionedTable(
        entityName: 'objectives',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(objective_pk)',
        ],
        columns: [
          _column_83,
          _column_1,
          _column_13,
          _column_6,
          _column_2,
          _column_4,
          _column_91,
          _column_5,
          _column_3,
          _column_82,
          _column_10,
          _column_84,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape29 transactions = Shape29(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_89,
          _column_71,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_91,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
          _column_85,
          _column_90,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape23 budgets = Shape23(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_35,
          _column_86,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_71,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape15 categoryBudgetLimits = Shape15(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_71,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape28 extends i0.VersionedTable {
  Shape28({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get objectivePk =>
      columnsByName['objective_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get emojiIconName =>
      columnsByName['emoji_icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<DateTime> _column_91(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('end_date', aliasedName, true,
        type: i1.DriftSqlType.dateTime);

class Shape29 extends i0.VersionedTable {
  Shape29({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get subCategoryFk =>
      columnsByName['sub_category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get originalDateDue =>
      columnsByName['original_date_due']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get objectiveFk =>
      columnsByName['objective_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetFksExclude =>
      columnsByName['budget_fks_exclude']! as i1.GeneratedColumn<String>;
}

final class Schema45 extends i0.VersionedSchema {
  Schema45({required super.database}) : super(version: 45);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    objectives,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape24 wallets = Shape24(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
          _column_87,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape25 categories = Shape25(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_82,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
          _column_88,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape30 objectives = Shape30(
      source: i0.VersionedTable(
        entityName: 'objectives',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(objective_pk)',
        ],
        columns: [
          _column_83,
          _column_1,
          _column_13,
          _column_6,
          _column_2,
          _column_4,
          _column_91,
          _column_5,
          _column_3,
          _column_82,
          _column_10,
          _column_84,
          _column_92,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape29 transactions = Shape29(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_89,
          _column_92,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_91,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
          _column_85,
          _column_90,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape31 budgets = Shape31(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_93,
          _column_35,
          _column_86,
          _column_10,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_92,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape32 categoryBudgetLimits = Shape32(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
          _column_92,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_92,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape30 extends i0.VersionedTable {
  Shape30({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get objectivePk =>
      columnsByName['objective_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get emojiIconName =>
      columnsByName['emoji_icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_92(String aliasedName) =>
    i1.GeneratedColumn<String>('wallet_fk', aliasedName, false,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES wallets (wallet_pk)'),
        defaultValue: const Constant("0"));

class Shape31 extends i0.VersionedTable {
  Shape31({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get budgetPk =>
      columnsByName['budget_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get startDate =>
      columnsByName['start_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get walletFks =>
      columnsByName['wallet_fks']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFks =>
      columnsByName['category_fks']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFksExclude =>
      columnsByName['category_fks_exclude']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get addedTransactionsOnly =>
      columnsByName['added_transactions_only']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetTransactionFilters =>
      columnsByName['budget_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get memberTransactionFilters =>
      columnsByName['member_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedOwnerMember =>
      columnsByName['shared_owner_member']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedMembers =>
      columnsByName['shared_members']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedAllMembersEver =>
      columnsByName['shared_all_members_ever']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get isAbsoluteSpendingLimit =>
      columnsByName['is_absolute_spending_limit']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<String> _column_93(String aliasedName) =>
    i1.GeneratedColumn<String>('wallet_fks', aliasedName, true,
        type: i1.DriftSqlType.string);

class Shape32 extends i0.VersionedTable {
  Shape32({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get categoryLimitPk =>
      columnsByName['category_limit_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetFk =>
      columnsByName['budget_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
}

final class Schema46 extends i0.VersionedSchema {
  Schema46({required super.database}) : super(version: 46);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    wallets,
    categories,
    objectives,
    transactions,
    budgets,
    categoryBudgetLimits,
    associatedTitles,
    appSettings,
    scannerTemplates,
    deleteLogs,
  ];
  late final Shape33 wallets = Shape33(
      source: i0.VersionedTable(
        entityName: 'wallets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(wallet_pk)',
        ],
        columns: [
          _column_67,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_94,
          _column_8,
          _column_87,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape25 categories = Shape25(
      source: i0.VersionedTable(
        entityName: 'categories',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_pk)',
        ],
        columns: [
          _column_68,
          _column_1,
          _column_2,
          _column_3,
          _column_82,
          _column_4,
          _column_5,
          _column_6,
          _column_10,
          _column_11,
          _column_88,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape34 objectives = Shape34(
      source: i0.VersionedTable(
        entityName: 'objectives',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(objective_pk)',
        ],
        columns: [
          _column_83,
          _column_95,
          _column_1,
          _column_13,
          _column_6,
          _column_2,
          _column_4,
          _column_91,
          _column_5,
          _column_3,
          _column_82,
          _column_10,
          _column_84,
          _column_96,
          _column_92,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape35 transactions = Shape35(
      source: i0.VersionedTable(
        entityName: 'transactions',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(transaction_pk)',
        ],
        columns: [
          _column_69,
          _column_97,
          _column_1,
          _column_13,
          _column_14,
          _column_70,
          _column_89,
          _column_92,
          _column_4,
          _column_5,
          _column_81,
          _column_10,
          _column_18,
          _column_19,
          _column_91,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_11,
          _column_25,
          _column_26,
          _column_27,
          _column_28,
          _column_29,
          _column_30,
          _column_72,
          _column_85,
          _column_98,
          _column_90,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape36 budgets = Shape36(
      source: i0.VersionedTable(
        entityName: 'budgets',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(budget_pk)',
        ],
        columns: [
          _column_73,
          _column_1,
          _column_13,
          _column_2,
          _column_33,
          _column_34,
          _column_93,
          _column_35,
          _column_86,
          _column_10,
          _column_96,
          _column_37,
          _column_38,
          _column_19,
          _column_4,
          _column_5,
          _column_39,
          _column_6,
          _column_92,
          _column_40,
          _column_41,
          _column_27,
          _column_42,
          _column_30,
          _column_43,
          _column_44,
          _column_66,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape32 categoryBudgetLimits = Shape32(
      source: i0.VersionedTable(
        entityName: 'category_budget_limits',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(category_limit_pk)',
        ],
        columns: [
          _column_74,
          _column_70,
          _column_75,
          _column_13,
          _column_5,
          _column_92,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape16 associatedTitles = Shape16(
      source: i0.VersionedTable(
        entityName: 'associated_titles',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(associated_title_pk)',
        ],
        columns: [
          _column_76,
          _column_70,
          _column_48,
          _column_4,
          _column_5,
          _column_6,
          _column_49,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape6 appSettings = Shape6(
      source: i0.VersionedTable(
        entityName: 'app_settings',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_50,
          _column_51,
          _column_52,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape17 scannerTemplates = Shape17(
      source: i0.VersionedTable(
        entityName: 'scanner_templates',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(scanner_template_pk)',
        ],
        columns: [
          _column_77,
          _column_4,
          _column_5,
          _column_54,
          _column_55,
          _column_56,
          _column_57,
          _column_58,
          _column_59,
          _column_78,
          _column_92,
          _column_61,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape18 deleteLogs = Shape18(
      source: i0.VersionedTable(
        entityName: 'delete_logs',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(delete_log_pk)',
        ],
        columns: [
          _column_79,
          _column_80,
          _column_63,
          _column_65,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape33 extends i0.VersionedTable {
  Shape33({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get walletPk =>
      columnsByName['wallet_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get currency =>
      columnsByName['currency']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get currencyFormat =>
      columnsByName['currency_format']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get decimals =>
      columnsByName['decimals']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get homePageWidgetDisplay =>
      columnsByName['home_page_widget_display']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_94(String aliasedName) =>
    i1.GeneratedColumn<String>('currency_format', aliasedName, true,
        type: i1.DriftSqlType.string);

class Shape34 extends i0.VersionedTable {
  Shape34({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get objectivePk =>
      columnsByName['objective_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get iconName =>
      columnsByName['icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get emojiIconName =>
      columnsByName['emoji_icon_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get archived =>
      columnsByName['archived']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<int> _column_95(String aliasedName) =>
    i1.GeneratedColumn<int>('type', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: Constant(0));
i1.GeneratedColumn<bool> _column_96(String aliasedName) =>
    i1.GeneratedColumn<bool>('archived', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("archived" IN (0, 1))'),
        defaultValue: const Constant(false));

class Shape35 extends i0.VersionedTable {
  Shape35({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get transactionPk =>
      columnsByName['transaction_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get pairedTransactionFk =>
      columnsByName['paired_transaction_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get note =>
      columnsByName['note']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFk =>
      columnsByName['category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get subCategoryFk =>
      columnsByName['sub_category_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get originalDateDue =>
      columnsByName['original_date_due']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get upcomingTransactionNotification =>
      columnsByName['upcoming_transaction_notification']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get type =>
      columnsByName['type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get paid =>
      columnsByName['paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get createdAnotherFutureTransaction =>
      columnsByName['created_another_future_transaction']!
          as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get skipPaid =>
      columnsByName['skip_paid']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get methodAdded =>
      columnsByName['method_added']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get transactionOwnerEmail =>
      columnsByName['transaction_owner_email']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get transactionOriginalOwnerEmail =>
      columnsByName['transaction_original_owner_email']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedOldKey =>
      columnsByName['shared_old_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedStatus =>
      columnsByName['shared_status']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedReferenceBudgetPk =>
      columnsByName['shared_reference_budget_pk']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get objectiveFk =>
      columnsByName['objective_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get objectiveLoanFk =>
      columnsByName['objective_loan_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetFksExclude =>
      columnsByName['budget_fks_exclude']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_97(String aliasedName) =>
    i1.GeneratedColumn<String>('paired_transaction_fk', aliasedName, true,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES transactions (transaction_pk)'),
        defaultValue: const Constant(null));
i1.GeneratedColumn<String> _column_98(String aliasedName) =>
    i1.GeneratedColumn<String>('objective_loan_fk', aliasedName, true,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES objectives (objective_pk)'));

class Shape36 extends i0.VersionedTable {
  Shape36({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get budgetPk =>
      columnsByName['budget_pk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<double> get amount =>
      columnsByName['amount']! as i1.GeneratedColumn<double>;
  i1.GeneratedColumn<String> get colour =>
      columnsByName['colour']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get startDate =>
      columnsByName['start_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get endDate =>
      columnsByName['end_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get walletFks =>
      columnsByName['wallet_fks']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFks =>
      columnsByName['category_fks']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get categoryFksExclude =>
      columnsByName['category_fks_exclude']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get income =>
      columnsByName['income']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get archived =>
      columnsByName['archived']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get addedTransactionsOnly =>
      columnsByName['added_transactions_only']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get periodLength =>
      columnsByName['period_length']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get reoccurrence =>
      columnsByName['reoccurrence']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get dateCreated =>
      columnsByName['date_created']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get dateTimeModified =>
      columnsByName['date_time_modified']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<bool> get pinned =>
      columnsByName['pinned']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get order =>
      columnsByName['order']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get walletFk =>
      columnsByName['wallet_fk']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get budgetTransactionFilters =>
      columnsByName['budget_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get memberTransactionFilters =>
      columnsByName['member_transaction_filters']!
          as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedKey =>
      columnsByName['shared_key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get sharedOwnerMember =>
      columnsByName['shared_owner_member']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<DateTime> get sharedDateUpdated =>
      columnsByName['shared_date_updated']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get sharedMembers =>
      columnsByName['shared_members']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sharedAllMembersEver =>
      columnsByName['shared_all_members_ever']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get isAbsoluteSpendingLimit =>
      columnsByName['is_absolute_spending_limit']! as i1.GeneratedColumn<bool>;
}

i0.MigrationStepWithVersion migrationSteps({
  required Future<void> Function(i1.Migrator m, Schema34 schema) from33To34,
  required Future<void> Function(i1.Migrator m, Schema35 schema) from34To35,
  required Future<void> Function(i1.Migrator m, Schema36 schema) from35To36,
  required Future<void> Function(i1.Migrator m, Schema37 schema) from36To37,
  required Future<void> Function(i1.Migrator m, Schema38 schema) from37To38,
  required Future<void> Function(i1.Migrator m, Schema39 schema) from38To39,
  required Future<void> Function(i1.Migrator m, Schema40 schema) from39To40,
  required Future<void> Function(i1.Migrator m, Schema41 schema) from40To41,
  required Future<void> Function(i1.Migrator m, Schema42 schema) from41To42,
  required Future<void> Function(i1.Migrator m, Schema43 schema) from42To43,
  required Future<void> Function(i1.Migrator m, Schema44 schema) from43To44,
  required Future<void> Function(i1.Migrator m, Schema45 schema) from44To45,
  required Future<void> Function(i1.Migrator m, Schema46 schema) from45To46,
}) {
  return (currentVersion, database) async {
    switch (currentVersion) {
      case 33:
        final schema = Schema34(database: database);
        final migrator = i1.Migrator(database, schema);
        await from33To34(migrator, schema);
        return 34;
      case 34:
        final schema = Schema35(database: database);
        final migrator = i1.Migrator(database, schema);
        await from34To35(migrator, schema);
        return 35;
      case 35:
        final schema = Schema36(database: database);
        final migrator = i1.Migrator(database, schema);
        await from35To36(migrator, schema);
        return 36;
      case 36:
        final schema = Schema37(database: database);
        final migrator = i1.Migrator(database, schema);
        await from36To37(migrator, schema);
        return 37;
      case 37:
        final schema = Schema38(database: database);
        final migrator = i1.Migrator(database, schema);
        await from37To38(migrator, schema);
        return 38;
      case 38:
        final schema = Schema39(database: database);
        final migrator = i1.Migrator(database, schema);
        await from38To39(migrator, schema);
        return 39;
      case 39:
        final schema = Schema40(database: database);
        final migrator = i1.Migrator(database, schema);
        await from39To40(migrator, schema);
        return 40;
      case 40:
        final schema = Schema41(database: database);
        final migrator = i1.Migrator(database, schema);
        await from40To41(migrator, schema);
        return 41;
      case 41:
        final schema = Schema42(database: database);
        final migrator = i1.Migrator(database, schema);
        await from41To42(migrator, schema);
        return 42;
      case 42:
        final schema = Schema43(database: database);
        final migrator = i1.Migrator(database, schema);
        await from42To43(migrator, schema);
        return 43;
      case 43:
        final schema = Schema44(database: database);
        final migrator = i1.Migrator(database, schema);
        await from43To44(migrator, schema);
        return 44;
      case 44:
        final schema = Schema45(database: database);
        final migrator = i1.Migrator(database, schema);
        await from44To45(migrator, schema);
        return 45;
      case 45:
        final schema = Schema46(database: database);
        final migrator = i1.Migrator(database, schema);
        await from45To46(migrator, schema);
        return 46;
      default:
        throw ArgumentError.value('Unknown migration from $currentVersion');
    }
  };
}

i1.OnUpgrade stepByStep({
  required Future<void> Function(i1.Migrator m, Schema34 schema) from33To34,
  required Future<void> Function(i1.Migrator m, Schema35 schema) from34To35,
  required Future<void> Function(i1.Migrator m, Schema36 schema) from35To36,
  required Future<void> Function(i1.Migrator m, Schema37 schema) from36To37,
  required Future<void> Function(i1.Migrator m, Schema38 schema) from37To38,
  required Future<void> Function(i1.Migrator m, Schema39 schema) from38To39,
  required Future<void> Function(i1.Migrator m, Schema40 schema) from39To40,
  required Future<void> Function(i1.Migrator m, Schema41 schema) from40To41,
  required Future<void> Function(i1.Migrator m, Schema42 schema) from41To42,
  required Future<void> Function(i1.Migrator m, Schema43 schema) from42To43,
  required Future<void> Function(i1.Migrator m, Schema44 schema) from43To44,
  required Future<void> Function(i1.Migrator m, Schema45 schema) from44To45,
  required Future<void> Function(i1.Migrator m, Schema46 schema) from45To46,
}) =>
    i0.VersionedSchema.stepByStepHelper(
        step: migrationSteps(
      from33To34: from33To34,
      from34To35: from34To35,
      from35To36: from35To36,
      from36To37: from36To37,
      from37To38: from37To38,
      from38To39: from38To39,
      from39To40: from39To40,
      from40To41: from40To41,
      from41To42: from41To42,
      from42To43: from42To43,
      from43To44: from43To44,
      from44To45: from44To45,
      from45To46: from45To46,
    ));
