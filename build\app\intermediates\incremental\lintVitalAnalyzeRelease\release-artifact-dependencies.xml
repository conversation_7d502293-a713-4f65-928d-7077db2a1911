<dependencies>
  <compile
      roots="__local_aars__:D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:file_picker::release,:@@:flutter_timezone::release,:@@:in_app_review::release,:@@:recaptcha_enterprise_flutter::release,:@@:shared_preferences_android::release,com.google.firebase:firebase-analytics:22.4.0@aar,com.google.android.gms:play-services-measurement-api:22.4.0@aar,:@@:local_auth_android::release,:@@:app_links::release,:@@:app_settings::release,:@@:cloud_firestore::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:firebase_auth::release,:@@:firebase_core::release,:@@:flutter_charset_detector_android::release,:@@:flutter_displaymode::release,:@@:flutter_local_notifications::release,:@@:flutter_plugin_android_lifecycle::release,:@@:flutter_secure_storage::release,:@@:google_sign_in_android::release,:@@:home_widget::release,:@@:image_picker_android::release,:@@:in_app_purchase_android::release,:@@:notification_listener_service::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:permission_handler_android::release,:@@:quick_actions_android::release,:@@:share_plus::release,:@@:sqlite3_flutter_libs::release,:@@:system_theme::release,:@@:url_launcher_android::release,io.flutter:flutter_embedding_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar,androidx.biometric:biometric:1.1.0@aar,com.google.android.gms:play-services-measurement:22.4.0@aar,com.google.android.gms:play-services-measurement-sdk:22.4.0@aar,com.google.android.gms:play-services-measurement-impl:22.4.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar,com.google.android.gms:play-services-measurement-base:22.4.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.loader:loader:1.1.0@aar,androidx.activity:activity:1.9.3@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.lifecycle:lifecycle-common-java8:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.window:window-java:1.2.0@aar,androidx.window:window:1.2.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar,androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-basement:18.5.0@aar,androidx.core:core-ktx:1.15.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.15.0@aar,androidx.core:core:1.15.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-jvm:1.4.2@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar,com.google.guava:guava:32.1.3-android@jar,io.flutter:armeabi_v7a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar,io.flutter:arm64_v8a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar,io.flutter:x86_64_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.37.0@jar,com.google.errorprone:error_prone_annotations:2.36.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.j2objc:j2objc-annotations:2.8@jar">
    <dependency
        name="__local_aars__:D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="com.mr.flutter.plugin.filepicker:file_picker"/>
    <dependency
        name=":@@:flutter_timezone::release"
        simpleName="net.wolverinebeach.flutter_timezone:flutter_timezone"/>
    <dependency
        name=":@@:in_app_review::release"
        simpleName="dev.britannio.in_app_review:in_app_review"/>
    <dependency
        name=":@@:recaptcha_enterprise_flutter::release"
        simpleName="com.google.flutter.recaptcha:recaptcha_enterprise_flutter"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="io.flutter.plugins.sharedpreferences:shared_preferences_android"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.4.0@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name=":@@:local_auth_android::release"
        simpleName="io.flutter.plugins.localauth:local_auth_android"/>
    <dependency
        name=":@@:app_links::release"
        simpleName="com.llfbandit.app_links:app_links"/>
    <dependency
        name=":@@:app_settings::release"
        simpleName="com.spencerccf.app_settings:app_settings"/>
    <dependency
        name=":@@:cloud_firestore::release"
        simpleName="io.flutter.plugins.firebase.cloudfirestore:cloud_firestore"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="dev.fluttercommunity.plus.connectivity:connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="dev.fluttercommunity.plus.device_info:device_info_plus"/>
    <dependency
        name=":@@:firebase_auth::release"
        simpleName="io.flutter.plugins.firebase.auth:firebase_auth"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="io.flutter.plugins.firebase.core:firebase_core"/>
    <dependency
        name=":@@:flutter_charset_detector_android::release"
        simpleName="com.madlonkay.flutter_charset_detector:flutter_charset_detector_android"/>
    <dependency
        name=":@@:flutter_displaymode::release"
        simpleName="com.ajinasokan.flutterdisplaymode:flutter_displaymode"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="com.dexterous.flutterlocalnotifications:flutter_local_notifications"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:flutter_secure_storage::release"
        simpleName="com.it_nomads.fluttersecurestorage:flutter_secure_storage"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="io.flutter.plugins.googlesignin:google_sign_in_android"/>
    <dependency
        name=":@@:home_widget::release"
        simpleName="es.antonborri.home_widget:home_widget"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="io.flutter.plugins.imagepicker:image_picker_android"/>
    <dependency
        name=":@@:in_app_purchase_android::release"
        simpleName="io.flutter.plugins.inapppurchase:in_app_purchase_android"/>
    <dependency
        name=":@@:notification_listener_service::release"
        simpleName="notification.listener.service:notification_listener_service"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="dev.fluttercommunity.plus.packageinfo:package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="io.flutter.plugins.pathprovider:path_provider_android"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="com.baseflow.permissionhandler:permission_handler_android"/>
    <dependency
        name=":@@:quick_actions_android::release"
        simpleName="io.flutter.plugins.quickactions:quick_actions_android"/>
    <dependency
        name=":@@:share_plus::release"
        simpleName="dev.fluttercommunity.plus.share:share_plus"/>
    <dependency
        name=":@@:sqlite3_flutter_libs::release"
        simpleName="eu.simonbinder.sqlite3_flutter_libs:sqlite3_flutter_libs"/>
    <dependency
        name=":@@:system_theme::release"
        simpleName="com.bruno.system_theme:system_theme"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="io.flutter.plugins.urllauncher:url_launcher_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.core:core-ktx:1.15.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.15.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:2.8@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </compile>
  <package
      roots="__local_aars__:D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:app_settings::release,:@@:device_info_plus::release,:@@:file_picker::release,:@@:flutter_timezone::release,:@@:in_app_review::release,:@@:package_info_plus::release,:@@:recaptcha_enterprise_flutter::release,:@@:share_plus::release,:@@:shared_preferences_android::release,:@@:cloud_firestore::release,:@@:firebase_auth::release,:@@:firebase_core::release,com.google.firebase:firebase-firestore:25.1.3@aar,:@@:flutter_local_notifications::release,:@@:image_picker_android::release,:@@:local_auth_android::release,:@@:url_launcher_android::release,:@@:app_links::release,:@@:connectivity_plus::release,:@@:flutter_charset_detector_android::release,:@@:flutter_displaymode::release,:@@:flutter_plugin_android_lifecycle::release,:@@:flutter_secure_storage::release,:@@:google_sign_in_android::release,:@@:home_widget::release,:@@:in_app_purchase_android::release,:@@:notification_listener_service::release,:@@:path_provider_android::release,:@@:permission_handler_android::release,:@@:quick_actions_android::release,:@@:sqlite3_flutter_libs::release,:@@:system_theme::release,io.flutter:flutter_embedding_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar,androidx.glance:glance:1.0.0@aar,androidx.glance:glance-appwidget:1.0.0@aar,com.google.firebase:firebase-auth:23.2.0@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar,com.google.android.gms:play-services-auth:21.0.0@aar,androidx.biometric:biometric:1.1.0@aar,com.android.billingclient:billing:7.1.1@aar,com.google.android.recaptcha:recaptcha:18.7.0@aar,com.google.firebase:firebase-appcheck-interop:17.0.0@aar,com.google.firebase:firebase-database-collection:18.0.1@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-location:19.0.0@aar,com.google.android.gms:play-services-recaptchabase:16.1.0@aar,com.google.firebase:firebase-analytics:22.4.0@aar,com.google.android.gms:play-services-measurement:22.4.0@aar,com.google.android.gms:play-services-measurement-sdk:22.4.0@aar,com.google.android.gms:play-services-measurement-impl:22.4.0@aar,com.google.android.gms:play-services-fido:20.1.0@aar,com.google.android.gms:play-services-base:18.5.0@aar,androidx.preference:preference:1.2.1@aar,androidx.appcompat:appcompat:1.2.0@aar,com.google.android.gms:play-services-measurement-api:22.4.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.android.play:review:2.0.2@aar,com.google.android.play:integrity:1.4.0@aar,androidx.fragment:fragment-ktx:1.7.1@aar,androidx.activity:activity-ktx:1.9.3@aar,androidx.activity:activity:1.9.3@aar,androidx.activity:activity:1.9.3@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.recyclerview:recyclerview:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.work:work-runtime-ktx:2.7.1@aar,androidx.work:work-runtime:2.7.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar,androidx.lifecycle:lifecycle-service:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-common-java8:2.8.7@jar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.window:window-java:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.window:window:1.2.0@aar,androidx.compose.ui:ui-graphics:1.1.1@aar,androidx.compose.ui:ui-unit:1.1.1@aar,androidx.compose.ui:ui-geometry:1.1.1@aar,androidx.compose.runtime:runtime:1.2.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar,androidx.datastore:datastore-preferences-proto:1.1.3@jar,androidx.datastore:datastore-core-okio-jvm:1.1.3@jar,androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar,androidx.datastore:datastore-core-android:1.1.3@aar,androidx.datastore:datastore-preferences-android:1.1.3@aar,androidx.datastore:datastore-android:1.1.3@aar,androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar,androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar,androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar,com.google.android.gms:play-services-measurement-base:22.4.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-places-placereport:17.0.0@aar,com.google.android.gms:play-services-basement:18.5.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.core:core-ktx:1.15.0@aar,androidx.media:media:1.1.0@aar,androidx.browser:browser:1.8.0@aar,androidx.browser:browser:1.8.0@aar,androidx.core:core-remoteviews:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.appcompat:appcompat-resources:1.2.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.transition:transition:1.4.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.15.0@aar,androidx.core:core:1.15.0@aar,com.google.android.libraries.identity.googleid:googleid:1.1.0@aar,io.grpc:grpc-okhttp:1.62.2@jar,com.squareup.okio:okio-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar,androidx.security:security-crypto:1.1.0-alpha06@aar,com.google.crypto.tink:tink-android:1.9.0@jar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.google.firebase:firebase-components:18.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.google.android.datatransport:transport-backend-cct:3.1.8@aar,com.google.android.datatransport:transport-runtime:3.1.8@aar,com.google.android.datatransport:transport-api:3.0.0@aar,androidx.collection:collection-ktx:1.4.2@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.room:room-runtime:2.2.5@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.window.extensions.core:core:1.0.0@aar,androidx.sqlite:sqlite-framework:2.1.0@aar,androidx.sqlite:sqlite:2.1.0@aar,androidx.room:room-common:2.2.5@jar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar,androidx.compose.ui:ui-util:1.1.1@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar,io.grpc:grpc-android:1.62.2@aar,io.grpc:grpc-protobuf-lite:1.62.2@jar,io.grpc:grpc-stub:1.62.2@jar,io.grpc:grpc-util:1.62.2@jar,io.grpc:grpc-core:1.62.2@jar,io.grpc:grpc-context:1.62.2@jar,io.grpc:grpc-api:1.62.2@jar,com.google.guava:guava:32.1.3-android@jar,io.flutter:armeabi_v7a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar,io.flutter:arm64_v8a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar,io.flutter:x86_64_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.37.0@jar,com.google.code.gson:gson:2.12.0@jar,com.google.errorprone:error_prone_annotations:2.36.0@jar,org.microg:safe-parcel:1.7.0@aar,org.apache.tika:tika-core:3.1.0@jar,com.github.albfernandez:juniversalchardet:2.5.0@jar,eu.simonbinder:sqlite3-native-library:3.49.1+1@aar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.firebase:protolite-well-known-types:18.0.1@aar,com.google.firebase:firebase-annotations:16.2.0@jar,org.slf4j:slf4j-api:2.0.16@jar,commons-io:commons-io:2.18.0@jar,com.google.android.play:core-common:2.0.4@aar,com.google.protobuf:protobuf-javalite:3.25.5@jar,javax.inject:javax.inject:1@jar,io.perfmark:perfmark-api:0.26.0@jar,com.google.android:annotations:4.1.1.4@jar,org.codehaus.mojo:animal-sniffer-annotations:1.23@jar">
    <dependency
        name="__local_aars__:D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:D:\App\Android Studio\Projects\budget\build\app\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:app_settings::release"
        simpleName="com.spencerccf.app_settings:app_settings"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="dev.fluttercommunity.plus.device_info:device_info_plus"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="com.mr.flutter.plugin.filepicker:file_picker"/>
    <dependency
        name=":@@:flutter_timezone::release"
        simpleName="net.wolverinebeach.flutter_timezone:flutter_timezone"/>
    <dependency
        name=":@@:in_app_review::release"
        simpleName="dev.britannio.in_app_review:in_app_review"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="dev.fluttercommunity.plus.packageinfo:package_info_plus"/>
    <dependency
        name=":@@:recaptcha_enterprise_flutter::release"
        simpleName="com.google.flutter.recaptcha:recaptcha_enterprise_flutter"/>
    <dependency
        name=":@@:share_plus::release"
        simpleName="dev.fluttercommunity.plus.share:share_plus"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="io.flutter.plugins.sharedpreferences:shared_preferences_android"/>
    <dependency
        name=":@@:cloud_firestore::release"
        simpleName="io.flutter.plugins.firebase.cloudfirestore:cloud_firestore"/>
    <dependency
        name=":@@:firebase_auth::release"
        simpleName="io.flutter.plugins.firebase.auth:firebase_auth"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="io.flutter.plugins.firebase.core:firebase_core"/>
    <dependency
        name="com.google.firebase:firebase-firestore:25.1.3@aar"
        simpleName="com.google.firebase:firebase-firestore"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="com.dexterous.flutterlocalnotifications:flutter_local_notifications"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="io.flutter.plugins.imagepicker:image_picker_android"/>
    <dependency
        name=":@@:local_auth_android::release"
        simpleName="io.flutter.plugins.localauth:local_auth_android"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="io.flutter.plugins.urllauncher:url_launcher_android"/>
    <dependency
        name=":@@:app_links::release"
        simpleName="com.llfbandit.app_links:app_links"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="dev.fluttercommunity.plus.connectivity:connectivity_plus"/>
    <dependency
        name=":@@:flutter_charset_detector_android::release"
        simpleName="com.madlonkay.flutter_charset_detector:flutter_charset_detector_android"/>
    <dependency
        name=":@@:flutter_displaymode::release"
        simpleName="com.ajinasokan.flutterdisplaymode:flutter_displaymode"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:flutter_secure_storage::release"
        simpleName="com.it_nomads.fluttersecurestorage:flutter_secure_storage"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="io.flutter.plugins.googlesignin:google_sign_in_android"/>
    <dependency
        name=":@@:home_widget::release"
        simpleName="es.antonborri.home_widget:home_widget"/>
    <dependency
        name=":@@:in_app_purchase_android::release"
        simpleName="io.flutter.plugins.inapppurchase:in_app_purchase_android"/>
    <dependency
        name=":@@:notification_listener_service::release"
        simpleName="notification.listener.service:notification_listener_service"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="io.flutter.plugins.pathprovider:path_provider_android"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="com.baseflow.permissionhandler:permission_handler_android"/>
    <dependency
        name=":@@:quick_actions_android::release"
        simpleName="io.flutter.plugins.quickactions:quick_actions_android"/>
    <dependency
        name=":@@:sqlite3_flutter_libs::release"
        simpleName="eu.simonbinder.sqlite3_flutter_libs:sqlite3_flutter_libs"/>
    <dependency
        name=":@@:system_theme::release"
        simpleName="com.bruno.system_theme:system_theme"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.glance:glance:1.0.0@aar"
        simpleName="androidx.glance:glance"/>
    <dependency
        name="androidx.glance:glance-appwidget:1.0.0@aar"
        simpleName="androidx.glance:glance-appwidget"/>
    <dependency
        name="com.google.firebase:firebase-auth:23.2.0@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="androidx.credentials:credentials:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="com.android.billingclient:billing:7.1.1@aar"
        simpleName="com.android.billingclient:billing"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.7.0@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.firebase:firebase-database-collection:18.0.1@aar"
        simpleName="com.google.firebase:firebase-database-collection"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-location:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.android.gms:play-services-recaptchabase:16.1.0@aar"
        simpleName="com.google.android.gms:play-services-recaptchabase"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.4.0@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="androidx.preference:preference:1.2.1@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="androidx.appcompat:appcompat:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.android.play:review:2.0.2@aar"
        simpleName="com.google.android.play:review"/>
    <dependency
        name="com.google.android.play:integrity:1.4.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.7.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.3@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.0.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.7.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.7.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.compose.ui:ui-graphics:1.1.1@aar"
        simpleName="androidx.compose.ui:ui-graphics"/>
    <dependency
        name="androidx.compose.ui:ui-unit:1.1.1@aar"
        simpleName="androidx.compose.ui:ui-unit"/>
    <dependency
        name="androidx.compose.ui:ui-geometry:1.1.1@aar"
        simpleName="androidx.compose.ui:ui-geometry"/>
    <dependency
        name="androidx.compose.runtime:runtime:1.2.1@aar"
        simpleName="androidx.compose.runtime:runtime"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-external-protobuf"/>
    <dependency
        name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-proto"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-core-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.4.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-places-placereport:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-places-placereport"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.core:core-ktx:1.15.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.media:media:1.1.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.core:core-remoteviews:1.0.0@aar"
        simpleName="androidx.core:core-remoteviews"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.15.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
        simpleName="com.google.android.libraries.identity.googleid:googleid"/>
    <dependency
        name="io.grpc:grpc-okhttp:1.62.2@jar"
        simpleName="io.grpc:grpc-okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.4.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="com.google.crypto.tink:tink-android:1.9.0@jar"
        simpleName="com.google.crypto.tink:tink-android"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.0.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.2@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.room:room-runtime:2.2.5@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.window.extensions.core:core:1.0.0@aar"
        simpleName="androidx.window.extensions.core:core"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.1.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.1.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.room:room-common:2.2.5@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.compose.ui:ui-util:1.1.1@aar"
        simpleName="androidx.compose.ui:ui-util"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="io.grpc:grpc-android:1.62.2@aar"
        simpleName="io.grpc:grpc-android"/>
    <dependency
        name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
        simpleName="io.grpc:grpc-protobuf-lite"/>
    <dependency
        name="io.grpc:grpc-stub:1.62.2@jar"
        simpleName="io.grpc:grpc-stub"/>
    <dependency
        name="io.grpc:grpc-util:1.62.2@jar"
        simpleName="io.grpc:grpc-util"/>
    <dependency
        name="io.grpc:grpc-core:1.62.2@jar"
        simpleName="io.grpc:grpc-core"/>
    <dependency
        name="io.grpc:grpc-context:1.62.2@jar"
        simpleName="io.grpc:grpc-context"/>
    <dependency
        name="io.grpc:grpc-api:1.62.2@jar"
        simpleName="io.grpc:grpc-api"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.code.gson:gson:2.12.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="org.microg:safe-parcel:1.7.0@aar"
        simpleName="org.microg:safe-parcel"/>
    <dependency
        name="org.apache.tika:tika-core:3.1.0@jar"
        simpleName="org.apache.tika:tika-core"/>
    <dependency
        name="com.github.albfernandez:juniversalchardet:2.5.0@jar"
        simpleName="com.github.albfernandez:juniversalchardet"/>
    <dependency
        name="eu.simonbinder:sqlite3-native-library:3.49.1+1@aar"
        simpleName="eu.simonbinder:sqlite3-native-library"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.firebase:protolite-well-known-types:18.0.1@aar"
        simpleName="com.google.firebase:protolite-well-known-types"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="org.slf4j:slf4j-api:2.0.16@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="commons-io:commons-io:2.18.0@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.google.android.play:core-common:2.0.4@aar"
        simpleName="com.google.android.play:core-common"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.25.5@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="io.perfmark:perfmark-api:0.26.0@jar"
        simpleName="io.perfmark:perfmark-api"/>
    <dependency
        name="com.google.android:annotations:4.1.1.4@jar"
        simpleName="com.google.android:annotations"/>
    <dependency
        name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
        simpleName="org.codehaus.mojo:animal-sniffer-annotations"/>
  </package>
</dependencies>
