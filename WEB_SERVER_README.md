# Budget App - Web Server Batch Files

This folder contains several batch files to help you run the Budget App web version locally for testing and development.

## 📁 Available Batch Files

### 1. `quick_web.bat` ⚡
**Quick start for testing**
- Minimal output, fastest startup
- Updates dependencies silently
- Starts development server immediately
- **Use when**: You just want to quickly test the app

```bash
# Just double-click or run:
quick_web.bat
```

### 2. `run_web_dev.bat` 🔧
**Development mode with full features**
- Hot reload enabled (press 'r' to reload changes)
- Hot restart enabled (press 'R' to restart app)
- Debug mode with detailed error messages
- Verbose output for debugging
- **Use when**: You're actively developing and need hot reload

```bash
# Double-click or run:
run_web_dev.bat
```

### 3. `run_web_server.bat` 🌐
**Full build + server**
- Cleans previous builds
- Builds web version first
- Then starts development server
- More stable but slower startup
- **Use when**: You want to test a clean build

```bash
# Double-click or run:
run_web_server.bat
```

### 4. `run_web_prod.bat` 🚀
**Production build testing**
- Creates optimized production build
- Option to serve the production files
- Tests the actual deployment version
- **Use when**: You want to test the final production version

```bash
# Double-click or run:
run_web_prod.bat
```

### 5. `run_web_brave.bat` 🦁
**Specifically for Brave Browser users**
- Builds web version and serves static files
- Includes Brave-specific instructions
- No auto-browser opening (prevents Brave issues)
- **Use when**: You're using Brave browser and having issues

```bash
# Double-click or run:
run_web_brave.bat
```

## 🎯 Which One Should I Use?

| Scenario | Recommended Batch File | Why |
|----------|----------------------|-----|
| Quick testing | `quick_web.bat` | Fastest startup |
| Active development | `run_web_dev.bat` | Hot reload for rapid iteration |
| Clean testing | `run_web_server.bat` | Fresh build every time |
| Pre-deployment testing | `run_web_prod.bat` | Tests production version |
| **Brave Browser users** | `run_web_brave.bat` | **Handles Brave-specific issues** |

## 🔧 Development Commands

When using development mode (`run_web_dev.bat`), you can use these commands in the terminal:

- **`r`** - Hot reload (apply changes without restarting)
- **`R`** - Hot restart (restart the entire app)
- **`h`** - Show all available commands
- **`d`** - Detach (keep app running but stop Flutter tools)
- **`c`** - Clear the screen
- **`q`** - Quit (stop the app and server)

## 🌐 Accessing the App

Once any server is running, the app will be available at:
- **Local**: http://localhost:8080
- **Network**: http://[your-ip]:8080 (accessible from other devices on same network)

## 🛠️ Troubleshooting

### App Stuck on Loading Screen
1. Try `run_web_dev.bat` for better error messages
2. Check browser console (F12) for JavaScript errors
3. Clear browser cache and cookies
4. Try incognito/private browsing mode

### Brave Browser Issues 🦁
**Problem**: Brave opens and closes immediately, or app doesn't load
**Solutions**:
1. **Use the Brave-specific batch file**: `run_web_brave.bat`
2. **Disable Brave Shields**:
   - Click the shield icon in the address bar
   - Turn off "Shields" for localhost
   - Refresh the page
3. **Alternative approaches**:
   - Use Chrome instead of Brave for development
   - Use the production server (`run_web_server.bat`) instead of dev mode
   - Open the URL manually instead of auto-opening

### Flutter Not Found Error
1. Make sure Flutter is installed
2. Add Flutter to your system PATH
3. Restart command prompt/terminal
4. Run `flutter doctor` to verify installation

### Port Already in Use
1. Stop any existing Flutter web servers
2. Change port in batch file (edit `--web-port=8080` to different number)
3. Or kill the process using the port

### Build Errors
1. Run `flutter clean` manually
2. Run `flutter pub get` manually
3. Check for any dependency conflicts
4. Update Flutter: `flutter upgrade`

## 📝 Customization

You can edit any batch file to:
- Change the port number (default: 8080)
- Change the hostname (default: localhost)
- Add additional Flutter flags
- Modify the output messages

Example: To change port to 3000, edit the batch file and change:
```bash
flutter run -d chrome --web-port=8080
```
to:
```bash
flutter run -d chrome --web-port=3000
```

## 🔍 Debugging Web Issues

If the web version has issues:

1. **Check browser console** (F12 → Console tab)
2. **Check network tab** (F12 → Network tab) for failed requests
3. **Try different browsers** (Chrome, Edge, Firefox)
4. **Check Flutter web compatibility** with `flutter doctor`
5. **Use development mode** for detailed error messages

## 📱 Testing on Mobile Devices

To test on mobile devices on the same network:
1. Find your computer's IP address
2. Use `http://[your-ip]:8080` on mobile browser
3. Make sure firewall allows connections on port 8080

---

**Happy coding! 🚀**
