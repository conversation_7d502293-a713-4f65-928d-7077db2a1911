@echo off
echo ========================================
echo  Budget App - Web Server for Brave Browser
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter and add it to your PATH
    pause
    exit /b 1
)

echo Flutter version:
flutter --version
echo.

REM Get dependencies
echo Getting dependencies...
flutter pub get
echo.

echo Building web version for Brave browser...
flutter build web --release
if %errorlevel% neq 0 (
    echo ERROR: Web build failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.

REM Check if Python is available for serving
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting local server using Python on localhost:8080...
    echo.
    echo BRAVE BROWSER INSTRUCTIONS:
    echo 1. The server will start automatically
    echo 2. Open Brave browser manually
    echo 3. Go to: http://localhost:8080
    echo 4. If you see issues, disable Brave Shields for localhost:
    echo    - Click the Brave Shield icon in address bar
    echo    - Turn off "Shields" for this site
    echo 5. Refresh the page
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    
    REM Don't auto-open browser, let user open manually
    echo Server starting... Open http://localhost:8080 in Brave
    cd build\web
    python -m http.server 8080
    cd ..\..
) else (
    REM Check if Node.js is available
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo Starting local server using Node.js on localhost:8080...
        echo.
        echo BRAVE BROWSER INSTRUCTIONS:
        echo 1. The server will start automatically
        echo 2. Open Brave browser manually
        echo 3. Go to: http://localhost:8080
        echo 4. If you see issues, disable Brave Shields for localhost
        echo 5. Refresh the page
        echo.
        echo Press Ctrl+C to stop the server
        echo.
        
        echo Server starting... Open http://localhost:8080 in Brave
        npx serve build\web -p 8080
    ) else (
        echo.
        echo Build completed! Files are in: build\web\
        echo.
        echo To serve the files, you need either Python or Node.js:
        echo 1. Python: python -m http.server 8080 (from build\web\ directory)
        echo 2. Node.js: npx serve build\web -p 8080
        echo.
        echo Then open http://localhost:8080 in Brave browser
        echo Remember to disable Brave Shields if needed
        echo.
    )
)

echo.
echo Web server stopped.
pause
