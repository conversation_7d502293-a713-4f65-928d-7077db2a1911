## 0.5.2
* **Added** `customBuilder` for infinite lists

## 0.5.0
* **Added** NNBD support
* **Fixed** #70

### 0.4.1
* **Fixed** #56

## 0.4.0
* **Added** `SheetListenerBuilder`
* **Added** `liftOnScrollHeaderElevation` and `liftOnScrollFooterElevation` fields.
* **Changed** `addTopViewPaddingOnFullscreen` to `avoidStatusBar`
* **Added** `routeSettings` field to `showSlidingBottomSheet`.

### 0.3.7
* **Added** `extendBody` field
* **Various** bug fixes

### 0.3.5
* **Added** `initialSnap` field to `SnapSpec`.
* **Added** `maxScrollExtent` field to `SheetState`.

### 0.3.4
* **Various** bug fixes and performance improvements.

## 0.3.0
* **Added** `isDismissable` option for the `SlidingSheetDialog`
* **Changed** the `closeSheetOnBackButtonPressed` field to `closeOnBackButtonPressed`
* **Added** `body` and `parallaxSpec` field for the `SlidingSheet`

### 0.2.12
* **Various** bug fixes and performance improvements.

## 0.2.0
* **Changed** the implemenation for bottom sheets.

## 0.1.0
* Initial release