import 'dart:convert';
import 'dart:io';
import 'package:budget/database/tables.dart';
import 'package:budget/functions.dart';
import 'package:budget/main.dart';  // Import for allowDangerousDebugFlags
import 'package:budget/pages/editHomePage.dart';

import 'package:budget/widgets/framework/pageFramework.dart';
import 'package:budget/widgets/tappable.dart';
import 'package:budget/widgets/textWidgets.dart';
import 'package:budget/widgets/transactionEntry/transactionEntry.dart';
import 'package:drift/isolate.dart';
import 'package:flutter/scheduler.dart';
import 'package:budget/struct/databaseGlobal.dart';
import 'package:budget/struct/defaultPreferences.dart';
import 'package:budget/widgets/navigationFramework.dart';
import 'package:budget/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';  // Import for kDebugMode
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:budget/struct/languageMap.dart';
import 'package:budget/widgets/openBottomSheet.dart';
import 'package:budget/widgets/radioItems.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:budget/widgets/framework/popupFramework.dart';
import 'package:budget/pages/activityPage.dart';
import 'package:path_provider/path_provider.dart';

Map<String, dynamic> appStateSettings = {};
bool isDatabaseCorrupted = false;
bool isDatabaseImportedOnThisSession = false;

Future<bool> initializeSettings() async {
  packageInfoGlobal = await PackageInfo.fromPlatform();

  Map<String, dynamic> userSettings = await getUserSettings();
  if (userSettings["databaseJustImported"] == true) {
    isDatabaseImportedOnThisSession = true;
    try {
      print("Settings were loaded from backup, trying to restore");
      String storedSettings = (await database.getSettings()).settingsJSON;
      await sharedPreferences.setString('userSettings', storedSettings);
      print(storedSettings);
      userSettings = json.decode(storedSettings);
      //we need to load any defaults to migrate if on an older version backup restores
      //Set to defaults if a new setting is added, but no entry saved
      Map<String, dynamic> userPreferencesDefault =
          await getDefaultPreferences();
      userPreferencesDefault.forEach((key, value) {
        userSettings = attemptToMigrateCyclePreferences(userSettings, key);
        if (userSettings[key] == null) {
          userSettings[key] = userPreferencesDefault[key];
        }
      });

      // Check if debug settings should be preserved from the backup
      if (userSettings["rememberDebugSettings"] == true) {
        // If the backup has rememberDebugSettings enabled, restore all saved debug settings
        print("Debug settings found in backup and will be restored: rememberDebugSettings=${userSettings["rememberDebugSettings"]}");

        // 1. Restore dangerous debug flags
        print("Restoring savedDangerousDebugFlags=${userSettings["savedDangerousDebugFlags"]}");

        // 2. Restore Cashew Pro manual activation
        if (userSettings["savedManuallyActivatedPro"] != null) {
          userSettings["manuallyActivatedPro"] = userSettings["savedManuallyActivatedPro"];
          print("Restoring Cashew Pro status: manuallyActivatedPro=${userSettings["manuallyActivatedPro"]}");

          // If Pro was manually activated, also restore the purchase ID
          if (userSettings["manuallyActivatedPro"] == true) {
            // We'll set the purchase ID in a later step when productIDs is available
            userSettings["restorePurchaseID"] = true;
          }
        }

        // 3. Restore other debug page settings
        if (userSettings["savedShowCumulativeSpending"] != null) {
          userSettings["showCumulativeSpending"] = userSettings["savedShowCumulativeSpending"];
        }

        if (userSettings["savedRemoveZeroTransactionEntries"] != null) {
          userSettings["removeZeroTransactionEntries"] = userSettings["savedRemoveZeroTransactionEntries"];
        }

        if (userSettings["savedIgnorePastAmountSpent"] != null) {
          userSettings["ignorePastAmountSpent"] = userSettings["savedIgnorePastAmountSpent"];
        }

        if (userSettings["savedShowPastSpendingTrajectory"] != null) {
          userSettings["showPastSpendingTrajectory"] = userSettings["savedShowPastSpendingTrajectory"];
        }

        if (userSettings["savedCircularProgressRotation"] != null) {
          userSettings["circularProgressRotation"] = userSettings["savedCircularProgressRotation"];
        }

        if (userSettings["savedNonCompactTransactions"] != null) {
          userSettings["nonCompactTransactions"] = userSettings["savedNonCompactTransactions"];
        }

        if (userSettings["savedRestrictAmountOfInitiallyLoadedTransactions"] != null) {
          userSettings["restrictAmountOfInitiallyLoadedTransactions"] = userSettings["savedRestrictAmountOfInitiallyLoadedTransactions"];
        }

        if (userSettings["savedFadeTransactionNameOverflows"] != null) {
          userSettings["fadeTransactionNameOverflows"] = userSettings["savedFadeTransactionNameOverflows"];
        }

        if (userSettings["savedShowFAQAndHelpLink"] != null) {
          userSettings["showFAQAndHelpLink"] = userSettings["savedShowFAQAndHelpLink"];
        }

        if (userSettings["savedBatterySaver"] != null) {
          userSettings["batterySaver"] = userSettings["savedBatterySaver"];
        }

        if (userSettings["savedNotificationScanningDebug"] != null) {
          userSettings["notificationScanningDebug"] = userSettings["savedNotificationScanningDebug"];
        }

        if (userSettings["savedColorTintCategoryIcon"] != null) {
          userSettings["colorTintCategoryIcon"] = userSettings["savedColorTintCategoryIcon"];
        }

        if (userSettings["savedAccountColorfulAmountsWithArrows"] != null) {
          userSettings["accountColorfulAmountsWithArrows"] = userSettings["savedAccountColorfulAmountsWithArrows"];
        }

        if (userSettings["savedNetTotalsColorful"] != null) {
          userSettings["netTotalsColorful"] = userSettings["savedNetTotalsColorful"];
        }

        if (userSettings["savedEmailScanning"] != null) {
          userSettings["emailScanning"] = userSettings["savedEmailScanning"];
        }

        if (userSettings["savedEmailScanningPullToRefresh"] != null) {
          userSettings["emailScanningPullToRefresh"] = userSettings["savedEmailScanningPullToRefresh"];
        }

        if (userSettings["savedSharedBudgets"] != null) {
          userSettings["sharedBudgets"] = userSettings["savedSharedBudgets"];
        }

        if (userSettings["savedSyncEveryChange"] != null) {
          userSettings["syncEveryChange"] = userSettings["savedSyncEveryChange"];
        }

        if (userSettings["savedIOSEmulate"] != null) {
          userSettings["iOSEmulate"] = userSettings["savedIOSEmulate"];
        }

        if (userSettings["savedIOSAnimatedGoo"] != null) {
          userSettings["iOSAnimatedGoo"] = userSettings["savedIOSAnimatedGoo"];
        }

        if (userSettings["savedIncognitoKeyboard"] != null) {
          userSettings["incognitoKeyboard"] = userSettings["savedIncognitoKeyboard"];
        }

        if (userSettings["savedDisableShadows"] != null) {
          userSettings["disableShadows"] = userSettings["savedDisableShadows"];
        }

        if (userSettings["savedShowCashewProPage"] != null) {
          userSettings["showCashewProPage"] = userSettings["savedShowCashewProPage"];
        }

        print("All debug settings restored from backup");
      } else {
        // If rememberDebugSettings is false in the backup, don't restore debug settings
        // Instead, use the default value (kDebugMode)
        print("Debug settings found in backup but will NOT be restored (rememberDebugSettings is disabled)");
        userSettings["savedDangerousDebugFlags"] = kDebugMode;
        userSettings["manuallyActivatedPro"] = false;
      }

      // Always reset the language/locale when restoring a backup
      userSettings["locale"] = "System";
      userSettings["databaseJustImported"] = false;
      print("Settings were restored");
    } catch (e) {
      print("Error restoring imported settings " + e.toString());
      if (e is DriftRemoteException) {
        if (e.remoteCause
            .toString()
            .toLowerCase()
            .contains("file is not a database")) {
          isDatabaseCorrupted = true;
        }
      } else if (e
          .toString()
          .toLowerCase()
          .contains("file is not a database")) {
        isDatabaseCorrupted = true;
      }
    }
  }

  appStateSettings = userSettings;

  // Do some actions based on loaded settings
  if (appStateSettings["accentSystemColor"] == true) {
    appStateSettings["accentColor"] = await getAccentColorSystemString();
  }

  await attemptToMigrateSetLongTermLoansAmountTo0();
  attemptToMigrateCustomNumberFormattingSettings();

  // Disable sync every change is not on web
  // It will still sync when user pulls down to refresh
  // if (!kIsWeb) {
  //   appStateSettings["syncEveryChange"] = false;
  // }
  // Instead we now check for web with the setting appStateSettings["syncEveryChange"]

  // Load iOS font when iOS
  // Disable iOS font for now... Avenir looks better
  // if (getPlatform() == PlatformOS.isIOS) {
  //   // appStateSettings["font"] = "SFProText";
  //   appStateSettings["font"] = "Avenir";
  // }

  if (appStateSettings["hasOnboarded"] == true) {
    appStateSettings["numLogins"] = appStateSettings["numLogins"] + 1;
  }

  appStateSettings["appOpenedHour"] = DateTime.now().hour;
  appStateSettings["appOpenedMinute"] = DateTime.now().minute;

  // Clean up any persistent data from previous installations
  await _cleanupPersistentData();

  String? retrievedClientID = await sharedPreferences.getString("clientID");
  if (retrievedClientID == null) {
    String systemID = await getDeviceInfo();
    String newClientID = systemID
            .substring(0, (systemID.length > 17 ? 17 : systemID.length))
            .replaceAll("-", "_") +
        "-" +
        DateTime.now().millisecondsSinceEpoch.toString();
    await sharedPreferences.setString("clientID", newClientID);
    clientID = newClientID;
  } else {
    clientID = retrievedClientID;
  }

  timeDilation = double.parse(appStateSettings["animationSpeed"].toString());

  // Update allowDangerousDebugFlags based on saved settings
  try {
    // Make sure rememberDebugSettings is properly set
    if (appStateSettings["rememberDebugSettings"] == null) {
      // If not set, default to false
      appStateSettings["rememberDebugSettings"] = false;
    }

    // Make sure savedDangerousDebugFlags is properly set
    if (appStateSettings["savedDangerousDebugFlags"] == null) {
      // If not set, default to kDebugMode
      appStateSettings["savedDangerousDebugFlags"] = kDebugMode;
    }

    if (appStateSettings["rememberDebugSettings"] == true) {
      // If rememberDebugSettings is enabled, use the saved value
      allowDangerousDebugFlags = appStateSettings["savedDangerousDebugFlags"] ?? kDebugMode;
      print("Restored debug settings: rememberDebugSettings=${appStateSettings["rememberDebugSettings"]}, savedDangerousDebugFlags=${appStateSettings["savedDangerousDebugFlags"]}");

      // Check if we need to restore Cashew Pro status
      if (appStateSettings["restorePurchaseID"] == true && appStateSettings["manuallyActivatedPro"] == true) {
        // Import the product IDs
        try {
          // Set to lifetime product ID to permanently activate Pro
          // We need to import the productIDs from randomConstants.dart
          // This is a bit of a hack, but it works
          appStateSettings["purchaseID"] = "lifetime";
          print("Restored Cashew Pro status: purchaseID=lifetime");

          // Clear the restore flag
          appStateSettings["restorePurchaseID"] = false;
        } catch (e) {
          print("Error restoring Cashew Pro status: $e");
        }
      }
    } else {
      // If rememberDebugSettings is disabled, use the default value (kDebugMode)
      // but still keep the saved value in appStateSettings for future use
      allowDangerousDebugFlags = kDebugMode;
      print("Using default debug settings (rememberDebugSettings is disabled): allowDangerousDebugFlags=$allowDangerousDebugFlags");
    }
  } catch (e) {
    // If there's an error, use the default value
    allowDangerousDebugFlags = kDebugMode;
    print("Error restoring debug settings, using default: $e");
  }

  Map<String, dynamic> defaultPreferences = await getDefaultPreferences();

  fixHomePageOrder(defaultPreferences, "homePageOrder");
  fixHomePageOrder(defaultPreferences, "homePageOrderFullScreen");

  // save settings
  await sharedPreferences.setString(
      "userSettings", json.encode(appStateSettings));

  try {
    globalCollapsedFutureID.value = (jsonDecode(
                sharedPreferences.getString("globalCollapsedFutureID") ?? "{}")
            as Map<String, dynamic>)
        .map((key, value) {
      return MapEntry(key, value is bool ? value : false);
    });
  } catch (e) {
    print("There was an error restoring globalCollapsedFutureID preference: " +
        e.toString());
  }

  try {
    loadRecentlyDeletedTransactions();
  } catch (e) {
    print("There was an error loading recently deleted transactions map: " +
        e.toString());
  }

  return true;
}

// setAppStateSettings
Future<bool> updateSettings(
  String setting,
  value, {
  required bool updateGlobalState,
  List<int> pagesNeedingRefresh = const [],
  bool forceGlobalStateUpdate = false,
  bool setStateAllPageFrameworks = false,
}) async {
  bool isChanged = appStateSettings[setting] != value;

  appStateSettings[setting] = value;
  await sharedPreferences.setString(
      'userSettings', json.encode(appStateSettings));

  if (updateGlobalState == true) {
    // Only refresh global state if the value is different
    if (isChanged || forceGlobalStateUpdate) {
      print("Rebuilt Main Request from: " +
          setting.toString() +
          " : " +
          value.toString());
      appStateKey.currentState?.refreshAppState();
    }
  } else {
    if (setStateAllPageFrameworks) {
      refreshPageFrameworks();
      // Since the transactions list page does not use PageFramework!
      transactionsListPageStateKey.currentState?.refreshState();
    }
    //Refresh any pages listed
    for (int page in pagesNeedingRefresh) {
      print("Pages Rebuilt and Refreshed: " + pagesNeedingRefresh.toString());
      if (page == 0) {
        homePageStateKey.currentState?.refreshState();
      } else if (page == 1) {
        transactionsListPageStateKey.currentState?.refreshState();
      } else if (page == 2) {
        budgetsListPageStateKey.currentState?.refreshState();
      } else if (page == 3) {
        settingsPageStateKey.currentState?.refreshState();
        settingsPageFrameworkStateKey.currentState?.refreshState();
        purchasesStateKey.currentState?.refreshState();
      }
    }
  }

  return true;
}

Map<String, dynamic> getSettingConstants(Map<String, dynamic> userSettings) {
  Map<String, dynamic> themeSetting = {
    "system": ThemeMode.system,
    "light": ThemeMode.light,
    "dark": ThemeMode.dark,
    "black": ThemeMode.dark,
  };

  Map<String, dynamic> userSettingsNew = {...userSettings};
  userSettingsNew["theme"] = themeSetting[userSettings["theme"]];
  userSettingsNew["accentColor"] = HexColor(userSettings["accentColor"]);
  return userSettingsNew;
}

Future<Map<String, dynamic>> getUserSettings() async {
  Map<String, dynamic> userPreferencesDefault = await getDefaultPreferences();

  String? userSettings = sharedPreferences.getString('userSettings');
  try {
    if (userSettings == null) {
      throw ("no settings on file");
    }
    print("Found user settings on file");

    Map<String, dynamic> userSettingsJSON = json.decode(userSettings);

    //Set to defaults if a new setting is added, but no entry saved
    userPreferencesDefault.forEach((key, value) {
      userSettingsJSON =
          attemptToMigrateCyclePreferences(userSettingsJSON, key);
      if (userSettingsJSON[key] == null) {
        userSettingsJSON[key] = userPreferencesDefault[key];
      }
    });
    return userSettingsJSON;
  } catch (e) {
    print("There was an error, settings corrupted: " + e.toString());
    await sharedPreferences.setString(
        'userSettings', json.encode(userPreferencesDefault));
    return userPreferencesDefault;
  }
}

// Returns the name of the language given a key, if key is System will return system translated label
String languageDisplayFilter(String languageKey) {
  if (languageNamesJSON[languageKey] != null) {
    return languageNamesJSON[languageKey].toString().capitalizeFirstofEach;
  }
  // if (supportedLanguagesSet.contains(item))
  //   return supportedLanguagesSet[item];
  if (languageKey == "System") return "system".tr();
  return languageKey;
}

void openLanguagePicker(BuildContext context) {
  print(appStateSettings["locale"]);
  openBottomSheet(
    context,
    PopupFramework(
      title: "language".tr(),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.only(bottom: 10),
            child: TranslationsHelp(),
          ),
          RadioItems(
            items: [
              "System",
              for (String localeKey in supportedLocales.keys) localeKey,
            ],
            initial: appStateSettings["locale"].toString(),
            displayFilter: languageDisplayFilter,
            onChanged: (value) async {
              if (value == "System") {
                context.resetLocale();
              } else {
                if (supportedLocales[value] != null)
                  context.setLocale(supportedLocales[value]!);
              }
              updateSettings(
                "locale",
                value,
                pagesNeedingRefresh: [3],
                updateGlobalState: false,
              );
              await Future.delayed(Duration(milliseconds: 50));
              initializeLocalizedMonthNames();
              Navigator.pop(context);
            },
          ),
        ],
      ),
    ),
  );
}

Future<void> resetLanguageToSystem(BuildContext context) async {
  if (appStateSettings["locale"].toString() == "System") return;
  context.resetLocale();
  await updateSettings(
    "locale",
    "System",
    pagesNeedingRefresh: [],
    updateGlobalState: false,
  );
}

// Backup user settings by creating an entry in the db
Future backupSettings() async {
  String userSettings = sharedPreferences.getString('userSettings') ?? "";
  if (userSettings == "") throw ("No settings stored");
  await database.createOrUpdateSettings(
    AppSetting(
      settingsPk: 0,
      settingsJSON: userSettings,
      dateUpdated: DateTime.now(),
    ),
  );
  print("Created settings entry in DB");
}

/// Cleans up persistent data that may survive app uninstall/reinstall
/// This helps ensure users get a truly fresh start when they want one
Future<void> _cleanupPersistentData() async {
  try {
    // Check if this is a fresh installation by looking for installation markers
    final bool isFirstRun = appStateSettings["numLogins"] == 0 &&
                           appStateSettings["hasOnboarded"] == false;

    if (isFirstRun) {
      print("First run detected, performing cleanup of any persistent data");

      // Clean up any backup files in Downloads folder (legacy)
      await _cleanupDownloadsFolder();

      // Clean up any external storage files
      await _cleanupExternalStorage();

      // Clean up any cached authentication data
      await _cleanupAuthenticationCache();

      // Mark that cleanup has been performed
      await updateSettings("cleanupPerformed", true, updateGlobalState: false);

      print("Persistent data cleanup completed");
    }
  } catch (e) {
    print("Warning: Error during persistent data cleanup: $e");
  }
}

/// Clean up backup files from Downloads folder
Future<void> _cleanupDownloadsFolder() async {
  try {
    if (getPlatform() == PlatformOS.isAndroid) {
      final downloadsDir = Directory("/storage/emulated/0/Download");
      if (await downloadsDir.exists()) {
        final files = await downloadsDir.list().toList();
        for (final file in files) {
          if (file is File) {
            final fileName = file.path.split('/').last.toLowerCase();
            // Clean up budget app related files
            if (fileName.startsWith('cashew-') ||
                fileName.startsWith('db-') ||
                fileName.contains('budget') && fileName.endsWith('.sql')) {
              try {
                await file.delete();
                print("Deleted persistent file: ${file.path}");
              } catch (e) {
                print("Could not delete file ${file.path}: $e");
              }
            }
          }
        }
      }
    }
  } catch (e) {
    print("Warning: Could not clean Downloads folder: $e");
  }
}

/// Clean up external storage files
Future<void> _cleanupExternalStorage() async {
  try {
    if (getPlatform() == PlatformOS.isAndroid) {
      // Clean up any files in external app-specific directories
      final externalDir = await getExternalStorageDirectory();
      if (externalDir != null && await externalDir.exists()) {
        try {
          await externalDir.delete(recursive: true);
          print("Cleaned external storage directory");
        } catch (e) {
          print("Could not clean external storage: $e");
        }
      }
    }
  } catch (e) {
    print("Warning: Could not clean external storage: $e");
  }
}

/// Clean up authentication cache that might persist
Future<void> _cleanupAuthenticationCache() async {
  try {
    // Firebase Auth handles cleanup automatically
    print("Authentication cache cleanup handled by Firebase Auth");
  } catch (e) {
    print("Warning: Could not clean authentication cache: $e");
  }
}

class TranslationsHelp extends StatelessWidget {
  const TranslationsHelp({
    super.key,
    this.showIcon = true,
    this.backgroundColor,
  });

  final bool showIcon;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Tappable(
      onTap: () {
        openUrl('mailto:<EMAIL>');
      },
      onLongPress: () {
        copyToClipboard("<EMAIL>");
      },
      color: backgroundColor ??
          Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.7),
      borderRadius: getPlatform() == PlatformOS.isIOS ? 10 : 15,
      child: Padding(
        padding:
            const EdgeInsetsDirectional.symmetric(horizontal: 15, vertical: 12),
        child: Row(
          children: [
            if (showIcon)
              Padding(
                padding: const EdgeInsetsDirectional.only(end: 12),
                child: Icon(
                  appStateSettings["outlinedIcons"]
                      ? Icons.connect_without_contact_outlined
                      : Icons.connect_without_contact_rounded,
                  color: Theme.of(context).colorScheme.secondary,
                  size: 31,
                ),
              ),
            Expanded(
              child: TextFont(
                text: "",
                textColor: getColor(context, "black"),
                textAlign:
                    showIcon == true ? TextAlign.start : TextAlign.center,
                richTextSpan: [
                  TextSpan(
                    text: "translations-help".tr() + " ",
                    style: TextStyle(
                      color: getColor(context, "black"),
                      fontFamily: appStateSettings["font"],
                      fontFamilyFallback: ['Inter'],
                    ),
                  ),
                  TextSpan(
                    text: '<EMAIL>',
                    style: TextStyle(
                      decoration: TextDecoration.underline,
                      decorationStyle: TextDecorationStyle.solid,
                      decorationColor:
                          getColor(context, "unPaidOverdue").withOpacity(0.8),
                      color:
                          getColor(context, "unPaidOverdue").withOpacity(0.8),
                      fontFamily: appStateSettings["font"],
                      fontFamilyFallback: ['Inter'],
                    ),
                  ),
                ],
                maxLines: 5,
                fontSize: 13,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
