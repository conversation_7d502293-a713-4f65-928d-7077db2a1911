<FrameLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/widget_container"
    android:layout_margin="6dp">
	<ImageView
        android:id="@+id/widget_background"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:scaleType="fitXY"
        android:src="@drawable/widget_background"
    />
	<LinearLayout
		xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingLeft="20dp"
    android:paddingRight="18dp"
    android:paddingVertical="5dp">
		<LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical">
			<TextView
            android:id="@+id/net_worth_amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:gravity="start|center_vertical"
            android:padding="3dp"
            android:textSize="25sp"
            android:textStyle="bold"
            android:textColor="#000000"
        />
			<TextView
            android:id="@+id/net_worth_transactions_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:padding="3dp"
            android:text="Tap to configure widget"
            android:textSize="13sp"
            android:textColor="#000000"
        />
		</LinearLayout>
		<FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
			<ImageButton
          android:id="@+id/plus_button"
          android:src="@drawable/plus_button"
          android:layout_width="55dp"
          android:layout_height="58dp"
          android:gravity="center"
          android:adjustViewBounds="true"
          android:scaleType="fitCenter"
          android:background="@android:color/transparent"/>
		</FrameLayout>
	</LinearLayout>
</FrameLayout>
