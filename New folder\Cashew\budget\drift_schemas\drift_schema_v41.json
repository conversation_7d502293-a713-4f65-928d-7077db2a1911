{"_meta": {"description": "This file contains a serialized version of schema entities for drift.", "version": "1.0.0"}, "options": {"store_date_time_values_as_text": false}, "entities": [{"id": 0, "references": [], "type": "table", "data": {"name": "wallets", "was_declared_in_moor": false, "columns": [{"name": "wallet_pk", "getter_name": "walletPk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "name", "getter_name": "name", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "colour", "getter_name": "colour", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "icon_name", "getter_name": "iconName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "date_created", "getter_name": "dateCreated", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => new DateTime.now()", "dsl_features": []}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}, {"name": "order", "getter_name": "order", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "currency", "getter_name": "currency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "decimals", "getter_name": "decimals", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": "<PERSON>stant(2)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["wallet_pk"]}}, {"id": 1, "references": [], "type": "table", "data": {"name": "categories", "was_declared_in_moor": false, "columns": [{"name": "category_pk", "getter_name": "categoryPk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "name", "getter_name": "name", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "colour", "getter_name": "colour", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "icon_name", "getter_name": "iconName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "emoji_icon_name", "getter_name": "emojiIconName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "date_created", "getter_name": "dateCreated", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => new DateTime.now()", "dsl_features": []}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}, {"name": "order", "getter_name": "order", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "income", "getter_name": "income", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"income\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "method_added", "getter_name": "methodAdded", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const EnumIndexConverter<MethodAdded>(MethodAdded.values)", "dart_type_name": "MethodAdded"}}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["category_pk"]}}, {"id": 2, "references": [], "type": "table", "data": {"name": "objectives", "was_declared_in_moor": false, "columns": [{"name": "objective_pk", "getter_name": "objectivePk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "name", "getter_name": "name", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "amount", "getter_name": "amount", "moor_type": "double", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "order", "getter_name": "order", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "colour", "getter_name": "colour", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "date_created", "getter_name": "dateCreated", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => new DateTime.now()", "dsl_features": []}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}, {"name": "icon_name", "getter_name": "iconName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "emoji_icon_name", "getter_name": "emojiIconName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "income", "getter_name": "income", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"income\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "pinned", "getter_name": "pinned", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"pinned\" IN (0, 1))", "default_dart": "const Constant(true)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["objective_pk"]}}, {"id": 3, "references": [1, 0, 2], "type": "table", "data": {"name": "transactions", "was_declared_in_moor": false, "columns": [{"name": "transaction_pk", "getter_name": "transactionPk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "name", "getter_name": "name", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "amount", "getter_name": "amount", "moor_type": "double", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "note", "getter_name": "note", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "category_fk", "getter_name": "categoryFk", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES categories (category_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "wallet_fk", "getter_name": "walletFk", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES wallets (wallet_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "date_created", "getter_name": "dateCreated", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => new DateTime.now()", "dsl_features": []}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}, {"name": "original_date_due", "getter_name": "originalDateDue", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}, {"name": "income", "getter_name": "income", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"income\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "period_length", "getter_name": "period<PERSON><PERSON><PERSON>", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "reoccurrence", "getter_name": "reoccurrence", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const EnumIndexConverter<BudgetReoccurence>(BudgetReoccurence.values)", "dart_type_name": "BudgetReoccurence"}}, {"name": "upcoming_transaction_notification", "getter_name": "upcomingTransactionNotification", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"upcoming_transaction_notification\" IN (0, 1))", "default_dart": "const Constant(true)", "default_client_dart": null, "dsl_features": []}, {"name": "type", "getter_name": "type", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const EnumIndexConverter<TransactionSpecialType>(TransactionSpecialType.values)", "dart_type_name": "TransactionSpecialType"}}, {"name": "paid", "getter_name": "paid", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"paid\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "created_another_future_transaction", "getter_name": "createdAnotherFutureTransaction", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"created_another_future_transaction\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "skip_paid", "getter_name": "<PERSON><PERSON><PERSON>", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"skip_paid\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "method_added", "getter_name": "methodAdded", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const EnumIndexConverter<MethodAdded>(MethodAdded.values)", "dart_type_name": "MethodAdded"}}, {"name": "transaction_owner_email", "getter_name": "transactionOwnerEmail", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_original_owner_email", "getter_name": "transactionOriginalOwnerEmail", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "shared_key", "getter_name": "sharedKey", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "shared_old_key", "getter_name": "sharedOldKey", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "shared_status", "getter_name": "sharedStatus", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const EnumIndexConverter<SharedStatus>(SharedStatus.values)", "dart_type_name": "SharedStatus"}}, {"name": "shared_date_updated", "getter_name": "sharedDateUpdated", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "shared_reference_budget_pk", "getter_name": "sharedReferenceBudgetPk", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "objective_fk", "getter_name": "objectiveFk", "moor_type": "string", "nullable": true, "customConstraints": null, "defaultConstraints": "REFERENCES objectives (objective_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["transaction_pk"]}}, {"id": 4, "references": [0], "type": "table", "data": {"name": "budgets", "was_declared_in_moor": false, "columns": [{"name": "budget_pk", "getter_name": "budgetPk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "name", "getter_name": "name", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "amount", "getter_name": "amount", "moor_type": "double", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "colour", "getter_name": "colour", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "start_date", "getter_name": "startDate", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "end_date", "getter_name": "endDate", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "category_fks", "getter_name": "categoryFks", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const StringListInColumnConverter()", "dart_type_name": "List<String>"}}, {"name": "category_fks_exclude", "getter_name": "categoryFksExclude", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const StringListInColumnConverter()", "dart_type_name": "List<String>"}}, {"name": "added_transactions_only", "getter_name": "addedTransactionsOnly", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"added_transactions_only\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "period_length", "getter_name": "period<PERSON><PERSON><PERSON>", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "reoccurrence", "getter_name": "reoccurrence", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const EnumIndexConverter<BudgetReoccurence>(BudgetReoccurence.values)", "dart_type_name": "BudgetReoccurence"}}, {"name": "date_created", "getter_name": "dateCreated", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => new DateTime.now()", "dsl_features": []}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}, {"name": "pinned", "getter_name": "pinned", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"pinned\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}, {"name": "order", "getter_name": "order", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "wallet_fk", "getter_name": "walletFk", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES wallets (wallet_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "budget_transaction_filters", "getter_name": "budgetTransactionFilters", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": "const Constant(null)", "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const BudgetTransactionFiltersListInColumnConverter()", "dart_type_name": "List<BudgetTransactionFilters>"}}, {"name": "member_transaction_filters", "getter_name": "memberTransactionFilters", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": "const Constant(null)", "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const StringListInColumnConverter()", "dart_type_name": "List<String>"}}, {"name": "shared_key", "getter_name": "sharedKey", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "shared_owner_member", "getter_name": "sharedOwnerMember", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const EnumIndexConverter<SharedOwnerMember>(SharedOwnerMember.values)", "dart_type_name": "SharedOwnerMember"}}, {"name": "shared_date_updated", "getter_name": "sharedDateUpdated", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "shared_members", "getter_name": "sharedMembers", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const StringListInColumnConverter()", "dart_type_name": "List<String>"}}, {"name": "shared_all_members_ever", "getter_name": "sharedAllMembersEver", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const StringListInColumnConverter()", "dart_type_name": "List<String>"}}, {"name": "is_absolute_spending_limit", "getter_name": "isAbsoluteSpendingLimit", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"is_absolute_spending_limit\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["budget_pk"]}}, {"id": 5, "references": [1, 4], "type": "table", "data": {"name": "category_budget_limits", "was_declared_in_moor": false, "columns": [{"name": "category_limit_pk", "getter_name": "categoryLimitPk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "category_fk", "getter_name": "categoryFk", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES categories (category_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "budget_fk", "getter_name": "budgetFk", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES budgets (budget_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "amount", "getter_name": "amount", "moor_type": "double", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["category_limit_pk"]}}, {"id": 6, "references": [1], "type": "table", "data": {"name": "associated_titles", "was_declared_in_moor": false, "columns": [{"name": "associated_title_pk", "getter_name": "associatedTitlePk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "category_fk", "getter_name": "categoryFk", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES categories (category_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "title", "getter_name": "title", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "date_created", "getter_name": "dateCreated", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => new DateTime.now()", "dsl_features": []}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}, {"name": "order", "getter_name": "order", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "is_exact_match", "getter_name": "isExactMatch", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"is_exact_match\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["associated_title_pk"]}}, {"id": 7, "references": [], "type": "table", "data": {"name": "app_settings", "was_declared_in_moor": false, "columns": [{"name": "settings_pk", "getter_name": "settingsPk", "moor_type": "int", "nullable": false, "customConstraints": null, "defaultConstraints": "PRIMARY KEY AUTOINCREMENT", "default_dart": null, "default_client_dart": null, "dsl_features": ["auto-increment"]}, {"name": "settings_j_s_o_n", "getter_name": "settingsJSON", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "date_updated", "getter_name": "dateUpdated", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => new DateTime.now()", "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": []}}, {"id": 8, "references": [1, 0], "type": "table", "data": {"name": "scanner_templates", "was_declared_in_moor": false, "columns": [{"name": "scanner_template_pk", "getter_name": "scannerTemplatePk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "date_created", "getter_name": "dateCreated", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => new DateTime.now()", "dsl_features": []}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}, {"name": "template_name", "getter_name": "templateName", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "contains", "getter_name": "contains", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "title_transaction_before", "getter_name": "titleTransactionBefore", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "title_transaction_after", "getter_name": "titleTransactionAfter", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "amount_transaction_before", "getter_name": "amountTransactionBefore", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "amount_transaction_after", "getter_name": "amountTransactionAfter", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": null, "max": null}}]}, {"name": "default_category_fk", "getter_name": "defaultCategoryFk", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES categories (category_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "wallet_fk", "getter_name": "walletFk", "moor_type": "string", "nullable": false, "customConstraints": null, "defaultConstraints": "REFERENCES wallets (wallet_pk)", "default_dart": null, "default_client_dart": null, "dsl_features": ["unknown"]}, {"name": "ignore", "getter_name": "ignore", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"ignore\" IN (0, 1))", "default_dart": "const Constant(false)", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["scanner_template_pk"]}}, {"id": 9, "references": [], "type": "table", "data": {"name": "delete_logs", "was_declared_in_moor": false, "columns": [{"name": "delete_log_pk", "getter_name": "deleteLogPk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": "() => uuid.v4()", "dsl_features": []}, {"name": "entry_pk", "getter_name": "entryPk", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "type", "getter_name": "type", "moor_type": "int", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const EnumIndexConverter<DeleteLogType>(DeleteLogType.values)", "dart_type_name": "DeleteLogType"}}, {"name": "date_time_modified", "getter_name": "dateTimeModified", "moor_type": "dateTime", "nullable": false, "customConstraints": null, "default_dart": "Constant(DateTime.now())", "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["delete_log_pk"]}}]}