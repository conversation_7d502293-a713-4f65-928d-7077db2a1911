# Translation Management Tools

This directory contains Python scripts for managing translations in the Budget app. These tools help automate translation workflows, fix missing translations, and maintain translation files.

## 📁 Files Overview

- **`translations.csv`** - Main translation file containing all translation keys and their translations
- **`Online-trsaltion.py`** - Automatically translates missing entries using Google Translate
- **`fix-missing-translations.py`** - Finds and fixes missing translation keys in Dart code
- **`generate-translations.py`** - Converts CSV to individual JSON files for each language
- **`remove_duplicates.py`** - Removes duplicate entries from the translation CSV
- **`generated/`** - Directory containing generated JSON files for each language

## 🚀 Quick Start

### Prerequisites

Install required Python packages:
```bash
pip install pandas deep-translator
```

### Basic Workflow

1. **Generate JSON files from CSV:**
   ```bash
   python generate-translations.py
   ```

2. **Find missing translations in code:**
   ```bash
   python fix-missing-translations.py --verbose
   ```

3. **Auto-translate missing entries:**
   ```bash
   python Online-trsaltion.py --verbose
   ```

4. **Remove duplicates:**
   ```bash
   python remove_duplicates.py
   ```

## 📖 Detailed Usage

### 1. Online Translation Tool (`Online-trsaltion.py`)

Automatically fills missing translations using Google Translate API.

**Basic usage:**
```bash
# Translate all missing entries
python Online-trsaltion.py

# Show what would be translated (dry run)
python Online-trsaltion.py --dry-run

# Translate only specific languages
python Online-trsaltion.py --target-langs fr,es,de

# Verbose output with detailed progress
python Online-trsaltion.py --verbose

# Translate from different source language
python Online-trsaltion.py --source-lang en --target-langs zh,ja,ko
```

**Advanced options:**
```bash
# Use custom CSV file
python Online-trsaltion.py --file path/to/custom.csv

# Create backup before changes
python Online-trsaltion.py --backup

# Combine multiple options
python Online-trsaltion.py --verbose --dry-run --target-langs fr,es
```

### 2. Missing Translation Fixer (`fix-missing-translations.py`)

Scans Dart code for missing translation keys and fixes them.

**Basic usage:**
```bash
# Scan and report issues only
python fix-missing-translations.py

# Fix missing .tr() calls and add keys to CSV
python fix-missing-translations.py --fix

# Fix issues and run online translation
python fix-missing-translations.py --fix --translate

# Verbose output
python fix-missing-translations.py --verbose
```

**What it does:**
- Finds translation keys used without `.tr()` suffix
- Identifies hardcoded strings that should be translation keys
- Verifies all used translation keys exist in CSV
- Automatically fixes missing `.tr()` calls
- Adds missing keys to CSV file

### 3. Translation Generator (`generate-translations.py`)

Converts the main CSV file to individual JSON files for each language.

**Usage:**
```bash
python generate-translations.py
```

**Output:**
- Creates `generated/` directory
- Generates `{language}.json` for each language column
- Used by Flutter app for localization

### 4. Duplicate Remover (`remove_duplicates.py`)

Removes duplicate entries from the translation CSV file.

**Usage:**
```bash
python remove_duplicates.py
```

**Features:**
- Detects duplicate English text entries
- Removes duplicate rows (keeps first occurrence)
- Creates automatic backup
- Provides detailed removal report

## 🔄 Complete Translation Workflow

### For New Translations:

1. **Add new translation keys to your Dart code:**
   ```dart
   Text("new-feature-title".tr())
   ```

2. **Find and fix missing translations:**
   ```bash
   python fix-missing-translations.py --fix
   ```

3. **Auto-translate missing entries:**
   ```bash
   python Online-trsaltion.py --verbose
   ```

4. **Generate JSON files:**
   ```bash
   python generate-translations.py
   ```

### For Maintenance:

1. **Remove duplicates:**
   ```bash
   python remove_duplicates.py
   ```

2. **Check for issues:**
   ```bash
   python fix-missing-translations.py --verbose
   ```

3. **Update translations:**
   ```bash
   python Online-trsaltion.py --target-langs fr,es,de
   ```

4. **Regenerate JSON files:**
   ```bash
   python generate-translations.py
   ```

## 🛠️ Troubleshooting

### Common Issues:

**"Module not found" errors:**
```bash
pip install pandas deep-translator
```

**CSV encoding issues:**
- Ensure CSV file is saved with UTF-8 encoding
- Check for special characters or malformed rows

**Translation API errors:**
- Check internet connection
- Reduce batch size with smaller `--target-langs` lists
- Add delays between requests

**Permission errors:**
- Ensure write permissions to translation files
- Close CSV file if open in Excel/editor

### File Locations:

- Main CSV: `assets/translations/translations.csv`
- Generated files: `assets/translations/generated/*.json`
- Backups: `assets/translations/translations.csv.backup_*`
- Reports: `assets/translations/hardcoded-strings-report.txt`

## 📊 Language Support

The tools support all languages defined in the CSV file, including:
- English (en), French (fr), Spanish (es), German (de)
- Chinese Simplified (zh), Chinese Traditional (zh-Hant)
- Japanese (ja), Korean (ko), Arabic (ar), Hindi (hi)
- And many more...

## 🔧 Configuration

### Language Code Mapping:

The tools automatically map CSV column headers to Google Translate language codes:
- `zh` → `zh-cn` (Chinese Simplified)
- `zh-Hant` → `zh-tw` (Chinese Traditional)
- `fil` → `tl` (Filipino/Tagalog)
- `he` → `iw` (Hebrew)

### Custom Settings:

Edit the Python files to customize:
- Translation delays (rate limiting)
- Language mappings
- File paths
- Output formats

## 📝 Best Practices

1. **Always backup** before running translation tools
2. **Use dry-run** first to preview changes
3. **Review translations** before committing
4. **Run tools in order**: fix → translate → generate
5. **Test generated files** in the Flutter app
6. **Keep CSV file clean** by removing duplicates regularly

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Ensure all prerequisites are installed
3. Verify file permissions and paths
4. Check CSV file format and encoding
