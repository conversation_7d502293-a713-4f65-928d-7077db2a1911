{"logs": [{"outputFile": "dev.fluttercommunity.plus.share.share_plus-release-5:/values-fa/values-fa.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}}]}, {"outputFile": "dev.fluttercommunity.plus.share.share_plus-mergeReleaseResources-3:/values-fa/values-fa.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}}]}]}