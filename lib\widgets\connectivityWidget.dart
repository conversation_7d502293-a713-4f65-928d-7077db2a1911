import 'dart:async';
import 'dart:io' if (dart.library.html) 'dart:html' as html;
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:budget/main.dart';
import 'package:budget/struct/settings.dart';
import 'package:budget/widgets/openSnackbar.dart';
import 'package:easy_localization/easy_localization.dart';

import 'globalSnackbar.dart';

/// A safe connectivity checker that avoids Platform conflicts on web
class ConnectivityWidget {
  static final ConnectivityWidget _instance = ConnectivityWidget._internal();
  factory ConnectivityWidget() => _instance;
  ConnectivityWidget._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isOnline = true;
  bool get isOnline => _isOnline;

  final StreamController<bool> _connectivityStreamController =
      StreamController<bool>.broadcast();
  Stream<bool> get onConnectivityChanged => _connectivityStreamController.stream;

  /// Initialize connectivity monitoring
  Future<void> initialize() async {
    // Skip initialization during debug mode on web to prevent issues
    if (kDebugMode && kIsWeb) {
      print("ConnectivityWidget: Skipping initialization in web debug mode");
      return;
    }

    await _checkConnectivity();
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((_) {
      _checkConnectivity();
    });
  }

  /// Check current connectivity status
  Future<bool> checkConnection() async {
    return await _checkConnectivity();
  }

  /// Internal connectivity check with platform-safe handling
  Future<bool> _checkConnectivity() async {
    try {
      // Skip during debug mode on web
      if (kDebugMode && kIsWeb) {
        _isOnline = true;
        return true;
      }

      bool wasOnline = _isOnline;
      final connectivityResult = await _connectivity.checkConnectivity();
      
      if (connectivityResult == ConnectivityResult.none) {
        _isOnline = false;
      } else if (kIsWeb) {
        // For web, just rely on connectivity_plus result
        _isOnline = true;
      } else {
        // For mobile/desktop, do additional verification
        _isOnline = await _performInternetCheck();
      }

      // Handle connectivity changes
      if (wasOnline != _isOnline) {
        await Future.delayed(const Duration(milliseconds: 300));
        if (wasOnline != _isOnline) {
          _handleConnectivityChange();
          _connectivityStreamController.add(_isOnline);
        }
      }

      return _isOnline;
    } catch (e) {
      print("ConnectivityWidget: Error checking connectivity: $e");
      return _isOnline; // Return last known state
    }
  }

  /// Platform-safe internet connectivity check
  Future<bool> _performInternetCheck() async {
    if (kIsWeb) return true;
    
    try {
      // Use conditional compilation to avoid Platform conflicts
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      print("ConnectivityWidget: Internet check failed: $e");
      return false;
    }
  }

  /// Handle connectivity state changes
  void _handleConnectivityChange() {
    try {
      updateSettings("isOfflineMode", !_isOnline, updateGlobalState: true);
      
      if (_isOnline) {
        // Going online
        updateSettings("skipTokenValidation", false, updateGlobalState: true);
        updateSettings("pauseBackgroundSync", false, updateGlobalState: true);
        updateSettings("disablePeriodicConnectivityChecks", false, updateGlobalState: true);
        
        if (entireAppLoaded) {
          showOnlineSnackbar();
        }
      } else {
        // Going offline
        updateSettings("skipTokenValidation", true, updateGlobalState: true);
        updateSettings("pauseBackgroundSync", true, updateGlobalState: true);
        updateSettings("disablePeriodicConnectivityChecks", true, updateGlobalState: true);
      }

      if (kDebugMode) {
        print("ConnectivityWidget: Connectivity changed to ${_isOnline ? 'ONLINE' : 'OFFLINE'}");
      }
    } catch (e) {
      print("ConnectivityWidget: Error handling connectivity change: $e");
    }
  }

  /// Show online connectivity snackbar
  void showOnlineSnackbar() {
    if (!entireAppLoaded) return;
    
    openSnackbar(
      SnackbarMessage(
        title: "online-mode".tr(),
        icon: Icons.cloud_done,
        timeout: const Duration(milliseconds: 2500),
      ),
    );
  }

  /// Show offline connectivity snackbar
  void showOfflineSnackbar() {
    if (!entireAppLoaded) return;
    
    openSnackbar(
      SnackbarMessage(
        title: "offline-mode".tr(),
        icon: Icons.cloud_off,
        timeout: const Duration(milliseconds: 2500),
      ),
    );
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityStreamController.close();
    if (kDebugMode) {
      print("ConnectivityWidget: Disposed");
    }
  }
}

/// Simple connectivity check function for use throughout the app
Future<bool> checkInternetConnection() async {
  try {
    // Skip during debug mode on web to prevent issues
    if (kDebugMode && kIsWeb) {
      return true;
    }

    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      return false;
    }

    // For mobile/desktop platforms, do a simple connectivity check
    if (!kIsWeb) {
      try {
        final result = await InternetAddress.lookup('google.com');
        return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      } catch (e) {
        return false;
      }
    } else {
      // For web platform, just rely on connectivity_plus result
      return true;
    }
  } catch (e) {
    print("Error checking connectivity: $e");
    return false;
  }
}

/// Widget that provides connectivity status to its children
class ConnectivityProvider extends StatefulWidget {
  final Widget child;
  final bool enablePeriodicChecks;
  final Duration checkInterval;

  const ConnectivityProvider({
    super.key,
    required this.child,
    this.enablePeriodicChecks = true,
    this.checkInterval = const Duration(seconds: 30),
  });

  @override
  State<ConnectivityProvider> createState() => _ConnectivityProviderState();
}

class _ConnectivityProviderState extends State<ConnectivityProvider> {
  Timer? _connectivityTimer;
  late StreamSubscription<bool> _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    
    // Initialize connectivity widget
    ConnectivityWidget().initialize();
    
    // Listen to connectivity changes
    _connectivitySubscription = ConnectivityWidget().onConnectivityChanged.listen((isOnline) {
      if (mounted) {
        setState(() {});
      }
    });

    // Start periodic checks if enabled
    if (widget.enablePeriodicChecks) {
      _startPeriodicChecks();
    }
  }

  void _startPeriodicChecks() {
    _connectivityTimer = Timer.periodic(widget.checkInterval, (timer) {
      if (mounted) {
        _checkConnectivity();
      } else {
        timer.cancel();
      }
    });
  }

  void _checkConnectivity() async {
    try {
      // Skip if disabled or in debug mode on web
      if (kDebugMode && kIsWeb) return;
      
      bool disableChecks = appStateSettings["disablePeriodicConnectivityChecks"] ?? false;
      if (disableChecks) return;

      await ConnectivityWidget().checkConnection();
    } catch (e) {
      print("ConnectivityProvider: Error checking connectivity: $e");
    }
  }

  @override
  void dispose() {
    _connectivityTimer?.cancel();
    _connectivitySubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
