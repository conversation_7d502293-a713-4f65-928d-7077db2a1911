@echo off
echo ========================================
echo    Budget App - Web Local Server
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter and add it to your PATH
    pause
    exit /b 1
)

echo Flutter version:
flutter --version
echo.

REM Clean previous builds
echo Cleaning previous builds...
flutter clean
echo.

REM Get dependencies
echo Getting dependencies...
flutter pub get
echo.

REM Check for Chrome
echo Checking for Chrome browser...
flutter devices | findstr "Chrome" >nul
if %errorlevel% neq 0 (
    echo WARNING: Chrome browser not detected
    echo Make sure Chrome is installed and available
    echo.
)

REM Build web version first
echo Building web version for production...
flutter build web --release
if %errorlevel% neq 0 (
    echo ERROR: Web build failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.

REM Check if Python is available for serving
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting local server using Python on localhost:8080...
    echo.
    echo Open your browser and go to: http://localhost:8080
    echo.
    echo BRAVE BROWSER USERS:
    echo - If the page doesn't load, disable Brave Shields for localhost
    echo - Click the shield icon in the address bar and turn off shields
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    cd build\web
    python -m http.server 8080
    cd ..\..
) else (
    REM Check if Node.js is available
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo Starting local server using Node.js on localhost:8080...
        echo.
        echo Open your browser and go to: http://localhost:8080
        echo.
        echo BRAVE BROWSER USERS:
        echo - If the page doesn't load, disable Brave Shields for localhost
        echo.
        echo Press Ctrl+C to stop the server
        echo.
        npx serve build\web -p 8080
    ) else (
        echo.
        echo Build completed! Files are in: build\web\
        echo.
        echo To serve the files, you need either Python or Node.js:
        echo 1. Python: python -m http.server 8080 (from build\web\ directory)
        echo 2. Node.js: npx serve build\web -p 8080
        echo.
        echo Then open http://localhost:8080 in your browser
        echo (For Brave: disable Shields if needed)
        echo.
    )
)

echo.
echo Web server stopped.
pause
