@echo off
echo ========================================
echo    Budget App - Web Local Server
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter and add it to your PATH
    pause
    exit /b 1
)

echo Flutter version:
flutter --version
echo.

REM Clean previous builds
echo Cleaning previous builds...
flutter clean
echo.

REM Get dependencies
echo Getting dependencies...
flutter pub get
echo.

REM Check for Chrome
echo Checking for Chrome browser...
flutter devices | findstr "Chrome" >nul
if %errorlevel% neq 0 (
    echo WARNING: Chrome browser not detected
    echo Make sure Chrome is installed and available
    echo.
)

REM Build web version first (optional - comment out if you want faster startup)
echo Building web version...
flutter build web
if %errorlevel% neq 0 (
    echo WARNING: Web build failed, but continuing with debug mode...
    echo.
)

echo.
echo ========================================
echo Starting web server on localhost:8080
echo ========================================
echo.
echo The app will open in your default browser
echo Press Ctrl+C to stop the server
echo.

REM Run the web server
flutter run -d chrome --web-port=8080 --web-hostname=localhost

echo.
echo Web server stopped.
pause
