{"logs": [{"outputFile": "dev.fluttercommunity.plus.share.share_plus-release-5:/values-kk/values-kk.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}}]}, {"outputFile": "dev.fluttercommunity.plus.share.share_plus-mergeReleaseResources-3:/values-kk/values-kk.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}}]}]}