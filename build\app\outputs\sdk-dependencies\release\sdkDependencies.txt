# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.0"
  }
  digests {
    sha256: "\326\371\033{\0170l\312)\237\354t\373|4\344\207Mo^\305\271%\240\264\336!\220\036\021\234?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.0"
  }
  digests {
    sha256: "\267\227\232z\254\224\005_\r\237\037\323\264|\345\377\341\313`2\250B\272\237\276q\206\360\205(\221x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.0"
  }
  digests {
    sha256: "\245\237\242O\337\037\373YK\256\315\277\017\321\000\020\371w\316\241\0026\324\207\3764d\227zsw\372"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.1.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "32.1.3-android"
  }
  digests {
    sha256: " \346\254\211\002\335\364\236x\006\314p\363\005L\215\221\254\313^\357\334\020\363 ~\200\340\2436\262c"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.37.0"
  }
  digests {
    sha256: "\344\316\023v\314\'5\341\335\342 \266*\320\221?Q)w\004\332\255\025Z3\363\206\274]\260\331\367"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.36.0"
  }
  digests {
    sha256: "wD\016\'\v\v\311\242I\220<Z\al6\247\"\304\210l\244\364&u\362\220:\034S\355a\245"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.11.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "22.4.0"
  }
  digests {
    sha256: "\256\361,\0355\271$\"q>E\366\360P\036\303\024\372\221t\301\224\250}\227\361\343\336\200\222\370\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "22.4.0"
  }
  digests {
    sha256: "\207\312\312\260e\236\221\362\316C[I\232\006\f\375\n\200t\350\302Z\212z~\237\242\276>G\aN"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.15.0"
  }
  digests {
    sha256: "C+\205\241\227@v\341KH~\316J(\305\232\204\361\271\357\303\374\213\347,\327\360]2\005^Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\270\353\231}\3244\327\016\271|\275m\023\225\2663|gqq|z\315Y\361\034\bS\363\206\002\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.7"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.5.0"
  }
  digests {
    sha256: "\243\337n\373)\276\262\377x\373|\336q*\307s\275l\334\337\311\203<\220\030\225\200O`\234\231^"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.7.1"
  }
  digests {
    sha256: "}\336\276\235\203\n\004\22673\334\263@\312\264\272\301\346\235\374\247\227\246\325\232\247\274V(!P\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.9.3"
  }
  digests {
    sha256: "~E\322\002=\301}q_\377\363\334W\222\367\230d,J\341\034\224\200\006\261u*\271\355\332W\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.7"
  }
  digests {
    sha256: "C\322\212lm\251\301\313r\277\265\314\033Q\032y7\262\333\213y\325/7\320\267\361Ly\260\220\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\347\347\004?f\260wcuc\377VJ\177\306\316\344a\322\204\240\362H\\\005\n!C\253\275\211 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.8.7"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.7"
  }
  digests {
    sha256: "\327\206\345\346\367\233\217\253\002o\t\242\324\256=\341\327(u\367dZk{\344\217\035\232\263\204sK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\266\347\205\350S+\025Y!1\005c\251\017\322b\265\332\317id\256\352l\273\344\022\254\035\245\224`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.8.7"
  }
  digests {
    sha256: "p64RZg\261\306\276\252\253O\230\r\372\v\254\337~\024=\v]Q\365\254\r\224\004@\002\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.7"
  }
  digests {
    sha256: "b\340v\242\336-e\n#\277yIT\031\370\2445z\330\351\262\215\200\023\030\320\374\335n5\372\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\032\316\273P\337/\f\a\363y\325\342:(\367yOp\357\262\266\3104\254\353\350\303\224xN\346\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\263\323\234]\272^ ~\364\307\235I\032vP\001\224\305\352ht\025\177\005\241_\001y,\f\304\347"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.8.7"
  }
  digests {
    sha256: "\024P\347.\352.\310\b\260\211\271j\275\307s\312\003\244\024>&\177z\255\331\256\271\247\303\232\376\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.7"
  }
  digests {
    sha256: "do*B\322\210K\0020\217\261\2307\b\236&i\203_\342\034\246\245\266\006f\325\353\224\320\201\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.15.0"
  }
  digests {
    sha256: "\307,\227\377J20\213\312\363\006\036\352]\3373E\\\344\023\316\344\252\035\\\202D\fWY\324\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.9.3"
  }
  digests {
    sha256: "\342\3324\366\214\254Uy\206(\001\230\345\301Tk\212$Ri\260P\261\314\220\265i\026\333\366\222\001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.1.0"
  }
  digests {
    sha256: "\250;Y|\322@\235\301w\367\3712\367\304\b.\367\265P\213i\266\304\272q`~u\333\331\231q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.7.1"
  }
  digests {
    sha256: "\217\254\271\a\030\317R^\257\376m\033\326\024]\377\025mj\345\035<\037\202\002\315\005\216\370\201\366*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "22.4.0"
  }
  digests {
    sha256: "\245@4%\257\333\231\271\267\ry\020\336~\211l\235QH\001b\207m\246\227S\227\376y\331\203\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "22.4.0"
  }
  digests {
    sha256: "\327\000*\357A\362E\351}\'f\303\246m\312\031\352\vL\375\217$l;\212~\t\300\r\322|K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "T\nn-\307\2167\006\025\367\177\207,\017;p\n\032\312\035x\244\251\257}(\b\005\bH\306c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "\262\210]\265\335\255\233E\177\224\322\020A\332S&}\250\232\337\3448$+\240}\321\034\217\246>t"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "22.4.0"
  }
  digests {
    sha256: "\276\004\002 $\336?\273/\231\227\033\364\2144\341\324\r\226S\311T\3567\301\361@s\320\205\247\316"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "22.4.0"
  }
  digests {
    sha256: "}\337\a\f\351FP\0166;\304\rV6\355\206P\256\365\232\003\251\3768|<\356\375\265\271\373\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "22.4.0"
  }
  digests {
    sha256: "\313\202\273_v\tQ\177\352\002 \3263\220\006\375]p\317\300\260\224\256\225\003\340\234\214{\002\331\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-firestore"
    version: "25.1.3"
  }
  digests {
    sha256: "\374\376\'\304t~\311\326\021\261h\216\027\366\264\317,\277\263\035\327v\341\260\233\037\252\347\246;\377\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "protolite-well-known-types"
    version: "18.0.1"
  }
  digests {
    sha256: "\355\327\206\202D\343\366x\226\357\260\304\306\274\233N\212\262W\v0\217E\312\376G\352\210x\225\346q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.protobuf"
    artifactId: "protobuf-javalite"
    version: "3.25.5"
  }
  digests {
    sha256: "y\243\377Q\254*\213\033\347\377\372\"\204\203\326It\361\177>\377\377\331f\260T\330qy\344V4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.0.0"
  }
  digests {
    sha256: "\375y\334\340.\305\033\223\037\307\265\307,7\022\210\322F\b_\324\027]\272\340l\370\347vr|k"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-android"
    version: "1.62.2"
  }
  digests {
    sha256: "%D\222\016\245\364g \334\367^\202\000\243t\320\323v\375\310\362\224({\265yu\272\263\202\2469"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-api"
    version: "1.62.2"
  }
  digests {
    sha256: ".\211iD\317Q>\016\\\3752\274\327,\211`\032\'\306\312V\221o\204\262\017:\023\272\317\033\037"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-core"
    version: "1.62.2"
  }
  digests {
    sha256: "\030C\231\002\304s\242\301Q\036Q}\023\270\256ycx\205\n\216\332Cx|k\247x\372\220\374\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.12.0"
  }
  digests {
    sha256: "M7\177Q\310K\265\264\345`;\336\345\376\177\374\232\266\267\256\344\356\016\037b\306\372\335\204\305\372\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android"
    artifactId: "annotations"
    version: "4.1.1.4"
  }
  digests {
    sha256: "\272sN\036\204\300\235aZ\366\240\2353\003KO\004B\370w-\354\022\016\3737m\206\245e\256\025"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.codehaus.mojo"
    artifactId: "animal-sniffer-annotations"
    version: "1.23"
  }
  digests {
    sha256: "\237\376Rk\364:cH\351\330\263;\234\326\365\200\247\365\356\320\317\005Y\023\000~\332&=\351t\320"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.perfmark"
    artifactId: "perfmark-api"
    version: "0.26.0"
  }
  digests {
    sha256: "\267\322>\223\243E7\3163\'\b&\232\r\024\004x\212[^\031I\350/U5\374\345\033>\251["
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-context"
    version: "1.62.2"
  }
  digests {
    sha256: "\231Yt}\366\247S\021\236\034\032=\377\001\252vm$U\365\344\206\n\312\243\0055\236\035S:\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-okhttp"
    version: "1.62.2"
  }
  digests {
    sha256: "\236\220?\375+0\322\373{T\312Mr\221[\032\263[\r\"\274\220\230V[\016\217B(\325\232\376"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-util"
    version: "1.62.2"
  }
  digests {
    sha256: "<q\003\346\363s\205q\343\256\332B\017\342\246\254h\343TSM\213f\364\030\227\266u[H\2675"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.4.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "\0019\354zPm\273\325L\255b)\033\001\234\270PSK\340\227\310\306l\020\000\325\373\350\355\357>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-protobuf-lite"
    version: "1.62.2"
  }
  digests {
    sha256: "y\231y\211\250\302\265\277M\321\201\202\242\337./f\207\003\326\213\247\303\027\347\240x\t\323?\221\364"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-stub"
    version: "1.62.2"
  }
  digests {
    sha256: "\373L\246y\244!AC@le\254Ag\262\265\342\356,\253\037\301\001Vk\261\304i]\020^6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "23.2.0"
  }
  digests {
    sha256: "L\023\360\373\000\303z\373f\344\313\307]h\250\n\322\327=g\247\350[\r\244\225\200\250!\273\f9"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "I\217e\033O\221f\277w\017l\251z\353\250\351!\356\301\001\263\337\370\331\360\305\032\310\366\344\002\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials-play-services-auth"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "X0\246A8N\227\226A[0\363M\302Px-x\b\020\034\366\22264`\33199\240 \311"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.0.0"
  }
  digests {
    sha256: "\002\271\240\234N\f\261z\r\211<\277@\3741\323p\2262a\233\v\375%\036\\Q\343\347\256\327\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.1.0"
  }
  digests {
    sha256: "\222r:8\344\r:\312\037\2365\021YR\231\220!\251/\254$\2371a\3552\223\2214r\251\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.libraries.identity.googleid"
    artifactId: "googleid"
    version: "1.1.0"
  }
  digests {
    sha256: "\031J\301\374\031\206\335\037b\004o\2567\335\367~cw\017\334\037=4\272\2529|\373\364\321\221\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.4.0"
  }
  digests {
    sha256: "\377~ D\005\031\302\211\335\264\316\006=\334\000\003\303\376s\254 -\350\002T\324\277\272\372\341$H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.4"
  }
  digests {
    sha256: "6@\b$\001O\306\035\223y\261=\321\253\023\366QHG\373\0044|\017\337\346\030\272\316x\363\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.7.0"
  }
  digests {
    sha256: "\001\377\226\233\305\216\310\211\211%.\340t\333\313N\b\232u^S\022:k\300!E\th\205\257\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.security"
    artifactId: "security-crypto"
    version: "1.1.0-alpha06"
  }
  digests {
    sha256: "\227a\021\027\v?\023\322\360\371E}\237\237W\223G\024<\266\\\005Be\220m3\307a\212L*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.crypto.tink"
    artifactId: "tink-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\324\233\335\232\302I\340\232q}Q\337\243jT\377%\360\237\371\016\017\2740\237S\207\236i\313x\211"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-recaptchabase"
    version: "16.1.0"
  }
  digests {
    sha256: "\221G\314\031\235>M\241:w\222\257\306\035\222B\264\305\274y:,\237\375\"\211\260^\022\234\n\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "armeabi_v7a_release"
    version: "1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea"
  }
  digests {
    sha256: "/\306\030[\301\242\255\334\025\206\222\310 \264kr\0060]\177\034\223_]\354t\325\001\316VCA"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "arm64_v8a_release"
    version: "1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea"
  }
  digests {
    sha256: "\217W\332eR\316\001\363\304\372x\221\206\004\333\212\aN\216(\273J\345\'\345\203\021l\206q\037Z"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "x86_64_release"
    version: "1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea"
  }
  digests {
    sha256: "P-B\371\001\363\aJ\371\377\340! \356\240\211\272\310\3634\351A\020\322\266\030L\235\310ep\255"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.microg"
    artifactId: "safe-parcel"
    version: "1.7.0"
  }
  digests {
    sha256: "b\237^\277\333Px\326y\024!\2434\232\331\267\321\251\377m\b\314\023\266\241{\377\276\217\227V\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "flutter_embedding_release"
    version: "1.0.0-109150893958777c8f2215f6cfd3e89e984e8dea"
  }
  digests {
    sha256: "\f\321\'\\\201\243\357\327]P\261P\310\252_(n\305\005\004\017\235|*\253\340G\314\276\315\222%"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window-java"
    version: "1.2.0"
  }
  digests {
    sha256: "\343\336\373^\343\205wWP]z\326bu\334:\v\001Ai0\033\211\326\344\356\300;\016E\274\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.2.0"
  }
  digests {
    sha256: "\2504\302\027\331s\021\'wS=\257\022\201\332\263;\327x\372\335-f\276\2749\265\376\201\250\220\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window.extensions.core"
    artifactId: "core"
    version: "1.0.0"
  }
  digests {
    sha256: "1\000\205K-\300\336\\M\210\264~\034]\263\b\001w\f\202\035\312(\030\224C\267bO@\b\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.getkeepsafe.relinker"
    artifactId: "relinker"
    version: "1.4.5"
  }
  digests {
    sha256: "\260;M\021:\237\357y@\n\350T\301wW\302\221W%\346\377\321\326\021\026\352\332\353V\0237J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.tika"
    artifactId: "tika-core"
    version: "3.1.0"
  }
  digests {
    sha256: "#\2537\350\346\315\227\374w\367\256q\374\031\357\022\256K\221\267\246\251\022\r\360E\2721\374\327\320C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "2.0.16"
  }
  digests {
    sha256: "\241%x\335\341\272\000\275\233\201m8\212\v\207\231(\320\v\253<\203\302@\367\001;\364\031lW\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.18.0"
  }
  digests {
    sha256: "\363\312\017\215c\304\016#\245mT\020\034`\325\355\356\023kB\330K\373\205\274yc\t1\t\317\213"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.albfernandez"
    artifactId: "juniversalchardet"
    version: "2.5.0"
  }
  digests {
    sha256: "\316\262qe>\331\236\025\377\345.J\355\354\336\370\221\2044\361\232Cx\246\177~\276\016\250C\220X"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.1.0"
  }
  digests {
    sha256: "\337\2735\272\247\037\263\305\272;\367\363}\006\314~\233\346\203\f\'n$0\032RY\"\246\341C\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.glance"
    artifactId: "glance-appwidget"
    version: "1.0.0"
  }
  digests {
    sha256: "W\r p\274\330I\345e\271\002]pRU\244\3461\205o0!\347CV?\345\257sCW\267"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.2.1"
  }
  digests {
    sha256: "u|\306L\314?u\273c\177U\261H\254`\320\016\216\003UU\315Y\027\265\207\033\265\223M\246I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.1.1"
  }
  digests {
    sha256: "\341q\235\215\267T^\177;\343\272\364*\301\206_\261\314\243e%\331\270\211`\036,/N\243\252\374"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.1.1"
  }
  digests {
    sha256: "5\261\330\367\364`\207M\336\t\375~c\f\324@R\2216\225)\200v\374b\336\221\244\376\023E\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.1.1"
  }
  digests {
    sha256: "O\0364\256QZ\370\300Nx2u\335\355P\274\215\025a5\37459\177\261\310\032\236\001\361s\263"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.1.1"
  }
  digests {
    sha256: "VB\002\201\362\036(\210\024^\311C\214\244\2462\001\'\222\001;f}\251\252\003\352s>H.\226"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-remoteviews"
    version: "1.0.0"
  }
  digests {
    sha256: "\234\266a\006\376\245b\024)\253c|\211\034d\341(\211\307\026\241g!l\316@\242\370\230\371\t\367"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-android"
    version: "1.1.3"
  }
  digests {
    sha256: "z\235o`!\215w_\3549\250X@C5\235\373jcZa\326\206\027\252n\230&\330\232\001;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\260\257x\371\b\203@z\346?\321HW\304\346.w\037>\236?\202\340\227q[M\2518\215\340\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: ")\025\006\264\374\343\230$\227\223\343\' v5\275M\362Q\201\331S\230\b\327\030\032J\275\bu\365"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\220\333\\\004\261Ho\255*\017\342\220 \3769D\300\372\f]6\361A\036\336\241\231\000t*E\023"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: "\3139\341\";C$\3270\305s\242\274\352:50\236\275\223X\373\3460\211\227\005\243\373\312\312\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-proto"
    version: "1.1.3"
  }
  digests {
    sha256: "chja/9\270\312`;\353\3437mU\373l\236\342\212JQs\357\244\234\211\313\314\250\241\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-external-protobuf"
    version: "1.1.3"
  }
  digests {
    sha256: "\374\263\363s\317t4&\310^\273\r<\306D\256\372\362o9\267\372U\273\220#\215A\266\211\354\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.glance"
    artifactId: "glance"
    version: "1.0.0"
  }
  digests {
    sha256: "\366\323\034\317\334-\331\361\265\342\017\302\237j\230\320L\312W\333{v\241\220@\256\251\v\375\001\214\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.7.1"
  }
  digests {
    sha256: "u\177\237\313\366\1778\276\200\303\002j\371\a\274\361UY\370;\260\343\\\302\035Ax\203\031p\a["
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.2.5"
  }
  digests {
    sha256: "$\245T\233ynC\3437Q=)\b\255\254g\364SP\331\251\v\312~.a i!@\273\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.2.5"
  }
  digests {
    sha256: "+\023\r\324\241\323\331\033g\001\3553\tm8\237\001\304\374\021\227\247\254\326\271\027$\335\305\254\374\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.1.0"
  }
  digests {
    sha256: "\206ss\177\333.\373\255\221\256\256\355\031\'\353\262\222\022\323j\206}\223\271c\234\200i\001\237\212\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.1.0"
  }
  digests {
    sha256: "\203A\377\t-``\326*\a\"\177)#qU\377\363o\261o\226\311_\275\232\210N7]\271\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.7.1"
  }
  digests {
    sha256: "\0270\357\316\211\022(q\217zN\215\231\245\316\244\222\276\303a\306u\313\327\202\316/\373_\2427\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.7"
  }
  digests {
    sha256: "\016\217\0302&l[\006g\255=;\020\230\346$\344\232\t\aT\223\240\024\247\350\212\360\037\323\n\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.android.billingclient"
    artifactId: "billing"
    version: "7.1.1"
  }
  digests {
    sha256: "\254\0352\372\000w\301\032\322b\335\004\215\336C&\037\303\005\220@\361R\005\215!\311F\003\271e\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.0.0"
  }
  digests {
    sha256: "Ni\203\300p;5}\366\361\306\316\254\261\265\337\302\305\000jx\234y\237\354\"\230\262\2653tf"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.8"
  }
  digests {
    sha256: "\341~\335\036\367\375G\\\220\272\244\343\224\"3/\'\b}4\274\264l\264\214\350j\371\245Ja."
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.8"
  }
  digests {
    sha256: "\313\223S\357\027\221\256\027\t}\207\214\247\021\342Z\2342\316\311\004*\334I\260\f\255\376\341\247)\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-location"
    version: "19.0.0"
  }
  digests {
    sha256: "k \\C\272]\367Q\354\250\316\235\256zX\357\372\372\307\3267\373O\307\b\247R-\033\231\317\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-places-placereport"
    version: "17.0.0"
  }
  digests {
    sha256: ",\177\326:\320/(\025\n\344\377\344a]\254}iMy\016,Fg\367w\256\334\216\340T\351)"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "review"
    version: "2.0.2"
  }
  digests {
    sha256: "\026\002\002fx#\035\272\017t\202\321$NE\377\203\331\377\"\374\213mk\350\226\022(\254z2\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.biometric"
    artifactId: "biometric"
    version: "1.1.0"
  }
  digests {
    sha256: "\'\f{}\231\224-^\301\335\210YNFH\376\263=\2161\330\303\302\253#!\324\235\232\275\374\037"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.2.0"
  }
  digests {
    sha256: "=!1\245Za\247w2.!&\340\001\200\021\357\2463\236S\264AS\353e\033\026\002\f\312p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.2.0"
  }
  digests {
    sha256: "\304p)|\003\377=\341\303\321]\254\360\276\f\256c\253\301\vR\360!\335\a\256(\332\243\020\017\345"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.0.0"
  }
  digests {
    sha256: "\006\225o\261\254\001@\'\312\235+@F\232KB\252a\264\225{\261\030H\341\3775\'\001\253EH"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\345\b\306\225H\224\2237M\224+\367\264\356\002\253\367W\035%\252\304\306\"\345}l\325\315)\353s"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.4.1"
  }
  digests {
    sha256: "6\322\215\236\303:\214d18B\274\351\234\225sm\245\262zk:Q69\005\r\350/\aW&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "eu.simonbinder"
    artifactId: "sqlite3-native-library"
    version: "3.49.1+1"
  }
  digests {
    sha256: "kC9\'bW,\341BH\255Dc\352J\220>0\252\341\266\350\3312HP\313\235\272ym\212"
  }
  repo_index {
    value: 1
  }
}
library {
  digests {
    sha256: "\315\353\237\336\026\207\227dn,\311P\3749\262\322i\2035\037\332&\351\341\300\234\374\241\260$\362G"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 10
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
  library_dep_index: 84
  library_dep_index: 104
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 119
}
library_dependencies {
  library_index: 12
  library_dep_index: 13
  library_dep_index: 74
  library_dep_index: 83
}
library_dependencies {
  library_index: 13
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 66
  library_dep_index: 36
  library_dep_index: 67
  library_dep_index: 68
  library_dep_index: 73
  library_dep_index: 72
}
library_dependencies {
  library_index: 14
  library_dep_index: 15
}
library_dependencies {
  library_index: 15
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 18
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 0
}
library_dependencies {
  library_index: 18
  library_dep_index: 14
  library_dep_index: 14
}
library_dependencies {
  library_index: 19
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 63
  library_dep_index: 58
  library_dep_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 20
  library_dep_index: 16
  library_dep_index: 21
  library_dep_index: 14
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 47
  library_dep_index: 62
  library_dep_index: 0
  library_dep_index: 53
}
library_dependencies {
  library_index: 21
  library_dep_index: 0
}
library_dependencies {
  library_index: 22
  library_dep_index: 16
  library_dep_index: 7
}
library_dependencies {
  library_index: 23
  library_dep_index: 16
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 16
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 33
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 43
  library_dep_index: 48
  library_dep_index: 44
  library_dep_index: 51
  library_dep_index: 49
}
library_dependencies {
  library_index: 26
  library_dep_index: 16
}
library_dependencies {
  library_index: 27
  library_dep_index: 16
  library_dep_index: 26
}
library_dependencies {
  library_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 52
}
library_dependencies {
  library_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 32
  library_dep_index: 33
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 34
}
library_dependencies {
  library_index: 33
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 3
}
library_dependencies {
  library_index: 34
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 3
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 14
  library_dep_index: 20
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
  library_dep_index: 16
  library_dep_index: 21
  library_dep_index: 14
  library_dep_index: 53
  library_dep_index: 42
  library_dep_index: 24
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 58
  library_dep_index: 56
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 61
}
library_dependencies {
  library_index: 38
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 20
  library_dep_index: 24
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 54
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 57
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 48
  library_dep_index: 52
}
library_dependencies {
  library_index: 41
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 28
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 44
  library_dep_index: 51
  library_dep_index: 49
}
library_dependencies {
  library_index: 42
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 44
  library_dep_index: 51
  library_dep_index: 49
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 52
}
library_dependencies {
  library_index: 44
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 52
}
library_dependencies {
  library_index: 45
  library_dep_index: 16
  library_dep_index: 24
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 24
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 44
  library_dep_index: 51
  library_dep_index: 49
}
library_dependencies {
  library_index: 46
  library_dep_index: 16
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 16
}
library_dependencies {
  library_index: 48
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 33
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 39
  library_dep_index: 52
}
library_dependencies {
  library_index: 49
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 16
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 33
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 51
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 52
}
library_dependencies {
  library_index: 51
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 16
  library_dep_index: 53
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 33
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 44
  library_dep_index: 51
  library_dep_index: 49
}
library_dependencies {
  library_index: 53
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 20
}
library_dependencies {
  library_index: 54
  library_dep_index: 16
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 0
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 54
}
library_dependencies {
  library_index: 56
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 46
  library_dep_index: 7
}
library_dependencies {
  library_index: 57
  library_dep_index: 38
  library_dep_index: 53
  library_dep_index: 49
  library_dep_index: 48
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 38
}
library_dependencies {
  library_index: 58
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 14
}
library_dependencies {
  library_index: 59
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 14
}
library_dependencies {
  library_index: 61
  library_dep_index: 57
  library_dep_index: 18
  library_dep_index: 53
  library_dep_index: 37
  library_dep_index: 43
  library_dep_index: 48
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 37
}
library_dependencies {
  library_index: 62
  library_dep_index: 16
  library_dep_index: 14
}
library_dependencies {
  library_index: 63
  library_dep_index: 16
}
library_dependencies {
  library_index: 64
  library_dep_index: 16
}
library_dependencies {
  library_index: 65
  library_dep_index: 16
}
library_dependencies {
  library_index: 66
  library_dep_index: 36
}
library_dependencies {
  library_index: 67
  library_dep_index: 36
}
library_dependencies {
  library_index: 68
  library_dep_index: 14
  library_dep_index: 20
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 67
  library_dep_index: 72
  library_dep_index: 35
  library_dep_index: 5
}
library_dependencies {
  library_index: 69
  library_dep_index: 16
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 53
  library_dep_index: 69
  library_dep_index: 7
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 69
}
library_dependencies {
  library_index: 71
  library_dep_index: 14
  library_dep_index: 20
  library_dep_index: 37
  library_dep_index: 36
  library_dep_index: 35
}
library_dependencies {
  library_index: 72
  library_dep_index: 19
  library_dep_index: 36
}
library_dependencies {
  library_index: 73
  library_dep_index: 36
  library_dep_index: 67
}
library_dependencies {
  library_index: 74
  library_dep_index: 66
  library_dep_index: 36
  library_dep_index: 67
  library_dep_index: 73
  library_dep_index: 35
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 76
  library_dep_index: 80
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 75
  library_dep_index: 34
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 0
  library_dep_index: 36
  library_dep_index: 35
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
  library_dep_index: 16
  library_dep_index: 10
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 79
  library_dep_index: 75
  library_dep_index: 3
  library_dep_index: 76
  library_dep_index: 77
}
library_dependencies {
  library_index: 80
  library_dep_index: 35
  library_dep_index: 77
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 76
  library_dep_index: 81
  library_dep_index: 0
}
library_dependencies {
  library_index: 81
  library_dep_index: 35
  library_dep_index: 77
}
library_dependencies {
  library_index: 82
  library_dep_index: 36
  library_dep_index: 77
}
library_dependencies {
  library_index: 83
  library_dep_index: 14
  library_dep_index: 36
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
  library_dep_index: 35
  library_dep_index: 77
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 76
  library_dep_index: 89
  library_dep_index: 16
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 90
  library_dep_index: 98
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 0
  library_dep_index: 30
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 87
  library_dep_index: 71
  library_dep_index: 35
}
library_dependencies {
  library_index: 88
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 77
  library_dep_index: 75
}
library_dependencies {
  library_index: 89
  library_dep_index: 71
}
library_dependencies {
  library_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 5
}
library_dependencies {
  library_index: 91
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 5
}
library_dependencies {
  library_index: 92
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 93
  library_dep_index: 10
}
library_dependencies {
  library_index: 97
  library_dep_index: 91
}
library_dependencies {
  library_index: 98
  library_dep_index: 91
  library_dep_index: 99
  library_dep_index: 92
  library_dep_index: 100
  library_dep_index: 5
  library_dep_index: 96
}
library_dependencies {
  library_index: 99
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 95
  library_dep_index: 5
}
library_dependencies {
  library_index: 100
  library_dep_index: 101
}
library_dependencies {
  library_index: 101
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 102
  library_dep_index: 91
  library_dep_index: 86
  library_dep_index: 8
  library_dep_index: 5
}
library_dependencies {
  library_index: 103
  library_dep_index: 91
  library_dep_index: 5
  library_dep_index: 10
}
library_dependencies {
  library_index: 104
  library_dep_index: 105
  library_dep_index: 14
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 37
  library_dep_index: 64
  library_dep_index: 109
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 113
  library_dep_index: 115
  library_dep_index: 77
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 105
  library_dep_index: 16
  library_dep_index: 21
  library_dep_index: 14
  library_dep_index: 22
  library_dep_index: 20
  library_dep_index: 23
  library_dep_index: 7
}
library_dependencies {
  library_index: 106
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 107
}
library_dependencies {
  library_index: 107
  library_dep_index: 106
  library_dep_index: 108
  library_dep_index: 111
  library_dep_index: 112
  library_dep_index: 0
  library_dep_index: 106
}
library_dependencies {
  library_index: 108
  library_dep_index: 37
  library_dep_index: 58
  library_dep_index: 109
  library_dep_index: 110
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 111
  library_dep_index: 35
}
library_dependencies {
  library_index: 109
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 35
}
library_dependencies {
  library_index: 110
  library_dep_index: 14
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 35
}
library_dependencies {
  library_index: 111
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 35
}
library_dependencies {
  library_index: 112
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 113
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 114
}
library_dependencies {
  library_index: 115
  library_dep_index: 116
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 118
  library_dep_index: 35
  library_dep_index: 113
  library_dep_index: 0
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 116
  library_dep_index: 16
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 117
}
library_dependencies {
  library_index: 117
  library_dep_index: 16
  library_dep_index: 8
  library_dep_index: 93
  library_dep_index: 10
}
library_dependencies {
  library_index: 118
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 0
}
library_dependencies {
  library_index: 119
  library_dep_index: 16
}
library_dependencies {
  library_index: 124
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 16
  library_dep_index: 47
  library_dep_index: 20
  library_dep_index: 125
  library_dep_index: 128
}
library_dependencies {
  library_index: 125
  library_dep_index: 20
  library_dep_index: 126
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 126
}
library_dependencies {
  library_index: 126
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 20
  library_dep_index: 127
  library_dep_index: 0
  library_dep_index: 33
  library_dep_index: 125
}
library_dependencies {
  library_index: 127
  library_dep_index: 16
  library_dep_index: 0
}
library_dependencies {
  library_index: 129
  library_dep_index: 130
  library_dep_index: 131
}
library_dependencies {
  library_index: 133
  library_dep_index: 20
  library_dep_index: 14
}
library_dependencies {
  library_index: 134
  library_dep_index: 16
  library_dep_index: 135
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 53
  library_dep_index: 140
  library_dep_index: 141
  library_dep_index: 143
  library_dep_index: 149
  library_dep_index: 151
  library_dep_index: 155
  library_dep_index: 0
  library_dep_index: 155
}
library_dependencies {
  library_index: 135
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 33
}
library_dependencies {
  library_index: 136
  library_dep_index: 16
  library_dep_index: 137
  library_dep_index: 135
  library_dep_index: 139
  library_dep_index: 4
}
library_dependencies {
  library_index: 137
  library_dep_index: 138
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 135
  library_dep_index: 139
}
library_dependencies {
  library_index: 138
  library_dep_index: 16
  library_dep_index: 135
  library_dep_index: 139
  library_dep_index: 0
}
library_dependencies {
  library_index: 139
  library_dep_index: 0
}
library_dependencies {
  library_index: 140
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 0
}
library_dependencies {
  library_index: 141
  library_dep_index: 142
}
library_dependencies {
  library_index: 142
  library_dep_index: 16
  library_dep_index: 143
  library_dep_index: 147
  library_dep_index: 100
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 143
  library_dep_index: 147
  library_dep_index: 149
  library_dep_index: 151
  library_dep_index: 153
  library_dep_index: 154
}
library_dependencies {
  library_index: 143
  library_dep_index: 144
}
library_dependencies {
  library_index: 144
  library_dep_index: 16
  library_dep_index: 145
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 141
  library_dep_index: 147
  library_dep_index: 149
  library_dep_index: 151
  library_dep_index: 153
  library_dep_index: 154
}
library_dependencies {
  library_index: 145
  library_dep_index: 0
  library_dep_index: 146
}
library_dependencies {
  library_index: 146
  library_dep_index: 0
}
library_dependencies {
  library_index: 147
  library_dep_index: 148
}
library_dependencies {
  library_index: 148
  library_dep_index: 143
  library_dep_index: 100
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 141
  library_dep_index: 143
  library_dep_index: 149
  library_dep_index: 151
  library_dep_index: 153
  library_dep_index: 154
}
library_dependencies {
  library_index: 149
  library_dep_index: 150
}
library_dependencies {
  library_index: 150
  library_dep_index: 141
  library_dep_index: 151
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 141
  library_dep_index: 143
  library_dep_index: 147
  library_dep_index: 151
  library_dep_index: 153
  library_dep_index: 154
}
library_dependencies {
  library_index: 151
  library_dep_index: 152
}
library_dependencies {
  library_index: 152
  library_dep_index: 143
  library_dep_index: 147
  library_dep_index: 153
  library_dep_index: 100
  library_dep_index: 0
  library_dep_index: 141
  library_dep_index: 143
  library_dep_index: 147
  library_dep_index: 149
  library_dep_index: 153
  library_dep_index: 154
}
library_dependencies {
  library_index: 153
  library_dep_index: 154
  library_dep_index: 141
  library_dep_index: 143
  library_dep_index: 147
  library_dep_index: 149
  library_dep_index: 151
  library_dep_index: 154
}
library_dependencies {
  library_index: 154
  library_dep_index: 141
  library_dep_index: 143
  library_dep_index: 147
  library_dep_index: 149
  library_dep_index: 151
  library_dep_index: 153
}
library_dependencies {
  library_index: 155
  library_dep_index: 16
  library_dep_index: 16
  library_dep_index: 21
  library_dep_index: 135
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 143
  library_dep_index: 149
  library_dep_index: 151
  library_dep_index: 156
  library_dep_index: 161
  library_dep_index: 0
  library_dep_index: 134
}
library_dependencies {
  library_index: 156
  library_dep_index: 21
  library_dep_index: 7
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 20
  library_dep_index: 157
  library_dep_index: 160
  library_dep_index: 159
  library_dep_index: 20
  library_dep_index: 51
}
library_dependencies {
  library_index: 157
  library_dep_index: 158
  library_dep_index: 159
  library_dep_index: 160
  library_dep_index: 27
}
library_dependencies {
  library_index: 158
  library_dep_index: 16
}
library_dependencies {
  library_index: 159
  library_dep_index: 16
  library_dep_index: 160
}
library_dependencies {
  library_index: 160
  library_dep_index: 16
}
library_dependencies {
  library_index: 161
  library_dep_index: 156
  library_dep_index: 0
  library_dep_index: 33
}
library_dependencies {
  library_index: 162
  library_dep_index: 16
}
library_dependencies {
  library_index: 163
  library_dep_index: 38
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 166
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 169
  library_dep_index: 35
}
library_dependencies {
  library_index: 164
  library_dep_index: 16
}
library_dependencies {
  library_index: 165
  library_dep_index: 16
  library_dep_index: 164
  library_dep_index: 166
  library_dep_index: 119
  library_dep_index: 168
}
library_dependencies {
  library_index: 166
  library_dep_index: 16
  library_dep_index: 164
  library_dep_index: 119
  library_dep_index: 167
  library_dep_index: 78
}
library_dependencies {
  library_index: 167
  library_dep_index: 16
  library_dep_index: 119
}
library_dependencies {
  library_index: 168
  library_dep_index: 16
  library_dep_index: 119
}
library_dependencies {
  library_index: 169
  library_dep_index: 71
  library_dep_index: 36
  library_dep_index: 170
  library_dep_index: 35
}
library_dependencies {
  library_index: 170
  library_dep_index: 36
}
library_dependencies {
  library_index: 171
  library_dep_index: 36
  library_dep_index: 35
  library_dep_index: 114
}
library_dependencies {
  library_index: 172
  library_dep_index: 38
  library_dep_index: 173
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 37
}
library_dependencies {
  library_index: 173
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 174
  library_dep_index: 37
  library_dep_index: 175
  library_dep_index: 178
  library_dep_index: 14
}
library_dependencies {
  library_index: 174
  library_dep_index: 16
}
library_dependencies {
  library_index: 175
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 176
  library_dep_index: 177
}
library_dependencies {
  library_index: 176
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 14
}
library_dependencies {
  library_index: 177
  library_dep_index: 176
  library_dep_index: 23
  library_dep_index: 14
}
library_dependencies {
  library_index: 178
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 60
}
library_dependencies {
  library_index: 179
  library_dep_index: 16
  library_dep_index: 173
  library_dep_index: 20
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 180
  library_dep_index: 183
  library_dep_index: 14
}
library_dependencies {
  library_index: 180
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 181
}
library_dependencies {
  library_index: 181
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 60
  library_dep_index: 59
  library_dep_index: 182
  library_dep_index: 178
  library_dep_index: 183
  library_dep_index: 23
  library_dep_index: 185
  library_dep_index: 186
  library_dep_index: 174
}
library_dependencies {
  library_index: 182
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 60
}
library_dependencies {
  library_index: 183
  library_dep_index: 16
  library_dep_index: 60
  library_dep_index: 20
  library_dep_index: 126
  library_dep_index: 184
}
library_dependencies {
  library_index: 184
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 14
}
library_dependencies {
  library_index: 185
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 23
}
library_dependencies {
  library_index: 186
  library_dep_index: 16
  library_dep_index: 20
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 11
  dependency_index: 12
  dependency_index: 120
  dependency_index: 121
  dependency_index: 122
  dependency_index: 16
  dependency_index: 123
  dependency_index: 124
  dependency_index: 84
  dependency_index: 75
  dependency_index: 20
  dependency_index: 24
  dependency_index: 129
  dependency_index: 53
  dependency_index: 104
  dependency_index: 2
  dependency_index: 132
  dependency_index: 133
  dependency_index: 93
  dependency_index: 116
  dependency_index: 117
  dependency_index: 108
  dependency_index: 3
  dependency_index: 134
  dependency_index: 162
  dependency_index: 38
  dependency_index: 163
  dependency_index: 171
  dependency_index: 71
  dependency_index: 172
  dependency_index: 37
  dependency_index: 115
  dependency_index: 141
  dependency_index: 149
  dependency_index: 179
  dependency_index: 187
  dependency_index: 105
  dependency_index: 188
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://storage.googleapis.com/download.flutter.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jcenter.bintray.com/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
