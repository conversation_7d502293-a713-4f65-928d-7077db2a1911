<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>UISupportsDocumentBrowser</key>
  <false/>
  <key>LSSupportsOpeningDocumentsInPlace</key>
  <true/>
  <key>NSFaceIDUsageDescription</key>
  <string>Lock the application</string>
  <key>NSPhotoLibraryUsageDescription</key>
  <string><PERSON><PERSON> uses photos to attach to a transaction entry</string>
  <key>NSCameraUsageDescription</key>
  <string><PERSON><PERSON> uses the camera to capture a photo to be used as a transaction attachment</string>
  <key>ITSAppUsesNonExemptEncryption</key>
  <false/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Cashew</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>budget</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
  <key>CFBundleURLTypes</key>
  <array>
    <dict>
      <key>CFBundleTypeRole</key>
      <string>Editor</string>
      <key>CFBundleURLSchemes</key>
      <array>
        <string>com.googleusercontent.apps.267621253497-ih94g1srq37gun4mdmvadgl3ev588vpf</string>
      </array>
    </dict>
  </array>
  <key>CFBundleLocalizations</key>
  <array>
    <string>en</string>
    <string>fr</string>
    <string>es</string>
    <string>zh</string>
    <string>hi</string>
    <string>ar</string>
    <string>pt</string>
    <string>ru</string>
    <string>ja</string>
    <string>de</string>
    <string>ko</string>
    <string>tr</string>
    <string>it</string>
    <string>vi</string>
    <string>pl</string>
    <string>nl</string>
    <string>th</string>
    <string>cs</string>
  </array>
</dict>
</plist>
