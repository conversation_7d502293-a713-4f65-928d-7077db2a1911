{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/App/Android Studio/AndoirdSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/App/Android Studio/AndoirdSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/App/Android Studio/AndoirdSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/App/Android Studio/AndoirdSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-5003591149f67479ac0a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-67e82459d019eb26589c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-87b7c59f6c638f8e6a01.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-67e82459d019eb26589c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-87b7c59f6c638f8e6a01.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5003591149f67479ac0a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}