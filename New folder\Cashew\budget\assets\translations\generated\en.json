{"default-account-name": "Bank", "default-budget-name": "Budget", "budget-history": "Budget History", "home": "Home", "transactions": "Transactions", "budgets": "Budgets", "more-actions": "More Actions", "subscriptions": "Subscriptions", "notifications": "Notifications", "edit-account": "Edit Account", "accounts": "Accounts", "add-transaction": "Add Transaction", "add-account": "Add Account", "add-category": "Add Category", "add-title": "Add Title", "add-budget": "Add Budget", "title-placeholder": "Title", "name-placeholder": "Name", "enter-name": "Enter Name", "nickname-placeholder": "Nickname", "notes-placeholder": "Notes", "view-more": "View More", "view-all-transactions": "View All Transactions", "expense": "Expense", "income": "Income", "notifications-disabled": "Notifications Disabled", "notifications-disabled-description": "Tap here to enable notifications in your device's settings page", "greetings-general-1": "Hello", "greetings-general-2": "Hi there", "greetings-general-3": "Hi", "greetings-general-4": "How are you", "greetings-general-5": "What's up", "greetings-general-6": "Hello there", "greetings-general-7": "Hope all is well", "greetings-morning-1": "Good morning", "greetings-morning-2": "Good day", "greetings-afternoon-1": "Good afternoon", "greetings-afternoon-2": "Good day", "greetings-evening-1": "Good evening", "greetings-late-1": "Good night", "greetings-late-2": "Get some rest", "all-spending": "All Spending", "edit-data": "Edit Data", "account-details": "Account Details", "budgets-details": "Budgets Details", "categories-details": "Categories Details", "titles-details": "Titles Details", "edit-accounts": "Edit Accounts", "edit-budgets": "Edit Budgets", "edit-categories": "Edit Categories", "edit-titles": "Edit Titles", "edit-title": "Edit Title", "select-categories-to-watch": "Select Categories to Watch", "watch-categories": "Watch Categories", "upcoming": "Upcoming", "overdue": "Overdue", "lent": "<PERSON><PERSON>", "borrowed": "Borrowed", "all": "All", "no-transactions-within-time-range": "No transactions within time range", "total-cash-flow": "Total cash flow", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "remaining-amount-of": "left of", "spent-amount-of": "spent of", "overspent-amount-of": "overspent of", "onboarding-title-1": "Track your spending habits with {app}!", "onboarding-info-1": "Enter your daily transactions to gain powerful insights into your spending habits.", "onboarding-title-2": "Create a budget", "onboarding-info-2-1": "You can always edit this later.", "onboarding-title-3": "Welcome to {app}!", "sign-in-with-google": "Sign In with Google", "onboarding-info-3": "Keep your data backed up and synced with Google Drive.", "continue-without-sign-in": "Continue Without Sign In", "login": "<PERSON><PERSON>", "settings": "Settings", "about": "About", "about-app": "About {app}", "share-feedback": "Share Feedback", "theme": "Theme", "accent-color": "Accent Color", "accent-color-description": "Select a color theme for the interface", "material-you": "Material You", "material-you-description": "Use a colorful expressive interface", "theme-mode": "Theme Mode", "preferences": "Preferences", "language": "Language", "username": "Username", "battery-saver": "Battery Saver", "battery-saver-description": "Optimize the UI to increase performance and save battery", "verify-identity": "Please verify your identity.", "automation": "Automation", "auto-email-transactions": "Auto Email Transactions", "pay-subscriptions": "Pay Subscriptions", "pay-subscriptions-description": "Automatically mark a subscription as paid after the due date", "pay-repetitive": "Pay Repetitive", "pay-repetitive-description": "Automatically mark a repetitive transaction as paid after the due date", "pay-upcoming": "Pay Upcoming", "pay-upcoming-description": "Automatically mark an upcoming transaction as paid after the due date", "import-csv": "Import CSV File", "import-csv-description": "Import an external CSV data source", "rate-app": "Rate {app}", "rate-app-subtitle": "Share your feedback with the developer to help improve {app}", "rate-app-privacy": "Only the stars, feedback, and date will be shared.", "rate-app-privacy-email": "Only the stars, feedback, (email if provided), and date will be shared.", "feedback-suggestions-questions": "Feedback, suggestions, questions", "submit": "Submit", "select-color": "Select Color", "use-system-color": "Use System Color", "custom-color": "Custom Color", "select": "Select", "default-category-dining": "Dining", "default-category-groceries": "Groceries", "default-category-shopping": "Shopping", "default-category-transit": "Transit", "default-category-entertainment": "Entertainment", "default-category-bills-fees": "Bills & Fees", "default-category-gifts": "Gifts", "default-category-sports": "Sports", "default-category-beauty": "Beauty", "default-category-work": "Work", "default-category-travel": "Travel", "past-budget-cycles": "Past Budget Cycles", "edit-budget": "Edit Budget", "clear": "Clear", "done": "Done", "filters": "Filters", "reset": "Reset", "apply": "Apply", "csv-error": "Error Importing CSV", "no-file-selected": "No file selected", "ok": "OK", "info": "Info", "exchange-rate-notice": "Exchange Rate Notice", "exchange-rate-notice-description": "The exchange rates displayed within this app are for informational purposes only and should not be used for investment decisions. These rates are estimates and may not reflect actual rates. By using this app, you acknowledge that you understand and accept these limitations and that you assume full responsibility for any decisions made based on the information provided within the app.", "search-placeholder": "Search...", "search-accounts-placeholder": "Search accounts...", "search-budgets-placeholder": "Search budgets...", "search-categories-placeholder": "Search categories...", "search-titles-placeholder": "Search titles...", "search-transactions": "Search Transactions", "search": "Search", "search-currencies-placeholder": "Search currencies...", "ask-for-transaction-title": "Ask For Transaction Title", "ask-for-transaction-title-description": "When adding a transaction", "auto-add-titles": "Automatically Add Titles", "auto-add-titles-description": "When a transaction is created", "select-categories": "Select Categories", "transactions-to-include": "Transactions to Include", "category-spending-goals": "Category Spending Goals", "saving-tracking": "You should save", "for": "for", "more-days": "more days", "more-day": "more day", "spending-tracking": "You can spend", "of-spending": "of spending", "of-income": "of income", "of-expense": "of expense", "select-currency": "Select Currency", "categories": "Categories", "titles": "Titles", "edit-home": "Edit Home", "overdue-and-upcoming": "Overdue & Upcoming", "lent-and-borrowed": "Lent & Borrowed", "income-and-expenses": "Income & Expenses", "spending-graph": "Spending Graph", "select-budget": "Select Budget", "select-budgets": "Select Budgets", "default-line-graph": "Default (30 days)", "select-category": "Select Category", "enter-title": "Enter Title", "associated-titles": "Associated Titles", "associated-titles-description": "If a transaction title contains any of the phrases listed, it will be added to this category", "enter-amount": "Enter Amount", "set-amount": "Set Amount", "pay": "Pay", "pay-description": "Add payment on this transaction?", "cancel": "Cancel", "skip": "<PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposited": "Deposited", "deposit-description": "Deposit this amount?", "collect": "Collect", "collect-description": "Have you collected the amount you lent?", "collected": "Collected", "settled": "Settled", "settle-description": "Have you paid and settled your debt?", "settle": "Settle", "remove-skip": "Remove <PERSON>", "remove-skip-description": "Remove the skipped payment on this transaction?", "remove": "Remove", "remove-payment": "Remove Payment", "remove-payment-description": "Remove the payment on this transaction?", "skipped": "Skipped", "paid": "Paid", "not-paid": "Not paid", "discard-changes": "Discard Changes?", "discard-changes-description": "Are you sure you want to discard your changes.", "discard": "Discard", "changelog": "Changelog", "view-changelog": "Changelog", "view-app-intro": "Onboarding", "app-is-open-source": "{app} is Open Source!", "open-source": "Open Source", "lead-developer": "Lead Developer", "database-designer": "Database Designer", "graphics": "Graphics", "freepik-credit": "Icons from FlatIcon by FreePik", "font-awesome-credit": "Icons from Font Awesome", "pch-vector-credit": "Landing graphics by pch-vector", "major-tools": "Major <PERSON><PERSON>", "view-licenses-and-legalese": "Licenses and Legalese", "made-in-canada": "Made in Canada", "delete-all-data": "Delete All Data", "erase-everything": "Erase Everything?", "erase-everything-description": "All Google Drive backups will be kept.", "erase-everything-warning": "Are You Sure You Want to Erase Everything?", "erase-everything-warning-description": "All data and preferences will be deleted!", "erase": "Erase", "edit-category": "Edit Category", "save-changes": "Save Changes", "budget-type": "Budget Type", "monthly": "Monthly", "yearly": "Yearly", "total": "Total", "monthly-subscriptions": "Monthly subscriptions", "yearly-subscriptions": "Yearly subscriptions", "total-subscriptions": "Total subscriptions", "beginning": "beginning", "custom-line-graph": "Custom Start Date", "data-backup": "Data Backup", "export": "Export", "import": "Import", "sync": "Sync", "backups": "Backups", "devices": "devices", "restore-a-backup": "Restore a Backup", "manage-syncing-info": "Manage the syncing of data between multiple devices. May incur extra data usage.", "overwrite-warning": "This will overwrite all current data", "web-app": "Web App", "auto-backups": "Auto Backups", "auto-backups-description": "Backup data when opened", "sync-data": "Sync Data", "sync-data-description": "Sync data to other devices", "sync-every-change": "Sync on Every Change", "sync-every-change-description1": "Syncing after every change", "sync-every-change-description2": "Syncing on refresh/launch", "backup-frequency": "Backup Frequency", "number-of-days": "Number of days", "backup-limit": "Backup Limit", "change-limit": "Change Limit?", "change-limit-warning": "Changing the backup limit to a smaller number will remove any past backups that are currently stored, if they exceed the limit, everytime a backup is made.", "change": "Change", "load-backup": "Load Backup?", "load-backup-warning": "This will replace all your current data!", "load": "Load", "delete-backup": "Delete backup?", "deleted-backup": "Deleted Backup", "delete": "Delete", "selected": "selected", "system": "System", "light": "Light", "dark": "Dark", "view-all-currencies": "View All Currencies", "no-currencies-found": "No currencies found.", "no-transactions-found": "No transactions found.", "showing-transactions-from": "Showing transactions from", "days": "days", "day": "day", "category-to-transfer-all-transactions-to": "Category to transfer all transactions to", "merge-category": "Merge Category", "merge-into": "Merge into", "merge-into-description-categories": "This will transfer all transactions to the new category and erase the old category", "merge": "<PERSON><PERSON>", "set-title": "Set Title", "enter-limit": "Enter Limit", "set-limit": "<PERSON>", "of-budget": "of budget", "transaction": "Transaction", "nickname": "Nickname", "default": "<PERSON><PERSON><PERSON>", "subscription": "Subscription", "repetitive": "Repetitive", "added-to-other-budgets": "Added to other budgets", "no-budget": "No budget", "shared-to-other-budgets": "Shared to other budgets", "all-transactions": "All transactions", "added-only": "Added only", "select-period": "Select period", "custom": "Custom", "daily": "Daily", "weekly": "Weekly", "month": "Month", "year": "Year", "week": "Week", "months": "Months", "years": "Years", "weeks": "Weeks", "select-custom-period": "Select Custom Period", "add-subscription": "Add Subscription", "repeat-every": "Repeat every", "all-categories-budget": "All categories budget", "category-budget": "category budget", "category": "category", "budget": "Budget", "all-categories": "All categories", "sign-in-error": "Sign-in Error", "sign-in-error-description": "Check your connection and try again", "delete-selected-transactions": "Delete Selected Transactions?", "deleted": "Deleted", "edit-transaction": "Edit Transaction", "delete-budget": "Delete Budget", "delete-budget-question": "Delete Budget?", "pin-to-home": "Pin to Homepage", "elete-budget-added-warning": "All transactions belonging to this budget will no longer be connected to a budget.", "deleted-budget": "Deleted Budget", "delete-transaction": "Delete transaction", "delete-transaction-question": "Delete Transaction?", "daily-notifications": "Daily Notifications", "alert-time": "<PERSON><PERSON>", "upcoming-transactions": "Upcoming Transactions", "notification-reminder-1": "Don't forget to add transactions from today!", "notification-reminder-2": "Add your daily transactions to stay on track.", "notification-reminder-3": "Update your budget with today's expenses.", "notification-reminder-4": "Add today's expenses to your budget tracker.", "notification-reminder-5": "Record your transactions for the day.", "notification-reminder-6": "Stay on top of your budget by adding your daily transactions.", "notification-reminder-7": "Don't forget to add your daily transactions.", "notification-reminder-8": "Update your budget with today's expenses.", "notification-reminder-9": "Add your expenses from today to the app.", "notification-reminder-10": "Record your transactions to stay on track.", "notification-reminder-11": "Stay on top of your budget by adding transactions.", "notification-reminder-12": "Keep an accurate record of your spending.", "notification-reminder-13": "Add your daily transactions to the app.", "notification-reminder-14": "Update your budget with today's expenses.", "notification-reminder-15": "Record your transactions to stay on track.", "notification-reminder-16": "Stay on top of your budget by adding transactions.", "notification-reminder-17": "It's the end of the day, have you added your transactions?", "notification-reminder-18": "Make sure to add your daily transactions to the app.", "notification-reminder-19": "Add any expenses from today to your budget tracker.", "notification-reminder-20": "Record your transactions for the day to get a complete picture of your spending habits.", "notification-reminder-21": "Don't forget to add your daily transactions to stay on track with your budget.", "notification-reminder-22": "Take a few minutes to update your budget with today's expenses.", "notification-reminder-23": "Make sure to add any expenses from today to your budget tracker.", "notification-reminder-24": "Remember to record your transactions for the day to get a complete picture of your spending habits.", "notification-reminder-25": "Add your daily transactions to the app to keep your budget on track.", "notification-reminder-26": "Stay on top of your budget by adding your daily transactions to the app.", "notification-reminder-title": "Add Transactions", "notification-upcoming-transaction-title": "Upcoming Transaction Due", "translations-help": "If you would like to help with translations, please email", "no-transactions-for": "No transactions for", "average-spent": "average spent", "average-saved": "average saved", "select-icon": "Select Icon", "1-day-ago": "1 day ago", "1-hour-ago": "1 hour ago", "1-minute-ago": "1 minute ago", "days-ago": "days ago", "hours-ago": "hours ago", "minutes-ago": "minutes ago", "just-now": "just now", "synced": "Synced", "never": "never", "copied-to-clipboard": "Copied To Clipboard", "pasted-from-clipboard": "Pasted from clipboard", "select-graph": "Select Graph", "logout": "Logout", "backup-found": "Backup Found", "backup-found-description": "Would you like to restore a backup?", "restore": "Rest<PERSON>", "privacy-policy": "Privacy Policy and Terms of Use", "budget-like-a-pro": "Budget like a pro with", "support-the-developer": "Support the developer", "support-the-developer-description": "More than 3 years of development", "unlimited-budgets-and-goals": "Unlimited budgets", "unlimited-budgets-and-goals-description": "Create more than 1 budget, goal, or loan", "past-budget-periods": "Past budget periods", "past-budget-periods-description": "View spending breakdowns of past periods", "unlimited-color-picker": "Unlimited color picker", "unlimited-color-picker-description": "Pick any color you want", "continue-for-free": "Continue for free", "colorful-interface": "Colorful Interface", "theme-color": "Theme Color", "google-drive-backup-disclaimer": "Google Drive is used to backup your data. Please login to your Google account to continue.", "continue": "Continue", "set-name": "Set Name", "edit-spending-goals": "Edit Spending Goals", "spending-goals": "Spending Goals", "added-budget-description": "This budget will only include transactions that have been added to it.", "history": "History", "backup": "Backup", "google-drive-backup": "Google Drive Backup", "google-drive-backup-description": "All backups are created on your personal Google Drive and is not accessible to the developer. Read the Privacy Policy for more details.", "stored-backups": "backups will be stored", "lets-go": "Let's Go!", "transactions-for-selected-category": "Showing transactions from selected category", "select-start-date": "Select Start Date", "all-time": "All Time", "icon-suggestion-details": "Can't find an icon you want? Suggest some and they can be added!", "suggestion": "Suggestion", "icon-suggestion-privacy": "Only the suggestion and date will be shared.", "suggest-icon": "Suggest Icon", "preview-demo": "Preview Demo", "preview-demo-description": "Take a tour of the app's interface and features using randomly generated sample data. Exit the preview whenever you like.", "activate": "Activate", "preview-demo-warning": "Preview Demo is currently active, and auto-generated data is being used. It is not recommended to use the application in Demo mode, as it may lead to data loss. To remove generated data and exit Preview Demo, tap here.", "exit-demo": "Exit Demo", "not-available-in-preview-demo": "Not Available in Preview Demo", "not-available-in-preview-demo-description": "Disable Preview Demo mode to use this feature.", "copied": "<PERSON>pied", "created-copy": "Created Copy", "duplicate": "Duplicate", "delete-transactions": "Delete transactions", "change-account": "Change Account", "add-to-budget": "Add to Budget", "select-account": "Select Account", "move-to-other-account-button": "Move Transactions To Another Account and Delete", "changed-account": "Changed Account", "added-to-budget": "Added to Budget", "no-addable-budgets": "No budgets to add transactions to", "create-addable-budget": "Create 'Added Only' budget", "delete-budget-added-warning": "Transactions added to this budget will no longer be linked to this budget. They will not be deleted.", "removed-from-budget": "Removed From Budget", "pinned-to-homepage": "Pinned to Homepage", "unpinned-to-homepage": "Unpinned from Homepage", "expenses-only": "Expenses Only", "expenses-only-description": "Only expenses can be added to a budget", "delete-category-question": "Delete Category?", "delete-category-question-description": "This will delete all transactions associated with this category.", "delete-all-transactions-question": "Delete All Transactions?", "delete-category-merge-warning": "Deleting a category will delete all transactions in this category. Move transactions to another category to avoid this.", "deleted-category": "Deleted Category", "delete-account": "Delete account", "delete-account-question": "Delete Account?", "delete-account-question-description": "This will delete all transactions associated with this account.", "move-transactions": "Move Transactions", "delete-account-merge-warning": "Deleting an acount will delete all transactions in this account. Move transactions to another account to avoid this.", "deleted-account": "Deleted Account", "delete-title-question": "Delete Title?", "deleted-title": "Deleted Title", "remove-transactions-from-added-budget-question": "Remove Transactions?", "deleted-transactions": "Deleted Transactions", "deleted-transaction": "Deleted Transaction", "change-category": "Change Category", "changed-category": "Changed Category", "merged-category": "Merged Category", "an-error-occured": "An error occured", "more": "More", "syncing-failed": "Syncing Failed", "feedback-shared": "Feedback Shared!", "thank-you": "Thank you!", "email-optional": "Email (optional)", "feedback": "<PERSON><PERSON><PERSON>", "backup-created": "Backup Created", "delete-sync-backup-warning": "To prevent synchronization issues between clients, it's a good idea to sync all other active devices before deleting.", "development-team": "Development Team", "settings-and-customization": "Settings & Customization", "settings-and-customization-description": "Theme, Language, Import/Export CSV", "assign-columns": "Assign <PERSON>", "date": "Date", "amount": "Amount", "title": "Title", "note": "Note", "account": "Account", "date-format": "Date Format", "example": "Example:", "date-format-note": "Not needed if the CSV uses proper date formatting", "current-account": "Current Account", "none": "None", "error-getting-products": "Error getting products", "error-getting-products-description": "There was an error connecting to the app store. Please try again later.", "error-processing-order": "There was an error processing your purchase. Please try again.", "order-confirmation": "Thank you for supporting <PERSON><PERSON>!", "any-previous-purchases-restored": "Any previous purchases were restored", "already-purchased": "Already Purchased", "current-plan": "Current Plan", "lifetime": "Lifetime", "exchange-rates-api": "Exchange Rates API", "exchange-rates": "Exchange Rates", "active": "Active", "edit-home-page": "Edit Home Page", "delete-category": "Delete category", "manage": "Manage", "restore-purchases": "<PERSON><PERSON> Purchases", "backup-restored": "Backup Restored", "budget-total-type": "Budget Total Type", "total-spent": "Total Spent", "total-remaining": "Total Remaining", "total-spent-example": "Example: 25 spent of 100", "total-remaining-example": "Example: 75 left of 100", "importing-loading": "Importing...", "successfully-imported": "Successfully imported", "biometrics-disabled": "Biometrics Disabled", "biometrics-disabled-description": "Enable biometrics in the settings page and try again", "biometrics-error": "Biometrics Error", "biometrics-error-description": "There was an error using the devices biometrics", "from-the-developer": "From the Developer", "developer-message-1": "Financial well-being matters to all of us, and we're here to support your journey towards better money management. That's why we offer you premium features at no cost. The premium popup will appear, but you can dismiss it to access the locked feature", "developer-message-1-1": "(with no waiting too!)", "developer-message-2": "However, if possible, consider supporting the development. At only one coffee ☕ a month, your support plays a crucial role in this application's future growth. Thank you 😄", "support": "Support", "unlock-for-free": "Unlock for Free", "no-free-stuff": "No, I don't want free stuff", "search-transactions-placeholder": "Search transactions...", "scheduled": "Scheduled", "loans": "Loans", "add-credit": "Add Credit", "add-debt": "Add Debt", "add-upcoming": "Add Upcoming", "you-owe": "You owe", "you-get": "You get", "heat-map": "Heatmap", "select-budget-type": "Select Budget Type", "added-only-description-1": "Only the transactions you add", "added-only-description-2": "Useful for one-time budgets with custom time periods", "added-only-description-3": "Example: 'Vacation' budget", "all-transactions-description-1": "All transactions within selected categories and filters", "all-transactions-description-2": "Useful for long term budgets over multiple periods", "all-transactions-description-3": "Example: 'Monthly Spending' budget", "no-budgets-found": "No budgets found.", "no-categories-found": "No categories found.", "no-accounts-found": "No accounts found.", "no-titles-found": "No titles found.", "translations": "Translations", "ask-for-notes-with-title": "Ask For Note With Title", "ask-for-notes-with-title-description": "When entering the title", "enter-period-length": "Enter Period Length", "export-csv": "Export CSV File", "export-csv-warning": "Data Backup", "export-csv-warning-description": "Exporting to CSV should not be used as a backup of data. Budgets and detailed app data is not exported in the CSV! Please use Google Drive or the Import and Export data settings below.", "csv-saved-success": "CSV Saved to Device", "error-exporting": "Error Exporting", "backup-downloaded-success": "Backup Downloaded to <PERSON>ce", "backup-saved-success": "Backup Saved to <PERSON><PERSON>", "error-downloading": "Error Downloading", "error-saving": "Error <PERSON>", "error-importing": "Error Importing", "tools": "Tools", "delete-bill-item-question": "Delete Bill Item?", "bill-splitter": "<PERSON>", "bill-splitter-info": "The bill splitter will split a bill you paid for amongst the people who purchased items. You can easily keep track of who owes you certain amounts of money.", "clear-bill": "Clear Bill", "names": "Names", "summary": "Summary", "edit-item": "<PERSON>em", "add-item": "Add Item", "update-item": "Update Item", "delete-item": "Delete Item", "owed-to-you": "Owed to you", "name": "Name", "who-are-you-question": "Who Are You?", "who-are-you-description": "You should be the one who paid the bill", "split-evenly": "Split Evenly", "item-name-placeholder": "Item Name", "reset-bill-question": "Reset Bill?", "reset-bill-description": "This will delete all items in the current bill.", "success-generate-loans": "Success", "success-generate-loans-description": "Created loan transactions from bill", "missing-data": "Missing data.", "generate-loan-transactions": "Generate Loan Transactions", "no-names-found": "No names found.", "add-name": "Add Name", "delete-name-question": "Delete Name?", "duplicate-name-warning": "Duplicate name", "duplicate-name-warning-description": "Please choose another name", "notifications-reminder": "Add Transaction Reminder", "notifications-reminder-type": "Reminder Type", "daily-notification-type-if-not-opened": "If the app was not already opened today", "daily-notification-type-one-day-from-open": "24 hours from app opened", "daily-notification-type-everyday": "Everyday", "consider-csv-template": "Try again and consider using the provided CSV template.", "template": "Template", "enter-emoji": "Enter <PERSON>", "enter-emoji-placeholder": "Enter emoji", "use-emoji-details": "Select an emoji of your choice as the icon", "import-and-export": "Import & Export", "import-google-sheet": "Import Google Sheet", "enter-google-sheet-url": "Enter Google Sheet URL", "enter-google-sheet-url-description": "Ensure anyone with the link can view your Google Sheet and it is set to public", "create-template-copy": "Create Template Copy", "help": "Help", "create-template-copy-description": "Make a copy and save it to your Google Drive. Edit the sheet with your data and import it!", "get-template": "Get Template", "correct-total-balance": "Correct Total Balance", "default-category-account-amount-balancing": "Balance Correction", "negative-amount": "Negative Amount", "update-total-balance": "Update Total Balance", "updated-total-balance": "Updated Total Balance", "merge-account": "<PERSON><PERSON> Account", "account-to-transfer-all-transactions-to": "Account to transfer all transactions to", "merge-into-description-accounts": "This will transfer all transactions to the new account and erase the old account", "move-and-delete": "Move and Delete", "merged-account": "Merged Account", "cannot-remove-default-account": "Default account cannot be deleted", "cannot-remove": "Cannot Remove", "installment": "Installment", "net-worth": "Net Worth", "goals": "Goals", "edit-goals": "Edit Goals", "savings-goal": "Savings goal", "expense-goal": "Expense goal", "goal": "Goal", "edit-goal": "Edit Goal", "delete-goal": "Delete Goal", "add-goal": "Add Goal", "search-goals-placeholder": "Search goals...", "savings-goal-description-1": "Add income transactions to this goal", "savings-goal-description-2": "Save towards a goal", "savings-goal-description-3": "Example: 'Saving for a trip'", "expense-goal-description-1": "Add expense transactions to this goal", "expense-goal-description-2": "Payoff owed transaction installments", "expense-goal-description-3": "Example: 'Paying off a car loan'", "select-goal-type": "Select Goal Type", "include-income": "Income", "include-debt-and-credit": "Lent and borrowed", "added-to-goal": "Added to goal", "include-income-description-1": "Income transactions will be included in the budget", "include-income-description-2": "The spending budget total will decrease with income", "added-to-other-budgets-description-1": "Transactions you added to another budget will also be included in this budget", "include-debt-and-credit-description-1": "Lent and borrowed transactions will be included in the budget", "added-to-goal-description-1": "Transactions you added to a goal will also be included in this budget", "select-transactions-to-include": "Select Transactions to Include", "delete-goal-question": "Delete Goal?", "remove-transactions-from-goal-question": "Remove transactions?", "delete-goal-warning": "Transactions added to this goal will no longer be linked to this goal. They will not be deleted.", "deleted-goal": "Deleted Goal", "removed-from-goal": "Removed From Goal", "added-to-goal-action": "Added To Goal", "create-goal": "Create Goal", "no-goal": "No goal", "add-to-goal": "Add to Goal", "goals-details": "Goals Details", "select-exclude-categories": "Exclude Categories", "spending-and-savings-goals": "Spending & Saving Goals", "upcoming-transaction-type-description-1": "A transaction that is unpaid", "upcoming-transaction-type-description-2": "Does not count towards your total unless marked 'Paid' or 'Deposited'", "subscription-transaction-type-description-1": "Recurring transaction that will be shown on the subscriptions page", "subscription-transaction-type-description-2": "Does not count towards your total unless marked 'Paid' or 'Deposited'", "repetitive-transaction-type-description-1": "Recurring transaction", "repetitive-transaction-type-description-2": "Does not count towards your total unless marked 'Paid' or 'Deposited'", "repetitive-transaction-type-description-3": "Next transaction generated when current marked 'Paid' or 'Deposited'", "lent-transaction-type-description-1": "Transaction that you lent out", "lent-transaction-type-description-2": "Negative cash flow until marked 'Collected'", "borrowed-transaction-type-description-1": "Transaction borrowed from someone", "borrowed-transaction-type-description-2": "Positive cash flow until marked 'Settled'", "select-transaction-type": "Select Transaction Type", "mark-transaction-help-description": "Mark a special transaction by tapping the action button in the transaction entry", "increase-budget-warning": "Consider Your Budget", "increase-budget-warning-description": "Stick to your budget to maintain financial health. Increasing your budget can hinder your financial goals.", "example-goals": "Example Goals", "example-goals-1": "Trip Savings Jar", "example-goals-2": "Car Loan Payment", "include-income-onboarding-label": "Include Income", "select-goals": "Select Goals", "major-change-1": "Budget Income Support", "major-change-1-1": "Enable the new income budget transaction filter + more transaction filters for more personalized budgets", "major-change-2": "Exclude Categories", "major-change-2-1": "Exclude specific categories from your budget", "major-change-3": "Goals", "major-change-3-1": "Track spending and saving goals", "major-change-3-2": "Add transactions to goals and track your progress", "major-change-4": "Home Page Widgets", "major-change-4-1": "New pie chart homepage widget", "major-change-4-2": "New net worth homepage widget", "major-change-5": "Emoji Support", "major-change-5-1": "Emojis can be used as a category icon", "major-change-6": "Bug Fixes", "major-change-6-1": "Bug fixes and interface improvements", "close": "Close", "view-detailed-changelog": "View Detailed Changelog", "pie-chart": "Pie Chart", "select-type": "Select Type", "create-budget": "Create Budget", "no-goals-found": "No goals found.", "net-worth-settings": "Net Worth Settings", "transfer-balance": "Transfer Balance", "withdraw-amount": "Withdraw Amount", "transferred-balance": "Transferred Balance", "net-total": "Net Total", "all-accounts": "All Accounts", "transfer-amount": "Transfer Amount", "change-currency": "Change Currency", "select-primary-currency": "Select Primary Currency", "select-primary-currency-description": "You can always add more accounts with different currencies later.", "balance-correction": "Balance correction", "balance-correction-description-1": "Include account transfers and account correction transactions", "balance-correction-selected": "Balance Correction Selected", "balance-correction-selected-info": "Please use the budget transaction filters to modify whether to include the Balance Correction category.", "period-cycle-picker-description": "This will apply to the homepage [Income and Expenses], [], [Pie Chart], and the [All Spending] page.", "previous": "Previous", "every": "Every", "major-change-7": "Custom Periods", "major-change-7-1": "Select a custom period or spending cycle to view your homepage statistics and all spending summary", "major-change-8": "38 New Currencies", "cycle": "Cycle", "past-days": "Past Days", "start-date": "Start Date", "provide-email-question": "Provide Email?", "provide-email-question-description": "If you provide your email, the developer can contact you about your suggestion!", "submit-anyway": "Submit Anyway", "go-back": "Go Back", "export-data-file": "Export Cashew Data File", "import-data-file": "Import Cashew Data File", "google-drive": "Google Drive", "backup-your-data-reminder": "Backup Your Data", "backup-your-data-reminder-description": "Automatically backup your data with", "later": "Later", "homepage-banner": "Homepage Banner", "username-banner": "Username Banner", "major-change-9": "Local Backups", "major-change-9-1": "Import/Export full data file backups in Settings", "major-change-10": "Homepage Banner", "major-change-10-1": "Customize the homepage banner widget size in the Edit Home page", "select-period-tooltip": "Select Period", "data-overwrite-warning": "Data Overwrite", "data-overwrite-warning-description": "Importing data will overwrite all current data!", "select-backup-file": "Select Backup", "select-backup-file-description": "Please select your backup [.sqlite] or [.sql] file.", "database-corrupted": "Database Corrupted", "database-corrupted-description": "Your database is corrupted. You may have imported the wrong file.", "import-backup": "Import Backup", "view-previous-budget-periods": "View previous budget periods", "current-period": "Current Period", "set-spending-goals": "Set Category Spending Goals", "set-currency": "<PERSON>", "accounts-list": "Accounts List", "select-accounts": "Select Accounts", "of-category": "of category", "subcategories": "Subcategories", "view-subcategories": "View Subcategories", "select-subcategory": "Select Subcategory", "delete-subcategory-question": "Delete Subcategory?", "delete-subcategory-question-description": "This will remove the subcategory tag from all transactions associated with this subcategory.", "remove-all-transactions-from-subcategory-question": "Remove All Transactions?", "delete-subcategory-merge-warning": "Deleting a subcategory will remove all transactions from this subcategory. Move transactions to another subcategory to avoid this.", "select-the-main-category-for-this-subcategory": "Select the main category for this subcategory", "make-subcategory-of": "Make Subcategory of", "make-subcategory-description-categories": "This will move all transactions to the new category and label them with this subcategory", "make-subcategory": "Convert Into Subcategory", "subcategory-created": "Subcategory Created", "no-subcategory": "No Subcategory", "main-category": "Main Category", "subcategory": "Subcategory", "select-main-category": "Select Main Category", "major-change-11": "Subcategories", "major-change-11-1": "Organize your transactions into categories of categories", "major-change-12": "Accounts List Summary", "major-change-12-1": "New accounts list summary homepage section", "subcategory-to-transfer-all-transactions-to": "Subcategory to transfer all transactions to", "merge-into-description-subcategories": "This will transfer all transactions to the new subcategory and remove the old subcategory", "merged-subcategory": "Merged Subcategory", "merge-subcategory": "<PERSON>rge <PERSON>", "make-main-category": "Make Main Category", "make-main-category-question": "Make Main Category?", "make-main-category-description": "This will move all transactions from the main category to a new main category", "main-category-created": "Main Category Created", "no-folder-selected": "No folder selected", "clear-selection": "Clear Selection", "view-all-days": "View All Days", "view-to-today": "View To Today", "subcategories-description": "Create subcategories to further organize your transactions the way you want it", "examples": "Examples", "drinks": "Drinks", "coffee": "Coffee", "bubble-tea": "Bubble Tea", "soda": "Soda", "entertainment": "Entertainment", "movies": "Movies", "music": "Music", "activities": "Activities", "car": "Car Loan Payment", "gas": "Gas", "maintenance": "Maintenance", "insurance": "Insurance", "beauty": "Beauty", "haircut": "Haircut", "touchups": "Touchups", "clothing": "Clothing", "multiplier": "Multiplier", "bill-splitter-multiplier-description": "Enter a value that will be multiplied to every item in the bill splitter. Useful if tax is applied on each item.", "more-options": "More Options", "cannot-display-graph-for-current-time-period": "Cannot display graph for current time period.", "exclude-from-budget": "Exclude From Budget", "erase-synced-data-and-cloud-backups": "Erase Synced Data And Cloud Backups", "erase-cloud-data": "Erase Cloud Data", "erase-cloud-data-description": "Login to your Google account and delete backups located under 'Sync' and 'Backups'", "add": "Add", "transaction-type": "Transaction Type", "do-not-show-again": "Don't show again", "add-attachment": "Add attachment", "add-attachment-description": "Attachments are added to your Google Drive", "take-photo": "Take Photo", "select-photo": "Select Photo", "select-file": "Select File", "error-attaching-file": "Error Attaching File", "no-photo-taken": "No photo taken", "major-change-14": "Transaction Attachments", "major-change-14-1": "Attach photos or files to transactions via Google Drive", "ending": "ending", "until-forever": "Until Forever", "goal-reached": "Goal reached!", "goal-overdue": "Goal overdue", "save": "Save", "spend": "Spend", "until": "until", "of-subcategory": "of subcategory", "account-label": "Account Label", "account-label-description": "Add account label for all transactions", "transaction-details": "Transaction Details", "reorder-categories": "Reorder Categories", "drag-and-drop-categories-to-rearrange": "Drag and drop categories to rearrange", "show-all-categories": "Show All Categories", "only-income-categories": "Only Income Categories", "only-expense-categories": "Only Expense Categories", "transfer-in": "Transfer In", "transfer-out": "Transfer Out", "cannot-create-transaction": "Cannot Create Transaction", "category-no-longer-exists": "Category no longer exists", "default-category-income": "Income", "select-shortcut": "Select Shortcut", "all-spending-settings": "All Spending Settings", "goal-total-type": "Goal Total Type", "auto-mark-transactions": "Auto Pay Transactions", "auto-mark-transactions-description": "Mark overdue transactions as paid", "more-options-description": "Style, transactions, accounts, formatting", "header-height": "Header Height", "tall": "Tall", "short": "Short", "biometric-lock": "Biometric Lock", "biometric-lock-description": "Require biometrics on app launch", "remaining": "left", "created-transaction": "Created Transaction", "most-common-transactions": "Most Common Transactions", "most-common-transactions-description": "Quickly duplicate the most frequent transactions listed from the last 2 months by selecting in the list.", "spending-limit-type": "Spending Limit Type", "percent": "Percent", "major-change-15": "Add Button Long Press", "major-change-15-1": "Quickly duplicate frequent transactions, transfer balances across accounts and more", "date-range": "Date Range", "transactions-list": "Transactions List", "monthly-spending-summary": "Monthly Spending Summary", "monthly-spending-summary-description": "Transactions page cash flow banner", "enabled-in-settings-at-any-time": "This can be enabled in the settings at any time", "unlock-with": "Unlock with", "learn-more": "Learn More", "current": "Current", "installments": "Installments", "track-installments": "Installments", "track-installments-description": "Create a goal to track payment progress overtime. Add transactions to your goals to track your progress.", "initial-account-balance": "Initial Account Balance", "starting-at": "Starting at", "major-change-16": "New All Spending Page", "major-change-16-1": "Revamped the all spending page with spending period history", "font": "Font", "platform": "Platform", "personalization": "Personalization", "rounded": "Rounded", "outlined": "Outlined", "icon-style": "Icon Style", "more-options-description-theme": "Font, icon style, header height", "style": "Style", "increase-text-contrast": "High Text Contrast", "increase-text-contrast-description": "Increase lighter text contrast", "update-both-transfers-question": "Update Both Balance Transfers?", "update-both-transfers-question-description": "A balance transfer consists of 2 transactions. Would you like to update both?", "only-current": "Only Current", "update-both": "Update Both", "delete-both-transfers-question": "Delete Both Balance Transfers?", "delete-both-transfers-question-description": "A balance transfer consists of 2 transactions. Would you like to delete both?", "delete-both": "Delete Both", "paid-date": "Paid <PERSON>", "current-date": "Current Date", "transaction-date": "Transaction Date", "select-goal": "Select Goal", "until-goal-reached": "Until Goal Reached", "enter-payment-period": "Enter Payment Period", "enter-payment-amount": "Enter Payment Amount", "payments-of": "payments of", "payment-of": "payment of", "enter-color-code": "Enter Color Code", "excluded-from": "Excluded from", "remove-link-question": "Remove Link?", "remove-link-description": "Note: Attachments will *not* be deleted from your Google Drive", "auto-pay-description": "Overdue transactions are automatically marked as paid when the app is opened", "change-date": "Change Date", "changed-date": "Changed Date", "date-not-selected": "Date Not Selected", "time-not-selected": "Time Not Selected", "formatting": "Formatting", "number-format": "Number Format", "remove-tip": "Remove Tip", "why-is-auto-login-disabled-on-web": "Automatic login is not possible on the web app. This is a limitation imposed by Google.", "read-more-here": "Read more here", "auto-login-disabled-on-web": "Auto Login Disabled On Web", "decimal-precision-edit-account-info": "You can change the amount of decimals by editing specific accounts/wallets", "sync-fail-reason": "A device is running an old version of the app. Please update your device's application and re-sync or delete the device's sync backup.", "spending-totals": "Spending Totals", "cumulative": "Cumulative", "per-period": "Per Period", "extra-zeros-button": "Extra Zeros Button", "balance-correction-category-info": "Transactions in this category do not contribute to total income and expense calculations but factor into net total spending. Feel free to customize this category as you like.", "transfer": "Transfer", "show-balance-transfer-tab": "Balance Transfer Tab", "show-balance-transfer-tab-description": "When creating a new transaction", "correct-balance": "Correct Balance", "setup-installment-payments": "Setup installment payments", "deducted-from": "Deducted from", "transfer-fee": "Transfer Fee", "collect-all": "Collect All", "settle-all": "Settle All", "edit-loan": "<PERSON>an", "delete-loan": "Delete Loan", "delete-loan-question": "Delete Loan?", "delete-loan-warning": "Transactions added to this loan will no longer be linked to this loan. They will lose their interest and initial payment tags. The transactions will not be deleted.", "remove-transactions-from-loan-question": "Remove Transactions?", "deleted-loan": "Deleted Loan", "add-loan": "<PERSON>d <PERSON>", "loan": "Loan", "loan-accomplished": "<PERSON><PERSON> Accomplished", "loan-overdue": "<PERSON>an <PERSON>", "long-term-loan": "Long Term Loan", "long-term-loan-description-1": "Paid or collected in terms", "one-time-loan": "One Time Loan", "one-time-loan-description-1": "Paid or collected only once", "partially-collect": "Partially Collect", "partially-settle": "Partially Settle", "select-first-transaction-category": "Select first transaction category", "cannot-create-installment": "Cannot Create Installment", "missing-installment-period-and-amount": "Please select a payment length or amount", "all-spending-summary": "All Spending Summary", "all-spending-description": "Your spending statistics all in one place", "created-new-for": "Created new for", "date-banner-total": "Date Banner Total", "day-total": "Day Total", "no-loans-found": "No loans found.", "search-loans-placeholder": "Search loans...", "long-term": "Long Term", "one-time": "One Time", "in-app-subscription-terms-1": "Yearly and Monthly subscriptions renew automatically and charge immediately. Cancel anytime", "in-app-subscription-terms-2": "in Google Play.", "add-record": "Add Record", "no-loan": "No loan", "edit-record": "Edit Record", "view-long-term-loans": "View long term loans", "select-loans": "Select loans", "no-long-term-loans-found": "No long term loans found.", "create-loan": "Create Loan", "change-loan-amount-tip-lent": "To change the total amount, add a transaction 'Paid' record to the loan.", "change-loan-amount-tip-borrowed": "To change the total amount, add a transaction 'Collected' record to the loan.", "until-loan-reached": "Until Loan Reached", "initial-record": "Initial Record", "long-term-loans": "Long Term Loans", "customize-period-for-account-totals": "Customize period for account totals.", "borrowed-funds": "Borrowed funds", "lent-funds": "Lent funds", "settle-and-collect-all": "Settle/Collect All", "settled-and-collected": "Settled and Collected", "to-pay": "To Pay", "to-collect": "To Collect", "major-change-17": "Long Term Loans", "major-change-17-1": "Track lent and borrowed money over a period of multiple repayments", "amount-collected": "Amount Collected", "amount-settled": "Amount Settled", "right-panel": "Right Panel", "left-panel": "Left Panel", "top-center": "Top Center", "of-total": "of total", "date-reset": "Date Reset", "set-to-current-date-and-time": "Set to current date and time", "add-to-loan": "Add to Loan", "added-to-loan-action": "Added <PERSON>an", "removed-from-loan": "Removed From Loan", "select-loan": "Select Loan", "title-contains": "Title contains", "notes-contain": "Notes contain", "outgoing": "Outgoing", "incoming": "Incoming", "add-currency": "Add <PERSON>cy", "currency": "<PERSON><PERSON><PERSON><PERSON>", "unset": "Unset", "delete-currency": "Delete Currency", "delete-currency-question": "Delete Currency?", "custom-currency": "Custom Currency", "select-an-entry-to-set-custom-exchange-rate": "Select an entry to set a custom exchange rate. Note: All exchange rate calculations use USD as a reference.", "enjoying-cashew-question": "Enjoying Cashew?", "consider-rating": "Consider leaving a review if you enjoy the app 😄 Thank you!", "no-thanks": "No Thanks", "rate": "Rate!", "of-outgoing": "of outgoing", "of-incoming": "of incoming", "applies-to-homepage": "Applies to the homepage", "and-applies-to-widget": "and app widget", "widgets": "Widgets", "net-worth-total-widget": "Total Widget", "select-accounts-and-time-period": "Select accounts and time period", "credit": "Credit", "debit": "Debit", "credit-summary": "Credit Summary", "due-on": "due on", "current-statement-period": "Current Statement Period", "of-credit": "of credit", "of-debit": "of debit", "account-total": "Account Total", "view-all-spending-page-tip": "View the 'All Spending' page for a detailed breakdown of your finances over time.", "credit-limit": "Credit Limit", "due": "due", "credit-payment": "Credit Payment", "add-payment": "Add Payment", "next-payment": "Next payment", "next-payment-due": "Next Payment Due", "no-categories": "No Categories", "saved-amount-of": "saved of", "over-saved-amount-of": "over saved of", "of-saving": "of saving", "set-saving-goals": "Set Category Saving Goals", "edit-saving-goals": "Edit Saving Goals", "saving-goals": "Saving Goals", "saving-limit-type": "Saving Limit Type", "category-saving-goals": "Category Saving Goals", "saving": "Saving", "spending": "Spending", "include-expense": "Expense", "include-expense-description-1": "Expense transactions will be included in the budget", "include-expense-description-2": "The saving budget total will decrease with expenses", "savings-budget": "Savings budget", "expense-budget": "Expense budget", "savings-budget-description-1": "Track your income and budget your savings", "expense-budget-description-1": "Track your expenses and budget your spending", "end-date-reached": "End Date Reached", "over": "over", "clock-format": "Clock Format", "24-hour": "24 Hour", "12-hour": "12 Hour", "widget-theme": "Widget Theme", "widget-background-opacity": "Background Opacity", "currency-total": "Currency Total", "currency-total-description": "List all account currency totals", "app": "App", "set-color": "Set Color", "next-date-time": "Next", "set-date-time": "Set", "add-record-using-plus-button": "Add a record using the + button.", "all-settled": "All Settled", "difference-loan": "Difference loan", "open-settings": "Open Settings", "decimal-precision": "Decimal Precision", "decimal-precision-description": "Decimal places for transaction amount rounding", "same-accounts": "Same Accounts", "select-2-different-accounts": "Select 2 different accounts", "include-amount": "Include Amount", "percentage-precision": "Percentage Precision", "0-decimals": "0 Decimals", "1-decimal": "1 Decimals", "2-decimals": "2 Decimals", "onwards": "onwards", "failed-to-preview-image": "Failed to Preview Image", "open-link": "Open Link", "category-not-selected": "Category Not Selected", "all-transactions-require-a-category": "All transactions require a category", "total-offset": "Total Offset", "total-offset-description": "Offset the total of the loan by entering an amount. Useful if the loan has accumulated interest or other unaccounted for costs increasing the total.", "advanced-automation": "Advanced Automation", "deep-linking": "App Links", "deep-linking-description": "Automate the insertion of transactions using app URLs. Tap to learn more and view the documentation.", "added-transaction": "Added Transaction", "custom-format": "Custom Format", "delimiter": "Delimiter", "decimal": "Decimal", "symbol": "Symbol", "before": "Before", "after": "After", "delimiter-symbol": "Delimiter symbol", "decimal-symbol": "Decimal symbol", "set-delimiter": "Set Delimiter", "set-decimal": "Set <PERSON>", "no-outgoing-within-period": "No outgoing transactions within selected time period", "no-incoming-within-period": "No incoming transactions within selected time period", "no-expense-within-period": "No expense transactions within selected time period", "no-income-within-period": "No income transactions within selected time period", "select-all": "Select All", "excluded": "Excluded", "maximum-transactions": "Maximum Transactions", "only-the-first": "Only the first", "restart-required-to-load-backup": "An app restart is required to load data from a backup", "refresh-required-to-load-backup": "An webpage refresh is required to load data from a backup", "please-restart-the-application": "Please Restart the Application", "please-refresh-the-application": "Please Refresh the Page", "only-expense-income": "Only Expense & Income", "only-expense-income-description-1": "Don't include all outgoing/incoming", "only-expense-income-description-2": "Example: Do not include loans", "compact-number-format": "Compact Number Format", "positive-cash-flow": "Positive Cash Flow", "negative-cash-flow": "Negative Cash Flow", "number-animation": "Number Animation", "count-up": "Count Up", "disabled": "Disabled", "set-period-length": "Set Period Length", "change-currency-description": "The app currency is related to the selected account. Edit the account to change the currency.", "primary-default": "Primary", "short-number-format": "Short Number Format", "primary-currency": "Primary Currency", "change-title": "Change Title", "changed-title": "Changed Title", "edit": "Edit", "edit-transactions": "Edit Transactions", "transaction-list": "Transaction List", "transaction-list-home-description": "Lists the last 7 days containing a transaction", "and-any-transactions": "and any transactions", "days-ahead": "days ahead", "day-ahead": "day ahead", "future-transaction-days": "Future Transaction Days", "future-transaction-days-description": "Days ahead to list transactions", "error-parsing-json": "Error Parsing JSON", "transactions-in-the-csv": "transactions in the CSV.", "errors": "Errors", "black": "Black", "future-transactions": "Future Transactions", "hidden": "hidden", "present-transactions": "Present Transactions", "past-transactions": "Past Transactions", "all-outgoing-incoming": "All Outgoing & Incoming", "all-outgoing-incoming-description-1": "Include all negative/positive cash flow", "all-outgoing-incoming-description-2": "Example: Include loan transactions", "applies-when-switching-tabs-in-the-homepage-transactions-list": "Applies when switching tabs in the homepage transactions list", "number-pad-format": "Number Pad Format", "set-lower-range": "Set Lower Range", "set-upper-range": "Set Upper Range", "set-range": "Set Range", "amount-color": "Amount Color", "green-or-red": "Green / Red", "no-color": "No Color", "please-check-your-system-settings": "Please check your system settings", "maximum-precision": "Maximum Precision", "maximum-precision-description": "12 decimal places are supported", "monthly-upcoming": "Averaged monthly upcoming", "yearly-upcoming": "Averaged yearly upcoming", "total-upcoming": "Total upcoming", "no-subscription-transactions": "No subscription transactions.", "tools-and-extras": "Tools & Extras", "transaction-activity-log": "Transaction Activity Log", "activity-log": "Activity Log", "modified": "Modified", "restore-transaction": "Restore Transaction?", "created": "Created", "tap-each-section-to-customize": "Tap each section to customize", "transaction-no-longer-available": "The transaction was deleted and is no longer available to restore", "category-not-available": "Category Not Available", "the-original-category-has-been-deleted": "The original category has been deleted and the transaction cannot be recovered.", "transaction-restored": "Transaction Restored", "error-restoring": "Error <PERSON>", "expand-all-with-spending-goals": "Expand All With Spending Goals", "collapse-empty-categories": "Collapse Empty Categories", "created-copy-for-current-time": "Created Copy For Current Time", "haptic-feedback": "Haptic <PERSON>", "guide-and-faq": "FAQ, Guides, and Help", "faq": "FAQ", "app-animations": "App Animations", "app-animations-description": "Disabling animations can improve performance", "minimal": "Minimal", "major-change-18": "Transaction Activity Log", "major-change-18-1": "View a list of recently deleted/modified transactions", "greeting-message": "Greeting Message", "edit-heatmap": "Edit Heatmap", "first-weekday": "First Weekday", "calendar": "Calendar", "major-change-19": "Calendar Page", "major-change-19-1": "View your transactions on a calendar", "indefinite-loans": "Indefinite Loans", "indefinite": "Indefinite", "indefinite-loans-description": "Track the difference of ongoing loan transactions with friends", "edit-calendar": "Edit Calendar", "no-value": "No value found in clipboard", "clipboard-error": "Clipboard Error", "restore-purchases-help": "If you have trouble restoring your purchases, please contact the developer for help or read the FAQ.", "contact": "Contact", "loans-details": "Loans Details", "set-details": "Set Details", "import-warning": "Import Warning", "import-warning-description": "Import may fail! Please make sure you have selected a valid Cashew [.sqlite] or [.sql] backup file", "duplicate-budget-question": "Duplicate Budget?", "duplicate-budget": "Duplicate Budget"}