{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-3:/values-fr/values-fr.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\521dfdd1b9bd842178ea8da72f2f692e\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "782,889,991,1110", "endColumns": "106,101,118,104", "endOffsets": "884,986,1105,1210"}}, {"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,1215", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,1311"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-5:/values-fr/values-fr.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\521dfdd1b9bd842178ea8da72f2f692e\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "782,889,991,1110", "endColumns": "106,101,118,104", "endOffsets": "884,986,1105,1210"}}, {"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,1215", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,1311"}}]}]}