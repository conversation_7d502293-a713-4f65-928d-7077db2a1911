{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-5:/values-sk/values-sk.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,1219", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,1315"}}, {"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\521dfdd1b9bd842178ea8da72f2f692e\\transformed\\browser-1.8.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "792,899,1002,1117", "endColumns": "106,102,114,101", "endOffsets": "894,997,1112,1214"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-3:/values-sk/values-sk.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,1219", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,1315"}}, {"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\521dfdd1b9bd842178ea8da72f2f692e\\transformed\\browser-1.8.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "792,899,1002,1117", "endColumns": "106,102,114,101", "endOffsets": "894,997,1112,1214"}}]}]}