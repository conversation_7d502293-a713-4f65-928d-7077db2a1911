{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-5:/values-ms/values-ms.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\521dfdd1b9bd842178ea8da72f2f692e\\transformed\\browser-1.8.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "798,903,1003,1122", "endColumns": "104,99,118,101", "endOffsets": "898,998,1117,1219"}}, {"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,1224", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,1320"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-3:/values-ms/values-ms.xml", "map": [{"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\521dfdd1b9bd842178ea8da72f2f692e\\transformed\\browser-1.8.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "798,903,1003,1122", "endColumns": "104,99,118,101", "endOffsets": "898,998,1117,1219"}}, {"source": "D:\\App\\Android Studio\\gradle\\gradleCache\\caches\\8.10.2\\transforms\\b2f97d8ecf4925aa43c0f052d4a74fae\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,1224", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,1320"}}]}]}