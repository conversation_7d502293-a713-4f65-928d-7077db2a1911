<FrameLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/widget_container"
    android:layout_margin="6dp">
	<ImageView
        android:id="@+id/widget_background"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:scaleType="fitXY"
        android:src="@drawable/widget_background"
    />
	<LinearLayout
		xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_vertical|center_horizontal"
    android:padding="8dp">
		<ImageView
        android:id="@+id/plus_image"
        android:layout_width="32sp"
        android:layout_height="32sp"
        android:src="@drawable/plus"
    />
	</LinearLayout>
</FrameLayout>
