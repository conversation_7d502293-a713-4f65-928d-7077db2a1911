import 'package:flutter/widgets.dart';
// https://www.fluttericon.com/

class MoreIcons {
  MoreIcons._();

  static const _kFontFam = 'Icons';
  static const String? _kFontPkg = null;

  static const IconData chart_bar =
      IconData(0xf080, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData github =
      IconData(0xf09b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData money_bill =
      IconData(0xf0d6, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData google =
      IconData(0xf1a0, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData chart_area =
      IconData(0xf1fe, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData chart_pie =
      IconData(0xf200, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData chart_line =
      IconData(0xf201, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData google_drive =
      IconData(0xf3aa, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData google_play =
      IconData(0xf3ab, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData money_bill_alt =
      IconData(0xf3d1, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData money_bill_wave =
      IconData(0xf53a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData money_bill_wave_alt =
      IconData(0xf53b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData money_check =
      IconData(0xf53c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData money_check_alt =
      IconData(0xf53d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
