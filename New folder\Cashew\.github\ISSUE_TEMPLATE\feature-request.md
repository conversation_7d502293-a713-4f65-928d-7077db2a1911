---
name: Feature Request
about: Suggest something you would like
title: "[Feature Request]"
labels: enhancement
assignees: ''

---

**Is your feature request related to a problem? Please describe.**
[A clear and concise description of what the problem is. Ex. I'm always frustrated when...]

**Describe the solution you'd like**
[A clear and concise description of what you want to happen.]

**Describe alternatives you've considered**
[A clear and concise description of any alternative solutions or features you've considered.]

**Additional context**
[Add any other context or screenshots about the feature request here.]

NOTE TO THE SUBMITTER
**Please check past issues to ensure you are not duplicating feature requests. Duplicates will not be addressed again.**
Not all feature requests will be considered. This budget app was created with careful thought given to each feature's usability and prevention of feature overload. As you use the app, you will discover possibly related useful features and that may not be obvious right away. 
Feel free to email <NAME_EMAIL> and I'll be happy to help you!
