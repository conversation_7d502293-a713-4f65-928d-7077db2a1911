class IconForCategory {
  IconForCategory(
      {required this.icon, required this.tags, this.mostLikelyCategoryName});

  String icon;
  List<String> tags;
  String? mostLikelyCategoryName;
}

List<IconForCategory> iconObjects = [
  IconForCategory(
    icon: "air-hockey.png",
    tags: [
      "air hockey",
      "fun",
      "game",
      "table",
      "sport",
      "competition",
      "puck",
      "stick",
      "arcade",
      "entertainment"
    ],
    mostLikelyCategoryName: "Entertainment",
  ),
  IconForCategory(
    icon: "anchor.png",
    tags: [
      "anchor",
      "sea",
      "ocean",
      "ship",
      "sail",
      "boat",
      "nautical",
      "maritime",
      "harbor",
      "navigation"
    ],
    mostLikelyCategoryName: "Maritime",
  ),
  IconForCategory(
    icon: "antivirus.png",
    tags: [
      "antivirus",
      "security",
      "protection",
      "computer",
      "software",
      "malware",
      "virus",
      "firewall",
      "cybersecurity",
      "data"
    ],
    mostLikelyCategoryName: "Security",
  ),
  IconForCategory(
    icon: "balloons.png",
    tags: [
      "balloons",
      "party",
      "celebration",
      "birthday",
      "event",
      "decoration",
      "festive",
      "gift",
      "fun",
      "colorful"
    ],
    mostLikelyCategoryName: "Parties",
  ),
  IconForCategory(
    icon: "barber.png",
    tags: [
      "barber",
      "haircut",
      "grooming",
      "style",
      "beard",
      "beauty",
      "salon",
      "shave",
      "trim",
      "professional"
    ],
    mostLikelyCategoryName: "Beauty",
  ),
  IconForCategory(
    icon: "baseball-player.png",
    tags: [
      "baseball",
      "sport",
      "game",
      "bat",
      "team",
      "athlete",
      "competition",
    ],
    mostLikelyCategoryName: "Sports",
  ),
  IconForCategory(
    icon: "beach-umbrella.png",
    tags: [
      "beach umbrella",
      "beach",
      "summer",
      "vacation",
      "relax",
      "sunny",
      "tropical",
      "ocean",
      "sand",
      "parasol"
    ],
    mostLikelyCategoryName: "Vacations",
  ),
  IconForCategory(
    icon: "bicycle.png",
    tags: [
      "bicycle",
      "ride",
      "exercise",
      "outdoor",
      "transportation",
      "transit",
      "green",
      "travel",
      "eco friendly",
      "two wheeler",
      "commute",
      "biking",
      "bike",
    ],
    mostLikelyCategoryName: "transportation",
  ),
  IconForCategory(
    icon: "bills.png",
    tags: [
      "bills",
      "money",
      "finance",
      "payment",
      "expense",
      "accounting",
      "due",
      "cost",
      "debt",
      "banking"
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "binoculars.png",
    tags: [
      "binoculars",
      "view",
      "watch",
      "observe",
      "nature",
      "travel",
      "outdoor",
      "hiking",
      "wildlife",
      "adventure"
    ],
    mostLikelyCategoryName: "Nature",
  ),
  IconForCategory(
    icon: "birdhouse.png",
    tags: [
      "birdhouse",
      "bird",
      "nature",
      "home",
      "wooden",
      "decor",
      "garden",
      "feeder",
      "outdoor",
      "winged"
    ],
    mostLikelyCategoryName: "Nature",
  ),
  IconForCategory(
    icon: "bookshelf.png",
    tags: [
      "bookshelf",
      "book",
      "library",
      "knowledge",
      "literature",
      "reading",
      "study",
      "education",
      "knowledge",
      "fiction"
    ],
    mostLikelyCategoryName: "Books",
  ),
  IconForCategory(
    icon: "bouquet.png",
    tags: [
      "bouquet",
      "flowers",
      "gift",
      "occasion",
      "love",
      "romantic",
      "spring",
      "wedding",
      "funeral",
      "arrangement"
    ],
    mostLikelyCategoryName: "Occasions",
  ),
  IconForCategory(
    icon: "box.png",
    tags: [
      "box",
      "storage",
      "package",
      "container",
      "delivery",
      "gift",
      "shipment",
      "parcel",
      "post",
      "storage"
    ],
    mostLikelyCategoryName: "Storage",
  ),
  IconForCategory(
    icon: "briefcase.png",
    tags: [
      "briefcase",
      "business",
      "work",
      "professional",
      "case",
      "job",
      "career",
      "suit",
      "attire",
      "office"
    ],
    mostLikelyCategoryName: "Business",
  ),
  IconForCategory(
    icon: "bubble-tea.png",
    tags: [
      "bubble tea",
      "tea",
      "drink",
      "bubbles",
      "boba",
      "refreshment",
      "caffeine",
      "flavor",
      "sweet",
      "milk"
    ],
    mostLikelyCategoryName: "Drinks",
  ),
  IconForCategory(
    icon: "butterfly.png",
    tags: [
      "butterfly",
      "nature",
      "insect",
      "pretty",
      "flying",
      "wings",
      "flower",
      "summer",
      "pollination",
      "delicate"
    ],
    mostLikelyCategoryName: "Nature",
  ),
  IconForCategory(
    icon: "cactus.png",
    tags: [
      "cactus",
      "desert",
      "plant",
      "nature",
      "spikes",
      "succulent",
      "drought",
      "arid",
      "prickly",
      "heat"
    ],
    mostLikelyCategoryName: "Nature",
  ),
  IconForCategory(
    icon: "cake.png",
    tags: [
      "cake",
      "dessert",
      "sweet",
      "party",
      "celebration",
      "birthday",
      "icing",
      "layer",
      "treat",
      "sugar"
    ],
    mostLikelyCategoryName: "Celebrations",
  ),
  IconForCategory(
    icon: "calculator.png",
    tags: [
      "calculator",
      "math",
      "numbers",
      "finance",
      "school",
      "science",
      "economics",
      "calculation",
      "arithmetic",
      "mathematics"
    ],
    mostLikelyCategoryName: "Mathematics & Finance",
  ),
  IconForCategory(
    icon: "calendar.png",
    tags: [
      "calendar",
      "planning",
      "schedule",
      "time",
      "date",
      "events",
      "appointments",
      "timeline",
      "organization",
      "reminder"
    ],
    mostLikelyCategoryName: "Time Management",
  ),
  IconForCategory(
    icon: "camera.png",
    tags: [
      "camera",
      "photography",
      "picture",
      "capture",
      "memories",
      "shoot",
      "lens",
      "art",
      "travel",
      "snaps"
    ],
    mostLikelyCategoryName: "Photography",
  ),
  IconForCategory(
    icon: "campfire.png",
    tags: [
      "campfire",
      "fire",
      "outdoor",
      "camping",
      "roast",
      "firewood",
      "night",
      "heat"
    ],
    mostLikelyCategoryName: "Outdoor & Camping",
  ),
  IconForCategory(
    icon: "candy.png",
    tags: [
      "candy",
      "sweet",
      "treat",
      "sugar",
      "chocolate",
      "gummy",
      "lollipop",
      "snack",
      "dessert",
      "cane"
    ],
    mostLikelyCategoryName: "Food & Treats",
  ),
  IconForCategory(
    icon: "car(1).png",
    tags: [
      "car",
      "transportation",
      "transit",
      "vehicle",
      "drive",
      "gas",
      "auto",
      "road",
      "speed",
      "commute",
      "mobile"
    ],
    mostLikelyCategoryName: "transportation",
  ),
  IconForCategory(
    icon: "car(2).png",
    tags: [
      "car",
      "transportation",
      "transit",
      "vehicle",
      "drive",
      "gas",
      "auto",
      "road",
      "speed",
      "commute",
      "mobile"
    ],
    mostLikelyCategoryName: "transportation",
  ),
  IconForCategory(
    icon: "car-key.png",
    tags: [
      "car key",
      "vehicle",
      "access",
      "transportation",
      "transit",
      "ignition",
      "start",
      "drive",
      "gas",
      "auto",
      "mobile",
      "keys",
    ],
    mostLikelyCategoryName: "transportation",
  ),
  IconForCategory(
    icon: "car.png",
    tags: [
      "car",
      "transportation",
      "transit",
      "vehicle",
      "drive",
      "gas",
      "auto",
      "road",
      "speed",
      "commute",
      "mobile"
    ],
    mostLikelyCategoryName: "transportation",
  ),
  IconForCategory(
    icon: "cards.png",
    tags: [
      "cards",
      "game",
      "deck",
      "poker",
      "solitaire",
      "spade",
      "hearts",
      "clubs",
      "diamonds",
      "fun"
    ],
    mostLikelyCategoryName: "Games",
  ),
  IconForCategory(
    icon: "cat.png",
    tags: [
      "cat",
      "pet",
      "animal",
      "feline",
      "kitten",
      "purr",
      "cuddle",
      "whiskers",
      "tail",
      "cute"
    ],
    mostLikelyCategoryName: "Pets",
  ),
  IconForCategory(
    icon: "chef-hat.png",
    tags: [
      "chef hat",
      "food",
      "cook",
      "kitchen",
      "bake",
      "prepare",
      "gourmet",
      "recipe",
      "dish",
      "cuisine",
      "cooking",
    ],
    mostLikelyCategoryName: "Cooking",
  ),
  IconForCategory(
    icon: "chess.png",
    tags: [
      "chess",
      "game",
      "board",
      "strategy",
      "mind",
      "checkmate",
      "knight",
      "bishop",
      "rook",
      "pawn"
    ],
    mostLikelyCategoryName: "Games",
  ),
  IconForCategory(
    icon: "cleaning.png",
    tags: [
      "cleaning",
      "household",
      "home",
      "organize",
      "clean",
      "mop",
      "brush",
      "dusting",
      "tidy",
      "maintenance"
    ],
    mostLikelyCategoryName: "Cleaning",
  ),
  IconForCategory(
    icon: "clipboard.png",
    tags: [
      "clipboard",
      "notes",
      "paper",
      "to-do",
      "write",
      "plan",
      "checklist",
      "memo",
      "task",
      "organized"
    ],
    mostLikelyCategoryName: "Task Management",
  ),
  IconForCategory(
    icon: "clock.png",
    tags: [
      "clock",
      "time",
      "watch",
      "hour",
      "minute",
      "second",
      "schedule",
      "alarm",
      "deadline",
      "punctual"
    ],
    mostLikelyCategoryName: "Schedule",
  ),
  IconForCategory(
    icon: "clothes-hanger.png",
    tags: [
      "clothes hanger",
      "wardrobe",
      "fashion",
      "outfit",
      "closet",
      "hang",
      "clothing",
      "store",
      "accessory",
      "garment"
    ],
    mostLikelyCategoryName: "Clothing",
  ),
  IconForCategory(
    icon: "coconut-tree.png",
    tags: [
      "coconut tree",
      "tropical",
      "island",
      "beach",
      "ocean",
      "palm",
      "nut",
      "relax",
      "vacation",
      "sun"
    ],
    mostLikelyCategoryName: "Vacation",
  ),
  IconForCategory(
    icon: "code.png",
    tags: [
      "code",
      "program",
      "computer",
      "develop",
      "write",
      "script",
      "language",
      "syntax",
      "algorithm",
      "debug"
    ],
    mostLikelyCategoryName: "Development",
  ),
  IconForCategory(
    icon: "coffee-cup.png",
    tags: [
      "coffee cup",
      "coffee",
      "caffeine",
      "morning",
      "break",
      "mug",
      "drink",
      "energy",
      "aroma",
      "coffee shop"
    ],
    mostLikelyCategoryName: "Drinks",
  ),
  IconForCategory(
    icon: "coffee.png",
    tags: [
      "coffee",
      "caffeine",
      "morning",
      "break",
      "energy",
      "aroma",
      "coffee shop",
      "grind",
      "bean",
      "roast"
    ],
    mostLikelyCategoryName: "Drinks",
  ),
  IconForCategory(
    icon: "color-palette.png",
    tags: [
      "color palette",
      "art",
      "design",
      "color",
      "brush",
      "paint",
      "palette",
      "create",
      "inspire",
      "mix"
    ],
    mostLikelyCategoryName: "Art",
  ),
  IconForCategory(
    icon: "compass.png",
    tags: [
      "compass",
      "navigation",
      "direction",
      "travel",
      "adventure",
      "explore",
      "map",
      "outdoors",
      "north",
      "orient"
    ],
    mostLikelyCategoryName: "Adventure",
  ),
  IconForCategory(
    icon: "confetti.png",
    tags: [
      "confetti",
      "party",
      "celebration",
      "festive",
      "decorate",
      "fun",
      "event",
      "streamers",
      "colorful",
      "stream"
    ],
    mostLikelyCategoryName: "Parties",
  ),
  IconForCategory(
    icon: "construction-helmet.png",
    tags: [
      "construction helmet",
      "worker",
      "hardhat",
      "job site",
      "safety",
      "construction",
      "tools",
      "build",
      "renovation",
      "repair"
    ],
    mostLikelyCategoryName: "Construction",
  ),
  IconForCategory(
    icon: "cowboy-hat.png",
    tags: [
      "cowboy hat",
      "western",
      "ranch",
      "horseback",
      "cowboy",
      "wildwest",
      "country",
      "outlaw",
      "saddle",
      "rodeo"
    ],
    mostLikelyCategoryName: "Rodeo",
  ),
  IconForCategory(
    icon: "credit-card.png",
    tags: [
      "credit card",
      "debit card",
      "payment",
      "finance",
      "money",
      "transaction",
      "buy",
      "shop",
      "swipe",
      "plastic"
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "cricket-bat.png",
    tags: [
      "cricket bat",
      "cricket",
      "sport",
      "game",
      "bat",
      "hit",
      "run",
      "field",
      "match",
      "score"
    ],
    mostLikelyCategoryName: "Sports",
  ),
  IconForCategory(
    icon: "cupcake.png",
    tags: [
      "cupcake",
      "bake",
      "dessert",
      "sweet",
      "cake",
      "frosting",
      "snack",
      "bakery",
      "sugar",
      "treat"
    ],
    mostLikelyCategoryName: "Bakery",
  ),
  IconForCategory(
    icon: "cutlery.png",
    tags: [
      "cutlery",
      "silverware",
      "dining",
      "eat",
      "fork",
      "knife",
      "spoon",
      "plate",
      "meal",
      "dinner"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "dental-care.png",
    tags: [
      "dental care",
      "teeth",
      "dentist",
      "oral",
      "health",
      "clean",
      "floss",
      "brush",
      "pain",
      "cavity"
    ],
    mostLikelyCategoryName: "Dental",
  ),
  IconForCategory(
    icon: "desktop-computer.png",
    tags: [
      "desktop computer",
      "computer",
      "desktop",
      "pc",
      "screen",
      "keyboard",
      "mouse",
      "tech",
      "electronics",
      "device"
    ],
    mostLikelyCategoryName: "Technology",
  ),
  IconForCategory(
    icon: "diamond.png",
    tags: [
      "diamond",
      "jewelry",
      "ring",
      "necklace",
      "precious",
      "stone",
      "bling",
      "shine",
      "rich",
      "luxury"
    ],
    mostLikelyCategoryName: "Jewelry",
  ),
  IconForCategory(
    icon: "dice.png",
    tags: [
      "dice",
      "game",
      "chance",
      "roll",
      "number",
      "gambling",
      "risk",
      "bet",
      "luck",
      "fun"
    ],
    mostLikelyCategoryName: "Games",
  ),
  IconForCategory(
    icon: "dog.png",
    tags: [
      "dog",
      "pet",
      "animal",
      "puppy",
      "canine",
      "woof",
      "friend",
      "doggy",
      "bark",
      "pooch"
    ],
    mostLikelyCategoryName: "Pets",
  ),
  IconForCategory(
    icon: "donut.png",
    tags: [
      "donut",
      "doughnut",
      "food",
      "sweet",
      "treat",
      "snack",
      "sugar",
      "glaze",
      "frosting",
      "sprinkles"
    ],
    mostLikelyCategoryName: "Sweets",
  ),
  IconForCategory(
    icon: "double-bed.png",
    tags: [
      "double bed",
      "bed",
      "sleep",
      "mattress",
      "rest",
      "sleepy",
      "night",
      "restful",
      "comfortable",
      "nighttime",
      "hotel",
      "stay",
      "hotel",
      "lodge",
    ],
    mostLikelyCategoryName: "Lodging",
  ),
  IconForCategory(
    icon: "dress.png",
    tags: [
      "dress",
      "clothing",
      "fashion",
      "gown",
      "apparel",
      "women",
      "girl",
      "female",
      "outfit",
      "robe"
    ],
    mostLikelyCategoryName: "Clothing",
  ),
  IconForCategory(
    icon: "dvd.png",
    tags: [
      "dvd",
      "movie",
      "disc",
      "entertainment",
      "film",
      "watch",
      "player",
      "cd",
      "disk",
      "show"
    ],
    mostLikelyCategoryName: "Entertainment",
  ),
  IconForCategory(
    icon: "earth.png",
    tags: [
      "earth",
      "planet",
      "nature",
      "world",
      "environment",
      "sphere",
      "land",
      "green",
      "gift",
      "home",
      "globe",
    ],
    mostLikelyCategoryName: "Nature",
  ),
  IconForCategory(
    icon: "emergency-exit.png",
    tags: [
      "emergency exit",
      "exit",
      "emergency",
      "escape",
      "sign",
      "direction",
      "door",
      "evacuation",
      "alarm",
      "fire"
    ],
    mostLikelyCategoryName: "Safety",
  ),
  IconForCategory(
    icon: "envelope.png",
    tags: [
      "envelope",
      "mail",
      "letter",
      "correspondence",
      "post",
      "send",
      "receive",
      "postal",
      "message",
      "greeting"
    ],
    mostLikelyCategoryName: "Mail",
  ),
  IconForCategory(
    icon: "essay.png",
    tags: [
      "essay",
      "paper",
      "write",
      "student",
      "study",
      "assignment",
      "homework",
      "project",
      "composition",
      "writing"
    ],
    mostLikelyCategoryName: "Study",
  ),
  IconForCategory(
    icon: "extinguisher.png",
    tags: [
      "extinguisher",
      "fire",
      "emergency",
      "put out",
      "stop",
      "hose",
      "spray",
      "equipment",
      "safety",
      "firefighter"
    ],
    mostLikelyCategoryName: "Safety",
  ),
  IconForCategory(
    icon: "face-mask.png",
    tags: [
      "face mask",
      "mask",
      "health",
      "medicine",
      "protection",
      "virus",
      "germ",
      "outbreak",
      "sickness",
      "surgical"
    ],
    mostLikelyCategoryName: "Health",
  ),
  IconForCategory(
    icon: "fast-food.png",
    tags: [
      "fast food",
      "food",
      "meal",
      "snack",
      "hamburger",
      "french fries",
      "drive thru",
      "takeout",
      "convenient",
      "junk"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "favourite.png",
    tags: [
      "favourite",
      "star",
      "bookmark",
      "preferred",
      "chosen",
      "liked",
      "preferred",
      "picked",
      "picked",
      "highlighted"
    ],
    mostLikelyCategoryName: "Favourites",
  ),
  IconForCategory(
    icon: "feeding-bottle.png",
    tags: [
      "feeding bottle",
      "baby",
      "nursing",
      "infant",
      "bottle",
      "nipple",
      "suckle",
      "nourishment",
      "milk",
      "nutrition"
    ],
    mostLikelyCategoryName: "Baby",
  ),
  IconForCategory(
    icon: "fireplace.png",
    tags: [
      "fireplace",
      "hearth",
      "home",
      "warmth",
      "fire",
      "flame",
      "cozy",
      "coziness",
      "heat",
      "chimney"
    ],
    mostLikelyCategoryName: "Home",
  ),
  IconForCategory(
    icon: "fish.png",
    tags: [
      "fish",
      "sea",
      "ocean",
      "aquatic",
      "underwater",
      "fishy",
      "swimming",
      "fishing",
      "catch",
      "fish meal"
    ],
    mostLikelyCategoryName: "Fish",
  ),
  IconForCategory(
    icon: "fishing-rod.png",
    tags: [
      "fishing rod",
      "fishing",
      "catch",
      "angler",
      "fish",
      "bait",
      "reel",
      "hook",
      "line",
      "fish finder"
    ],
    mostLikelyCategoryName: "Fishing",
  ),
  IconForCategory(
    icon: "flashlight.png",
    tags: [
      "flashlight",
      "light",
      "torch",
      "flash",
      "beam",
      "illuminate",
      "emergency",
      "power outage",
      "portable",
      "battery"
    ],
    mostLikelyCategoryName: "Light",
  ),
  IconForCategory(
    icon: "flat-tire.png",
    tags: [
      "flat tire",
      "tire",
      "car",
      "vehicle",
      "deflated",
      "punctured",
      "wheel",
      "damaged",
      "repair",
      "spare"
    ],
    mostLikelyCategoryName: "Car",
  ),
  IconForCategory(
    icon: "flower.png",
    tags: [
      "flower",
      "bloom",
      "nature",
      "beauty",
      "garden",
      "spring",
      "petals",
      "floral",
      "vibrant",
      "delicate"
    ],
    mostLikelyCategoryName: "Flowers",
  ),
  IconForCategory(
    icon: "folder.png",
    tags: [
      "folder",
      "file",
      "organize",
      "documents",
      "paperwork",
      "files",
      "office",
      "data",
      "information",
      "records"
    ],
    mostLikelyCategoryName: "Files",
  ),
  IconForCategory(
    icon: "food-tray.png",
    tags: [
      "food tray",
      "meal",
      "dining",
      "plate",
      "restaurant",
      "fast food",
      "cafeteria",
      "serving",
      "dish",
      "restaurant"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "fried-potatoes.png",
    tags: [
      "fried potatoes",
      "potatoes",
      "fry",
      "french fries",
      "crispy",
      "fried food",
      "snack",
      "side dish",
      "fast food",
      "potato"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "fruit.png",
    tags: [
      "fruit",
      "food",
      "natural",
      "health",
      "sweet",
      "juicy",
      "vitamin",
      "nutritious",
      "diet",
      "delicious"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "gamepad.png",
    tags: [
      "gamepad",
      "gaming",
      "videogames",
      "electronics",
      "joystick",
      "controller",
      "entertainment",
      "leisure",
      "fun",
      "technology"
    ],
    mostLikelyCategoryName: "Gaming",
  ),
  IconForCategory(
    icon: "garden-hose.png",
    tags: [
      "garden hose",
      "garden",
      "watering",
      "hose",
      "gardening",
      "tools",
      "lawn",
      "plants",
      "irrigation",
      "outdoors"
    ],
    mostLikelyCategoryName: "Gardening",
  ),
  IconForCategory(
    icon: "gas-station.png",
    tags: [
      "gas station",
      "fuel",
      "petrol",
      "gasoline",
      "fill up",
      "refuel",
      "automobile",
      "transportation",
      "transit",
      "road trip",
      "gas",
      "fuel",
    ],
    mostLikelyCategoryName: "Gas",
  ),
  IconForCategory(
    icon: "gears.png",
    tags: [
      "gears",
      "mechanics",
      "engineering",
      "machinery",
      "industry",
      "technology",
      "machine",
      "parts",
      "work",
      "tools",
      "utilities",
      "settings",
      "cog",
      "gear",
      "control",
      "preferences",
      "configure",
      "options",
      "adjust",
      "customize",
    ],
    mostLikelyCategoryName: "Engineering",
  ),
  IconForCategory(
    icon: "gift.png",
    tags: [
      "gift",
      "present",
      "wrapped",
      "birthday",
      "occasion",
      "surprise",
      "give",
      "receive",
      "wrapping paper",
      "bow",
      "christmas",
      "holidays",
    ],
    mostLikelyCategoryName: "Gifts",
  ),
  IconForCategory(
    icon: "golf-ball.png",
    tags: [
      "golf ball",
      "golf",
      "sports",
      "game",
      "clubs",
      "green",
      "fairway",
      "putt",
      "tee",
      "ball"
    ],
    mostLikelyCategoryName: "Golf",
  ),
  IconForCategory(
    icon: "golf.png",
    tags: [
      "golf",
      "sports",
      "game",
      "clubs",
      "green",
      "fairway",
      "putt",
      "tee",
      "ball",
      "outdoors"
    ],
    mostLikelyCategoryName: "Golf",
  ),
  IconForCategory(
    icon: "graduation.png",
    tags: [
      "graduation",
      "ceremony",
      "education",
      "school",
      "diploma",
      "cap and gown",
      "commencement",
      "congrats",
      "success",
      "future",
      "hat",
      "student",
      "studying",
    ],
    mostLikelyCategoryName: "Education",
  ),
  IconForCategory(
    icon: "groceries.png",
    tags: [
      "groceries",
      "shopping",
      "food",
      "supermarket",
      "grocery store",
      "cart",
      "aisle",
      "checkout",
      "products",
      "buy"
    ],
    mostLikelyCategoryName: "Groceries",
  ),
  IconForCategory(
    icon: "guitar.png",
    tags: [
      "guitar",
      "music",
      "instrument",
      "acoustic",
      "electric",
      "strings",
      "song",
      "performance",
      "rock",
      "classical"
    ],
    mostLikelyCategoryName: "Music",
  ),
  IconForCategory(
    icon: "haircut.png",
    tags: [
      "haircut",
      "beauty",
      "salon",
      "hair",
      "style",
      "barber",
      "scissors",
      "razor",
      "beard",
      "hairdresser"
    ],
    mostLikelyCategoryName: "Barber",
  ),
  IconForCategory(
    icon: "hamster.png",
    tags: [
      "hamster",
      "pet",
      "animal",
      "rodent",
      "cage",
      "small",
      "cute",
      "fur",
      "whiskers",
      "critter"
    ],
    mostLikelyCategoryName: "Pets",
  ),
  IconForCategory(
    icon: "hand-fan.png",
    tags: [
      "hand fan",
      "cooling",
      "summer",
      "fan",
      "relaxation",
      "heat",
      "outdoor",
      "ventilation",
      "wind",
      "breeze"
    ],
    mostLikelyCategoryName: "Cooling",
  ),
  IconForCategory(
    icon: "headphones.png",
    tags: [
      "headphones",
      "music",
      "listening",
      "audio",
      "headset",
      "earphones",
      "sound",
      "entertainment",
      "privacy",
      "communication"
    ],
    mostLikelyCategoryName: "Music",
  ),
  IconForCategory(
    icon: "healthcare-and-medical.png",
    tags: [
      "healthcare",
      "medical",
      "health",
      "care",
      "medicine",
      "doctor",
      "nurse",
      "hospital",
      "clinic",
      "treatment"
    ],
    mostLikelyCategoryName: "Healthcare",
  ),
  IconForCategory(
    icon: "hearing-aid.png",
    tags: [
      "hearing aid",
      "hearing",
      "listening",
      "ear",
      "audiology",
      "disability",
      "support",
      "hearing loss",
      "sounds",
      "device"
    ],
    mostLikelyCategoryName: "Hearing",
  ),
  IconForCategory(
    icon: "heart.png",
    tags: [
      "heart",
      "love",
      "affection",
      "emotion",
      "romance",
      "health",
      "medical",
      "valentine",
      "care",
      "feeling"
    ],
    mostLikelyCategoryName: "Love",
  ),
  IconForCategory(
    icon: "helicopter.png",
    tags: [
      "helicopter",
      "flight",
      "air",
      "transportation",
      "transit",
      "military",
      "rescue",
      "emergency",
      "aircraft",
      "airport",
      "travel"
    ],
    mostLikelyCategoryName: "Flight",
  ),
  IconForCategory(
    icon: "high-heels.png",
    tags: [
      "high heels",
      "fashion",
      "footwear",
      "shoes",
      "women",
      "elegant",
      "style",
      "attire",
      "foot"
    ],
    mostLikelyCategoryName: "Fashion",
  ),
  IconForCategory(
    icon: "hiking-backpack.png",
    tags: [
      "hiking backpack",
      "hiking",
      "outdoor",
      "adventure",
      "backpack",
      "trekking",
      "mountain",
      "travel",
      "camping",
      "sport"
    ],
    mostLikelyCategoryName: "Outdoors",
  ),
  IconForCategory(
    icon: "hockey-stick.png",
    tags: [
      "hockey stick",
      "hockey",
      "sport",
      "ice",
      "rink",
      "game",
      "stick",
      "equipment",
      "team",
      "play"
    ],
    mostLikelyCategoryName: "Sports",
  ),
  IconForCategory(
    icon: "home2.png",
    tags: [
      "home2",
      "home",
      "house",
      "living",
      "real estate",
      "residence",
      "family",
      "property",
      "dwelling",
      "shelter"
    ],
    mostLikelyCategoryName: "Housing",
  ),
  IconForCategory(
    icon: "hoodie.png",
    tags: [
      "hoodie",
      "clothing",
      "apparel",
      "fashion",
      "sweatshirt",
      "casual",
      "comfort",
      "wardrobe",
      "outerwear",
      "streetwear"
    ],
    mostLikelyCategoryName: "Clothing",
  ),
  IconForCategory(
    icon: "house.png",
    tags: [
      "house",
      "home",
      "residence",
      "property",
      "real estate",
      "dwelling",
      "domicile",
      "abode",
      "residency",
      "property ownership"
    ],
    mostLikelyCategoryName: "Housing",
  ),
  IconForCategory(
    icon: "ice-cream-cup.png",
    tags: [
      "ice cream",
      "dessert",
      "sweet",
      "cold",
      "treat",
      "cone",
      "scoop",
      "vanilla",
      "chocolate",
      "strawberry"
    ],
    mostLikelyCategoryName: "Sweets",
  ),
  IconForCategory(
    icon: "idea.png",
    tags: [
      "idea",
      "innovation",
      "creation",
      "concept",
      "inspiration",
      "brainstorm",
      "imagination",
      "ingenuity",
      "invention",
      "originality"
    ],
    mostLikelyCategoryName: "Personal Development",
  ),
  IconForCategory(
    icon: "image.png",
    tags: [
      "image",
      "picture",
      "graphic",
      "photograph",
      "art",
      "digital",
      "visual",
      "drawing",
      "painting",
      "artistic"
    ],
    mostLikelyCategoryName: "Photography",
  ),
  IconForCategory(
    icon: "investment.png",
    tags: [
      "investment",
      "finance",
      "stock",
      "portfolio",
      "return",
      "money",
      "fund",
      "capital",
      "risk",
      "security"
    ],
    mostLikelyCategoryName: "Investments",
  ),
  IconForCategory(
    icon: "key.png",
    tags: [
      "key",
      "lock",
      "security",
      "access",
      "door",
      "unlock",
      "open",
      "privacy",
      "secret",
      "entry"
    ],
    mostLikelyCategoryName: "Security",
  ),
  IconForCategory(
    icon: "kite.png",
    tags: [
      "kite",
      "flight",
      "outdoor",
      "activity",
      "toy",
      "summer",
      "beach",
      "wind",
      "fun",
      "kids"
    ],
    mostLikelyCategoryName: "Outdoors",
  ),
  IconForCategory(
    icon: "laptop.png",
    tags: [
      "laptop",
      "computer",
      "technology",
      "electronics",
      "device",
      "notebook",
      "portable",
      "PC",
      "mobile",
      "office"
    ],
    mostLikelyCategoryName: "Technology",
  ),
  IconForCategory(
    icon: "lighthouse.png",
    tags: [
      "lighthouse",
      "beacon",
      "harbor",
      "navigation",
      "sea",
      "coast",
      "port",
      "guide",
      "ocean",
      "tower"
    ],
    mostLikelyCategoryName: "Vacation",
  ),
  IconForCategory(
    icon: "lightning-bolt.png",
    tags: [
      "lightning bolt",
      "electrical",
      "power",
      "storm",
      "thunder",
      "energy",
      "hazard",
      "warning",
      "nature",
      "shock"
    ],
    mostLikelyCategoryName: "Electricity",
  ),
  IconForCategory(
    icon: "locomotive.png",
    tags: [
      "locomotive",
      "train",
      "transportation",
      "transit",
      "steam",
      "engine",
      "rails",
      "journey",
      "travel",
      "vehicle",
      "wheels"
    ],
    mostLikelyCategoryName: "transportation",
  ),
  IconForCategory(
    icon: "magnifying-glass.png",
    tags: [
      "magnifying glass",
      "search",
      "detective",
      "investigation",
      "enlarge",
      "zoom",
      "look",
      "examine",
      "inspect",
      "study"
    ],
    mostLikelyCategoryName: "Investigations",
  ),
  IconForCategory(
    icon: "make-up.png",
    tags: [
      "make up",
      "beauty",
      "cosmetics",
      "glamour",
      "lipstick",
      "eyeshadow",
      "mascara",
      "powder",
      "blush",
      "polish"
    ],
    mostLikelyCategoryName: "Beauty",
  ),
  IconForCategory(
    icon: "makeup(1).png",
    tags: [
      "makeup(1)",
      "beauty",
      "cosmetics",
      "glamour",
      "lipstick",
      "eyeshadow",
      "mascara",
      "powder",
      "blush",
      "polish"
    ],
    mostLikelyCategoryName: "Beauty",
  ),
  IconForCategory(
    icon: "makeup.png",
    tags: [
      "makeup",
      "beauty",
      "cosmetics",
      "glamour",
      "lipstick",
      "eyeshadow",
      "mascara",
      "powder",
      "blush",
      "polish"
    ],
    mostLikelyCategoryName: "Beauty",
  ),
  IconForCategory(
    icon: "map.png",
    tags: [
      "map",
      "territory",
      "route",
      "journey",
      "plan",
      "destination",
      "geography",
      "atlas",
      "cartography",
      "navigation"
    ],
    mostLikelyCategoryName: "Travel",
  ),
  IconForCategory(
    icon: "mixer.png",
    tags: [
      "mixer",
      "kitchen",
      "cooking",
      "baking",
      "tools",
      "blender",
      "whisk",
      "beater",
      "device",
      "equipment"
    ],
    mostLikelyCategoryName: "Cooking",
  ),
  IconForCategory(
    icon: "money-bag.png",
    tags: [
      "money bag",
      "cash",
      "finance",
      "economy",
      "wealth",
      "treasure",
      "payment",
      "income",
      "purchase",
      "capital",
      "dollar",
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "money-bag2.png",
    tags: [
      "money bag",
      "finance",
      "savings",
      "wealth",
      "cash",
      "budget",
      "investment",
      "piggy bank",
      "banking",
      "finances",
      "dollar",
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "money.png",
    tags: [
      "money",
      "finance",
      "cash",
      "income",
      "spending",
      "payment",
      "economy",
      "banking",
      "finances",
      "payment",
      "dollar",
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "music.png",
    tags: [
      "music",
      "audio",
      "song",
      "melody",
      "rhythm",
      "notes",
      "instruments",
      "concert",
      "music industry",
      "sound"
    ],
    mostLikelyCategoryName: "Music",
  ),
  IconForCategory(
    icon: "necklace.png",
    tags: [
      "necklace",
      "jewelry",
      "accessories",
      "neckwear",
      "gold",
      "silver",
      "diamond",
      "fashion",
      "gemstones",
      "designer"
    ],
    mostLikelyCategoryName: "Jewelry",
  ),
  IconForCategory(
    icon: "noodles.png",
    tags: [
      "noodles",
      "food",
      "cuisine",
      "ramen",
      "spaghetti",
      "pasta",
      "dinner",
      "lunch",
      "meal",
      "nourishment"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "note.png",
    tags: [
      "note",
      "write",
      "pencil",
      "paper",
      "memo",
      "jot",
      "reminder",
      "message",
      "record",
      "notebook"
    ],
    mostLikelyCategoryName: "Notes",
  ),
  IconForCategory(
    icon: "oldschool-telephone.png",
    tags: [
      "oldschool telephone",
      "communication",
      "call",
      "talk",
      "telephone",
      "dial",
      "old school",
      "vintage",
      "telecommunication",
      "antique",
      "phone"
    ],
    mostLikelyCategoryName: "Phone",
  ),
  IconForCategory(
    icon: "open-book.png",
    tags: [
      "open book",
      "education",
      "library",
      "knowledge",
      "study",
      "read",
      "literature",
      "research",
      "textbook",
      "book"
    ],
    mostLikelyCategoryName: "Books",
  ),
  IconForCategory(
    icon: "open-email.png",
    tags: [
      "open email",
      "communication",
      "email",
      "message",
      "inbox",
      "mail",
      "send",
      "receive",
      "letter",
      "correspondence"
    ],
    mostLikelyCategoryName: "Communication",
  ),
  IconForCategory(
    icon: "padlock.png",
    tags: [
      "padlock",
      "security",
      "lock",
      "protection",
      "privacy",
      "restricted",
      "safeguard",
      "encryption",
      "password",
      "privacy"
    ],
    mostLikelyCategoryName: "Security",
  ),
  IconForCategory(
    icon: "parking.png",
    tags: [
      "parking",
      "car",
      "vehicle",
      "garage",
      "lot",
      "space",
      "street",
      "ticket",
      "meter",
      "violation"
    ],
    mostLikelyCategoryName: "Parking",
  ),
  IconForCategory(
    icon: "pen.png",
    tags: [
      "pen",
      "writing",
      "ink",
      "note",
      "document",
      "signature",
      "contract",
      "agreement",
      "journal",
      "diary"
    ],
    mostLikelyCategoryName: "Writing",
  ),
  IconForCategory(
    icon: "pencil.png",
    tags: [
      "pencil",
      "drawing",
      "sketch",
      "art",
      "design",
      "erase",
      "graphite",
      "sketchbook",
      "notebook",
      "draft"
    ],
    mostLikelyCategoryName: "Drawing",
  ),
  IconForCategory(
    icon: "piano.png",
    tags: [
      "piano",
      "music",
      "instrument",
      "keys",
      "scale",
      "compose",
      "melody",
      "symphony",
      "tune",
      "performance"
    ],
    mostLikelyCategoryName: "Music",
  ),
  IconForCategory(
    icon: "picnic.png",
    tags: [
      "picnic",
      "outdoor",
      "food",
      "lunch",
      "basket",
      "blanket",
      "gather",
      "enjoy",
      "nature",
      "relax"
    ],
    mostLikelyCategoryName: "Picnics",
  ),
  IconForCategory(
    icon: "piggy-bank.png",
    tags: [
      "piggy bank",
      "savings",
      "penny",
      "pig",
      "money",
      "coins",
      "child",
      "gift",
      "future",
      "wealth",
    ],
    mostLikelyCategoryName: "Savings",
  ),
  IconForCategory(
    icon: "pizza.png",
    tags: [
      "pizza",
      "food",
      "slice",
      "delivery",
      "cheese",
      "topping",
      "crust",
      "italian",
      "restaurant",
      "meal"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "plane.png",
    tags: [
      "plane",
      "travel",
      "airline",
      "flight",
      "ticket",
      "vacation",
      "destination",
      "airport",
      "journey",
      "aviation"
    ],
    mostLikelyCategoryName: "Travel",
  ),
  IconForCategory(
    icon: "plant.png",
    tags: [
      "plant",
      "nature",
      "flower",
      "leaf",
      "greenery",
      "garden",
      "pot",
      "soil",
      "green",
      "life"
    ],
    mostLikelyCategoryName: "Plants",
  ),
  IconForCategory(
    icon: "popcorn.png",
    tags: [
      "popcorn",
      "movie",
      "theater",
      "snack",
      "entertainment",
      "cinema",
      "film",
      "butter",
      "popcorn_machine",
      "kernel"
    ],
    mostLikelyCategoryName: "Entertainment",
  ),
  IconForCategory(
    icon: "popsicle.png",
    tags: [
      "popsicle",
      "ice_cream",
      "summer",
      "frozen_treat",
      "sweet",
      "refreshing",
      "cone",
      "stick",
      "fruit",
      "dessert"
    ],
    mostLikelyCategoryName: "Sweets",
  ),
  IconForCategory(
    icon: "price-tag.png",
    tags: [
      "price tag",
      "cost",
      "price",
      "value",
      "price tag",
      "price sticker",
      "price label",
      "price mark",
      "price marker",
      "price stick"
    ],
    mostLikelyCategoryName: "Shopping",
  ),
  IconForCategory(
    icon: "pumpkin.png",
    tags: [
      "pumpkin",
      "autumn",
      "halloween",
      "fall",
      "harvest",
      "jack_o'lantern",
      "orange",
      "vegetable",
      "carve",
      "decor"
    ],
    mostLikelyCategoryName: "Holidays",
  ),
  IconForCategory(
    icon: "recycling.png",
    tags: [
      "recycling",
      "environment",
      "sustainability",
      "waste",
      "recycle",
      "garbage",
      "upcycle",
      "trash",
      "reuse",
      "reduce"
    ],
    mostLikelyCategoryName: "Environment",
  ),
  IconForCategory(
    icon: "rent.png",
    tags: [
      "rent",
      "lease",
      "apartment",
      "house",
      "home",
      "payment",
      "property",
      "mortgage",
      "rental",
      "tenant"
    ],
    mostLikelyCategoryName: "Rent",
  ),
  IconForCategory(
    icon: "robot.png",
    tags: [
      "robot",
      "machine",
      "artificial_intelligence",
      "automation",
      "technology",
      "cyborg",
      "electronics",
      "mechanical",
      "android",
      "gadget"
    ],
    mostLikelyCategoryName: "Technology",
  ),
  IconForCategory(
    icon: "safety-helmet.png",
    tags: [
      "safety helmet",
      "construction",
      "workplace",
      "protection",
      "hardhat",
      "helmet",
      "construction_worker",
      "hard hat",
      "job site",
      "building"
    ],
    mostLikelyCategoryName: "Construction",
  ),
  IconForCategory(
    icon: "bank.png",
    tags: [
      "bank",
      "finance",
      "money",
      "deposit",
      "withdrawal",
      "ATM",
      "savings",
      "check",
      "loan",
      "investment"
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "sandals.png",
    tags: [
      "sandals",
      "shoes",
      "beach",
      "summer",
      "footwear",
      "foot",
      "warm",
      "casual",
      "vacation",
      "fashion"
    ],
    mostLikelyCategoryName: "Vacation",
  ),
  IconForCategory(
    icon: "school-backpack.png",
    tags: [
      "school backpack",
      "education",
      "books",
      "study",
      "school",
      "teacher",
      "student",
      "class",
      "homework",
      "knowledge"
    ],
    mostLikelyCategoryName: "Education",
  ),
  IconForCategory(
    icon: "science.png",
    tags: [
      "science",
      "lab",
      "experiment",
      "research",
      "discovery",
      "biology",
      "chemistry",
      "physics",
      "mathematics",
      "knowledge"
    ],
    mostLikelyCategoryName: "Science",
  ),
  IconForCategory(
    icon: "settings.png",
    tags: [
      "settings",
      "cog",
      "gear",
      "control",
      "preferences",
      "configure",
      "options",
      "adjust",
      "customize",
      "setup",
      "utilities",
      "handyman",
      "gears",
      "mechanics",
      "engineering",
      "machinery",
      "industry",
      "technology",
      "machine",
      "parts",
      "work",
      "tools",
    ],
    mostLikelyCategoryName: "Settings",
  ),
  IconForCategory(
    icon: "shopping.png",
    tags: [
      "shopping",
      "buy",
      "sell",
      "market",
      "store",
      "retail",
      "cart",
      "checkout",
      "purchase",
      "goods"
    ],
    mostLikelyCategoryName: "Shopping",
  ),
  IconForCategory(
    icon: "smartphone.png",
    tags: [
      "smartphone",
      "cellphone",
      "mobile",
      "telephone",
      "communication",
      "call",
      "text",
      "email",
      "internet",
      "device",
      'phone'
    ],
    mostLikelyCategoryName: "Phone",
  ),
  IconForCategory(
    icon: "sneakers.png",
    tags: [
      "sneakers",
      "shoes",
      "running",
      "sport",
      "exercise",
      "gym",
      "athletic",
      "workout",
      "footwear",
      "fashion"
    ],
    mostLikelyCategoryName: "Sportswear",
  ),
  IconForCategory(
    icon: "snooker.png",
    tags: [
      "snooker",
      "billiards",
      "pool",
      "cue",
      "stick",
      "game",
      "table",
      "competition",
      "skill",
      "fun"
    ],
    mostLikelyCategoryName: "Gaming",
  ),
  IconForCategory(
    icon: "snorkling.png",
    tags: [
      "snorkling",
      "diving",
      "ocean",
      "underwater",
      "scuba",
      "snorkel",
      "swimming",
      "beach",
      "sea",
      "watersports"
    ],
    mostLikelyCategoryName: "Snorkling",
  ),
  IconForCategory(
    icon: "snowman.png",
    tags: [
      "snowman",
      "winter",
      "snow",
      "cold",
      "ice",
      "frosty",
      "holiday",
      "seasonal",
      "snowflake",
      "festive",
      "christmas",
    ],
    mostLikelyCategoryName: "Winter",
  ),
  IconForCategory(
    icon: "socket.png",
    tags: [
      "socket",
      "electricity",
      "power",
      "outlet",
      "electronics",
      "plug",
      "energy",
      "electrical",
      "outlet",
      "device"
    ],
    mostLikelyCategoryName: "Electricity",
  ),
  IconForCategory(
    icon: "speaker.png",
    tags: [
      "speaker",
      "audio",
      "sound",
      "music",
      "volume",
      "loudspeaker",
      "device",
      "volume",
      "dj",
      "electronics"
    ],
    mostLikelyCategoryName: "Audio",
  ),
  IconForCategory(
    icon: "sports.png",
    tags: [
      "sports",
      "fitness",
      "exercise",
      "active",
      "game",
      "athletics",
      "training",
      "competition",
      "healthy",
      "activity"
    ],
    mostLikelyCategoryName: "Sports",
  ),
  IconForCategory(
    icon: "target.png",
    tags: [
      "target",
      "aim",
      "goal",
      "objective",
      "focus",
      "shoot",
      "bullseye",
      "hit",
      "archery",
      "challenge"
    ],
    mostLikelyCategoryName: "Goals",
  ),
  IconForCategory(
    icon: "teddy-bear.png",
    tags: [
      "teddy bear",
      "stuffed animal",
      "plush",
      "toy",
      "cute",
      "love",
      "childhood",
      "gift",
      "hug",
      "soft"
    ],
    mostLikelyCategoryName: "Toys",
  ),
  IconForCategory(
    icon: "telescope.png",
    tags: [
      "telescope",
      "astronomy",
      "observation",
      "space",
      "science",
      "stargazing",
      "telescope",
      "universe",
      "astronomer",
      "night"
    ],
    mostLikelyCategoryName: "Astronomy",
  ),
  IconForCategory(
    icon: "tie.png",
    tags: [
      "tie",
      "fashion",
      "business",
      "professional",
      "necktie",
      "accessory",
      "style",
      "formal",
      "gentleman",
      "dress"
    ],
    mostLikelyCategoryName: "Business",
  ),
  IconForCategory(
    icon: "tram.png",
    tags: [
      "tram",
      "public transportation",
      "commute",
      "transport",
      "transit",
      "train",
      "bus",
      "metro",
      "streetcar",
      "light rail",
      "subway"
    ],
    mostLikelyCategoryName: "transportation",
  ),
  IconForCategory(
    icon: "trash.png",
    tags: [
      "trash",
      "garbage",
      "waste",
      "recycling",
      "litter",
      "rubbish",
      "junk",
      "dumpster",
      "landfill",
      "compost"
    ],
    mostLikelyCategoryName: "Garbage",
  ),
  IconForCategory(
    icon: "tree.png",
    tags: [
      "tree",
      "nature",
      "outdoors",
      "forest",
      "park",
      "sapling",
      "foliage",
      "branch",
      "leaf",
      "wood"
    ],
    mostLikelyCategoryName: "Nature",
  ),
  IconForCategory(
    icon: "trophy.png",
    tags: [
      "trophy",
      "award",
      "winning",
      "championship",
      "prize",
      "medal",
      "competition",
      "victory",
      "plaque",
      "ribbon"
    ],
    mostLikelyCategoryName: "Awards",
  ),
  IconForCategory(
    icon: "tshirt.png",
    tags: [
      "tshirt",
      "clothing",
      "fashion",
      "apparel",
      "garment",
      "outfit",
      "threads",
      "wear",
      "attire",
      "textile"
    ],
    mostLikelyCategoryName: "Clothing",
  ),
  IconForCategory(
    icon: "umbrella.png",
    tags: [
      "umbrella",
      "rain",
      "storm",
      "wet",
      "water",
      "shade",
      "sun",
      "parasol",
      "drought",
      "protection"
    ],
    mostLikelyCategoryName: "Rain",
  ),
  IconForCategory(
    icon: "user.png",
    tags: [
      "user",
      "person",
      "human",
      "account",
      "customer",
      "identity",
      "client",
      "member",
      "subscriber",
      "consumer"
    ],
    mostLikelyCategoryName: "People",
  ),
  IconForCategory(
    icon: "vision.png",
    tags: [
      "vision",
      "eyeball",
      "optics",
      "sight",
      "perception",
      "observation",
      "view",
      "perspective",
      "gaze",
      "look"
    ],
    mostLikelyCategoryName: "Vision",
  ),
  IconForCategory(
    icon: "wallet.png",
    tags: [
      "wallet",
      "money",
      "cash",
      "purse",
      "billfold",
      "credit cards",
      "finances",
      "expenses",
      "payments",
      "purchases"
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "washing-machine.png",
    tags: [
      "washingmachine",
      "laundry",
      "appliances",
      "clothes",
      "wash",
      "machine",
      "home",
      "detergent",
      "spin",
      "dry"
    ],
    mostLikelyCategoryName: "Laundry",
  ),
  IconForCategory(
    icon: "watch.png",
    tags: [
      "watch",
      "time",
      "accessory",
      "wristwatch",
      "fashion",
      "style",
      "chronometer",
      "hours",
      "minutes",
      "seconds"
    ],
    mostLikelyCategoryName: "Watches",
  ),
  IconForCategory(
    icon: "watering-can.png",
    tags: [
      "watering can",
      "garden",
      "plants",
      "sprinkler",
      "water",
      "green",
      "nature",
      "flower",
      "grow",
      "irrigation"
    ],
    mostLikelyCategoryName: "Gardening",
  ),
  IconForCategory(
    icon: "weight.png",
    tags: [
      "weight",
      "gym",
      "exercise",
      "fitness",
      "training",
      "muscles",
      "lifting",
      "workout",
      "scale",
      "health"
    ],
    mostLikelyCategoryName: "Fitness",
  ),
  IconForCategory(
    icon: "yarn-ball.png",
    tags: [
      "yarn ball",
      "knitting",
      "crochet",
      "wool",
      "yarn",
      "thread",
      "needle",
      "stitch",
      "hobby",
      "craft"
    ],
    mostLikelyCategoryName: "Crafting",
  ),
  IconForCategory(
    icon: "cottage.png",
    tags: [
      "cottage",
      "home",
      "real estate",
      "property",
      "residence",
      "dwelling",
      "abode",
      "house",
      "domicile",
      "shelter"
    ],
    mostLikelyCategoryName: "Lodging",
  ),
  IconForCategory(
    icon: "furniture.png",
    tags: [
      "furniture",
      "chair",
      "table",
      "sofa",
      "bed",
      "couch",
      "decor",
      "home",
      "interior",
      "furnishing",
      "room"
    ],
    mostLikelyCategoryName: "Furniture",
  ),
  IconForCategory(
    icon: "shopping-cart.png",
    tags: [
      "shopping cart",
      "purchase",
      "buy",
      "cart",
      "store",
      "market",
      "shop",
      "retail",
      "consumer",
      "goods"
    ],
    mostLikelyCategoryName: "Shopping",
  ),
  IconForCategory(
    icon: "crypto.png",
    tags: [
      "crypto",
      "digital currency",
      "bitcoin",
      "ethereum",
      "blockchain",
      "mining",
      "altcoin",
      "wallet",
      "trading",
      "finance"
    ],
    mostLikelyCategoryName: "Cryptocurrency",
  ),
  IconForCategory(
    icon: "media-content.png",
    tags: [
      "media",
      "content",
      "entertainment",
      "movies",
      "music",
      "streaming",
      "television",
      "shows",
      "games",
      "books"
    ],
    mostLikelyCategoryName: "Media",
  ),
  IconForCategory(
    icon: "television.png",
    tags: [
      "television",
      "screen",
      "entertainment",
      "electronics",
      "media",
      "television set",
      "tv",
      "display",
      "device",
      "watching"
    ],
    mostLikelyCategoryName: "Television",
  ),
  IconForCategory(
    icon: "radio.png",
    tags: [
      "radio",
      "music",
      "communication",
      "electronics",
      "device",
      "antenna",
      "station",
      "broadcast",
      "receiver",
      "tuner"
    ],
    mostLikelyCategoryName: "Radio",
  ),
  IconForCategory(
    icon: "feathers.png",
    tags: [
      "feathers",
      "birds",
      "wing",
      "flight",
      "nature",
      "plumes",
      "birds",
      "feathered",
      "birdwatching",
      "ornithology"
    ],
    mostLikelyCategoryName: "Feathers",
  ),
  IconForCategory(
    icon: "skincare.png",
    tags: [
      "skincare",
      "beauty",
      "cosmetics",
      "skin",
      "care",
      "face",
      "products",
      "routine",
      "health",
      "wellness"
    ],
    mostLikelyCategoryName: "Skincare",
  ),
  IconForCategory(
    icon: "toiletries.png",
    tags: [
      "toiletries",
      "bathroom",
      "essentials",
      "hygiene",
      "personal care",
      "products",
      "toothbrush",
      "soap",
      "shampoo",
      "towels"
    ],
    mostLikelyCategoryName: "Toiletries",
  ),
  IconForCategory(
    icon: "pet-bowl.png",
    tags: [
      "pet-bowl",
      "pets",
      "animals",
      "food",
      "feeding",
      "cat",
      "dog",
      "water",
      "dish",
      "pet care"
    ],
    mostLikelyCategoryName: "Pets",
  ),
  IconForCategory(
    icon: "location-pin.png",
    tags: [
      "location-pin",
      "map",
      "pin",
      "location",
      "marker",
      "navigation",
      "geolocation",
      "address",
      "place",
      "direction"
    ],
    mostLikelyCategoryName: "Location",
  ),
  IconForCategory(
    icon: "sticky-notes.png",
    tags: [
      "sticky-notes",
      "notes",
      "memos",
      "reminders",
      "paper",
      "writing",
      "office",
      "organization",
      "stickers",
      "post-it"
    ],
    mostLikelyCategoryName: "Notes",
  ),
  IconForCategory(
    icon: "bill.png",
    tags: [
      "bill",
      "payment",
      "invoice",
      "expenses",
      "cost",
      "financial",
      "transaction",
      "due",
      "balance",
      "receipt"
    ],
    mostLikelyCategoryName: "Bills",
  ),
  IconForCategory(
    icon: "coin.png",
    tags: [
      "coin",
      "currency",
      "money",
      "payment",
      "finance",
      "cash",
      "coinage",
      "exchange",
      "wealth",
      "monetary",
      "dollar",
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "rainbow.png",
    tags: [
      "rainbow",
      "colorful",
      "colors",
      "spectrum",
      "prism",
      "light",
      "vibrant",
      "beauty",
      "weather",
      "nature"
    ],
    mostLikelyCategoryName: "Rainbow",
  ),
  IconForCategory(
    icon: "rain.png",
    tags: [
      "rain",
      "weather",
      "wet",
      "precipitation",
      "water",
      "storm",
      "umbrella",
      "clouds",
      "droplets",
      "downpour"
    ],
    mostLikelyCategoryName: "Rain",
  ),
  IconForCategory(
    icon: "cloudy.png",
    tags: [
      "cloudy",
      "clouds",
      "weather",
      "overcast",
      "sky",
      "grey",
      "forecast",
      "meteorology",
      "climate",
      "atmosphere"
    ],
    mostLikelyCategoryName: "Weather",
  ),
  IconForCategory(
    icon: "tickets.png",
    tags: [
      "tickets",
      "events",
      "concerts",
      "shows",
      "entertainment",
      "admission",
      "passes",
      "entry",
      "experience",
      "access"
    ],
    mostLikelyCategoryName: "Tickets",
  ),
  IconForCategory(
    icon: "celebration.png",
    tags: [
      "celebration",
      "party",
      "festivity",
      "event",
      "joy",
      "happiness",
      "cheer",
      "occasion",
      "festive",
      "merriment"
    ],
    mostLikelyCategoryName: "Celebration",
  ),
  IconForCategory(
    icon: "package.png",
    tags: [
      "package",
      "parcel",
      "shipping",
      "delivery",
      "mail",
      "box",
      "package tracking",
      "shipping service",
      "logistics",
      "shipment"
    ],
    mostLikelyCategoryName: "Shipping",
  ),
  IconForCategory(
    icon: "cabin.png",
    tags: [
      "cabin",
      "cottage",
      "woodland",
      "retreat",
      "nature",
      "rustic",
      "cozy",
      "mountains",
      "forest",
      "vacation",
      "housing",
      "lodging"
    ],
    mostLikelyCategoryName: "Cabin",
  ),
  IconForCategory(
    icon: "limousine.png",
    tags: [
      "limousine",
      "car",
      "luxury",
      "transportation",
      "chauffeur",
      "vehicle",
      "travel",
      "ride",
      "fancy",
      "prestige"
    ],
    mostLikelyCategoryName: "Limousine",
  ),
  IconForCategory(
    icon: "parents.png",
    tags: [
      "parents",
      "family",
      "children",
      "mom",
      "dad",
      "love",
      "parenting",
      "home",
      "mother",
      "father"
    ],
    mostLikelyCategoryName: "Parents",
  ),
  IconForCategory(
    icon: "dollar-coin.png",
    tags: [
      "dollar-coin",
      "money",
      "currency",
      "payment",
      "finance",
      "cash",
      "coinage",
      "exchange",
      "wealth",
      "monetary",
      "dollar",
    ],
    mostLikelyCategoryName: "Finance",
  ),
  IconForCategory(
    icon: "cash-bill-dollar.png",
    tags: [
      "cash-bill-dollar",
      "money",
      "currency",
      "payment",
      "finance",
      "cash",
      "bills",
      "wealth",
      "monetary",
      "currency-note"
          "dollar",
    ],
    mostLikelyCategoryName: "Cash",
  ),
  IconForCategory(
    icon: "internet-globe.png",
    tags: [
      "internet-globe",
      "internet",
      "web",
      "online",
      "network",
      "connection",
      "global",
      "communication",
      "technology",
      "world wide web"
    ],
    mostLikelyCategoryName: "Internet",
  ),
  IconForCategory(
    icon: "checker-chess-board.png",
    tags: [
      "checker-chess-board",
      "chess",
      "game",
      "board",
      "strategy",
      "competition",
      "checkers",
      "king",
      "pawn",
      "rook"
    ],
    mostLikelyCategoryName: "Chess",
  ),
  IconForCategory(
    icon: "portable-game-console.png",
    tags: [
      "portable-game-console",
      "gaming",
      "video games",
      "entertainment",
      "handheld",
      "console",
      "games",
      "controller",
      "gamer",
      "device"
    ],
    mostLikelyCategoryName: "Gaming",
  ),
  IconForCategory(
    icon: "bowling.png",
    tags: [
      "bowling",
      "sport",
      "game",
      "strike",
      "pins",
      "alley",
      "bowling ball",
      "recreation",
      "bowling pins",
      "spare"
    ],
    mostLikelyCategoryName: "Entertainment",
  ),
  IconForCategory(
    icon: "cap.png",
    tags: [
      "cap",
      "hat",
      "headwear",
      "fashion",
      "accessory",
      "style",
      "clothing",
      "headgear",
      "wearable",
      "head"
    ],
    mostLikelyCategoryName: "Cap",
  ),
  IconForCategory(
    icon: "winter-hat.png",
    tags: [
      "winter-hat",
      "hat",
      "cold",
      "winter",
      "headwear",
      "fashion",
      "accessory",
      "style",
      "clothing",
      "head",
      "christmas",
    ],
    mostLikelyCategoryName: "Winter",
  ),
  IconForCategory(
    icon: "top-hat.png",
    tags: [
      "top-hat",
      "hat",
      "formal",
      "headwear",
      "fashion",
      "accessory",
      "style",
      "clothing",
      "head",
      "elegant"
    ],
    mostLikelyCategoryName: "Formal",
  ),
  IconForCategory(
    icon: "charts.png",
    tags: [
      "charts",
      "data",
      "visualization",
      "graph",
      "analytics",
      "statistics",
      "diagram",
      "information",
      "presentation",
      "report"
    ],
    mostLikelyCategoryName: "Charts",
  ),
  IconForCategory(
    icon: "tea.png",
    tags: [
      "tea",
      "beverage",
      "drink",
      "cup",
      "brew",
      "infusion",
      "caffeine",
      "refreshment",
      "herbal",
      "hot"
    ],
    mostLikelyCategoryName: "Tea",
  ),
  IconForCategory(
    icon: "ball.png",
    tags: [
      "ball",
      "sports",
      "game",
      "play",
      "sporting",
      "round",
      "athletics",
      "equipment",
      "sphere",
      "recreation"
    ],
    mostLikelyCategoryName: "Sports",
  ),
  IconForCategory(
    icon: "apple.png",
    tags: [
      "apple",
      "fruit",
      "food",
      "healthy",
      "nutrition",
      "juicy",
      "orchard",
      "snack",
      "diet",
      "crisp"
    ],
    mostLikelyCategoryName: "Fruit",
  ),
  IconForCategory(
    icon: "keyboard.png",
    tags: [
      "keyboard",
      "computer",
      "typing",
      "input",
      "device",
      "technology",
      "keys",
      "typing",
      "electronics",
      "interface"
    ],
    mostLikelyCategoryName: "Technology",
  ),
  IconForCategory(
    icon: "fuel.png",
    tags: [
      "fuel",
      "energy",
      "gas",
      "petrol",
      "refueling",
      "car",
      "automobile",
      "gas station",
      "transportation",
      "combustible"
    ],
    mostLikelyCategoryName: "Fuel",
  ),
  IconForCategory(
    icon: "gasoline.png",
    tags: [
      "gasoline",
      "fuel",
      "petrol",
      "energy",
      "car",
      "automobile",
      "refueling",
      "gas station",
      "transportation",
      "combustible"
    ],
    mostLikelyCategoryName: "Gasoline",
  ),
  IconForCategory(
    icon: "organic-food.png",
    tags: [
      "organic-food",
      "vegetables",
      "crate",
      "produce",
      "healthy",
      "natural",
      "nutrition",
      "fresh",
      "farm",
      "sustainable"
    ],
    mostLikelyCategoryName: "Organic",
  ),
  IconForCategory(
    icon: "salad.png",
    tags: [
      "salad",
      "vegetables",
      "fruits",
      "healthy",
      "greens",
      "nutrition",
      "fresh",
      "tossed",
      "meal",
      "lettuce"
    ],
    mostLikelyCategoryName: "Salad",
  ),
  IconForCategory(
    icon: "healthy-food.png",
    tags: [
      "healthy-food",
      "nutrition",
      "fruits",
      "vegetables",
      "banana",
      "watermelon",
      "fresh",
      "nutritious",
      "diet",
      "wellness"
    ],
    mostLikelyCategoryName: "Healthy",
  ),
  IconForCategory(
    icon: "tablet.png",
    tags: [
      "tablet",
      "device",
      "technology",
      "electronics",
      "screen",
      "computer",
      "digital",
      "portable",
      "touchscreen",
      "gadget"
    ],
    mostLikelyCategoryName: "Tablet",
  ),
  IconForCategory(
    icon: "exchange-arrows-circle.png",
    tags: [
      "exchange-arrows-circle",
      "arrows",
      "circle",
      "rotate",
      "exchange",
      "circular",
      "refresh",
      "reload",
      "loop",
      "rotation"
    ],
    mostLikelyCategoryName: "Rotation",
  ),
  IconForCategory(
    icon: "exchange-arrows.png",
    tags: [
      "exchange-arrows",
      "arrows",
      "exchange",
      "swap",
      "switch",
      "transfer",
      "change",
      "direction",
      "trade",
      "transaction"
    ],
    mostLikelyCategoryName: "Exchange",
  ),
  IconForCategory(
    icon: "back-undo-arrow.png",
    tags: [
      "back-undo-arrow",
      "arrow",
      "undo",
      "back",
      "backward",
      "reverse",
      "return",
      "go back",
      "previous",
      "retreat"
    ],
    mostLikelyCategoryName: "Undo",
  ),
  IconForCategory(
    icon: "taxi(1).png",
    tags: [
      "taxi",
      "cab",
      "transportation",
      "car",
      "ride",
      "vehicle",
      "urban",
      "public",
      "transport",
      "passenger",
    ],
    mostLikelyCategoryName: "Taxi",
  ),
  IconForCategory(
    icon: "taxi(2).png",
    tags: [
      "taxi",
      "cab",
      "transportation",
      "car",
      "ride",
      "vehicle",
      "urban",
      "public",
      "transport",
      "passenger",
    ],
    mostLikelyCategoryName: "Taxi",
  ),
  IconForCategory(
    icon: "reload.png",
    tags: [
      "reload",
      "refresh",
      "circular",
      "rotation",
      "sync",
      "renew",
      "update",
      "cycle",
      "reload-icon",
      "reloading"
    ],
    mostLikelyCategoryName: "Reload",
  ),
  IconForCategory(
    icon: "sandwich.png",
    tags: [
      "sandwich",
      "food",
      "lunch",
      "meal",
      "bread",
      "ingredients",
      "snack",
      "deli",
      "sub",
      "submarine"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "subscription.png",
    tags: [
      "subscription",
      "subscribe",
      "membership",
      "renewal",
      "billing",
      "service",
      "recurring",
      "monthly",
      "payment",
      "plan"
    ],
    mostLikelyCategoryName: "Subscription",
  ),
  IconForCategory(
    icon: "microphone.png",
    tags: [
      "microphone",
      "audio",
      "sound",
      "recording",
      "voice",
      "music",
      "mic",
      "studio",
      "speech",
      "podcast"
    ],
    mostLikelyCategoryName: "Microphone",
  ),
  IconForCategory(
    icon: "church.png",
    tags: [
      "church",
      "place of worship",
      "religion",
      "spirituality",
      "faith",
      "sacred",
      "holy",
      "cathedral",
      "prayer",
      "sanctuary"
    ],
    mostLikelyCategoryName: "Church",
  ),
  IconForCategory(
    icon: "motor-bike(1).png",
    tags: [
      "motor-bike",
      "motorcycle",
      "bike",
      "vehicle",
      "transportation",
      "ride",
      "motor",
      "two-wheeler",
      "cycle",
      "speed"
    ],
    mostLikelyCategoryName: "Motorbike",
  ),
  IconForCategory(
    icon: "motor-bike(2).png",
    tags: [
      "motor-bike",
      "motorcycle",
      "bike",
      "vehicle",
      "transportation",
      "ride",
      "motor",
      "two-wheeler",
      "cycle",
      "speed"
    ],
    mostLikelyCategoryName: "Motorbike",
  ),
  IconForCategory(
    icon: "school-bus.png",
    tags: [
      "school-bus",
      "bus",
      "education",
      "transportation",
      "students",
      "school",
      "learning",
      "childhood",
      "ride",
      "vehicle"
    ],
    mostLikelyCategoryName: "School",
  ),
  IconForCategory(
    icon: "delivery-truck.png",
    tags: [
      "delivery-truck",
      "truck",
      "delivery",
      "transportation",
      "cargo",
      "logistics",
      "shipping",
      "freight",
      "distribution",
      "vehicle"
    ],
    mostLikelyCategoryName: "Delivery",
  ),
  IconForCategory(
    icon: "orange-juice.png",
    tags: [
      "orange-juice",
      "juice",
      "beverage",
      "drinks",
      "refreshment",
      "citrus",
      "fresh",
      "vitamin C",
      "healthy",
      "breakfast",
      "beer",
      "drink",
      "alcohol",
      "beverage",
      "bar",
      "pub",
      "cocktail",
      "bar",
    ],
    mostLikelyCategoryName: "Drink",
  ),
  IconForCategory(
    icon: "bottles.png",
    tags: [
      "bottles",
      "containers",
      "packaging",
      "glass",
      "plastic",
      "recycle",
      "liquid",
      "storage",
      "beverage",
      "vessels",
      "drinks",
      "refreshment",
      "beer",
      "drink",
      "alcohol",
      "beverage",
      "bar",
      "pub",
      "cocktail",
      "bar",
    ],
    mostLikelyCategoryName: "Drink",
  ),
  IconForCategory(
    icon: "sauces.png",
    tags: [
      "sauces",
      "condiments",
      "sauce",
      "flavoring",
      "cooking",
      "culinary",
      "food",
      "seasoning",
      "dressings",
      "flavor",
      "drinks",
      "bottles",
      "drinks",
      "refreshment",
      "beverage",
      "beer",
      "drink",
      "alcohol",
      "bar",
      "pub",
      "cocktail",
      "bar",
    ],
    mostLikelyCategoryName: "Sauces",
  ),
  IconForCategory(
    icon: "grill.png",
    tags: [
      "grill",
      "barbecue",
      "cooking",
      "outdoor",
      "bbq",
      "food",
      "grilling",
      "flame",
      "meat",
      "charcoal"
    ],
    mostLikelyCategoryName: "Grill",
  ),
  IconForCategory(
    icon: "theatre.png",
    tags: [
      "theatre",
      "theater",
      "performance",
      "arts",
      "drama",
      "entertainment",
      "stage",
      "play",
      "show",
      "acting",
    ],
    mostLikelyCategoryName: "Theatre",
  ),
  IconForCategory(
    icon: "glass.png",
    tags: [
      "glass",
      "beer",
      "drink",
      "alcohol",
      "beverage",
      "bar",
      "pub",
      "refreshment",
      "brew",
      "pint",
    ],
    mostLikelyCategoryName: "Drink",
  ),
  IconForCategory(
    icon: "fizzy-drink.png",
    tags: [
      "fizzy-drink",
      "cocktail",
      "drink",
      "beverage",
      "refreshment",
      "soda",
      "fizz",
      "carbonated",
      "mixed-drink",
      "bartender",
      "beer",
      "drink",
      "alcohol",
      "beverage",
      "bar",
      "pub",
      "bar",
    ],
    mostLikelyCategoryName: "Drink",
  ),
  IconForCategory(
    icon: "sushi.png",
    tags: [
      "sushi",
      "food",
      "japanese",
      "cuisine",
      "roll",
      "sashimi",
      "rice",
      "seaweed",
      "fresh",
      "healthy",
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "taco.png",
    tags: [
      "taco",
      "food",
      "mexican",
      "cuisine",
      "tortilla",
      "filling",
      "spicy",
      "tasty",
      "meat",
      "vegetables",
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "wifi.png",
    tags: [
      "wifi",
      "internet",
      "network",
      "wireless",
      "connection",
      "technology",
      "signal",
      "router",
      "online",
      "access"
    ],
    mostLikelyCategoryName: "WiFi",
  ),
  IconForCategory(
    icon: "fried-egg.png",
    tags: [
      "fried-egg",
      "egg",
      "food",
      "breakfast",
      "yolk",
      "cooking",
      "fried",
      "meal",
      "protein",
      "brunch"
    ],
    mostLikelyCategoryName: "Eggs",
  ),
  IconForCategory(
    icon: "car-charging-station.png",
    tags: [
      "car-charging-station",
      "charging",
      "electric",
      "vehicle",
      "energy",
      "power",
      "station",
      "charger",
      "electricity",
      "recharge"
    ],
    mostLikelyCategoryName: "Charging",
  ),
  IconForCategory(
    icon: "battery-charge.png",
    tags: [
      "battery-charge",
      "battery",
      "charge",
      "power",
      "energy",
      "electricity",
      "rechargeable",
      "cell",
      "portable",
      "juice"
    ],
    mostLikelyCategoryName: "Battery",
  ),
  IconForCategory(
    icon: "eggs.png",
    tags: [
      "eggs",
      "food",
      "breakfast",
      "protein",
      "cooking",
      "meal",
      "fresh",
      "chicken",
      "omelette",
      "scrambled-eggs"
    ],
    mostLikelyCategoryName: "Eggs",
  ),
  IconForCategory(
    icon: "shrimp.png",
    tags: [
      "shrimp",
      "seafood",
      "food",
      "prawn",
      "crustacean",
      "cooking",
      "shellfish",
      "delicacy",
      "ocean",
      "crab"
    ],
    mostLikelyCategoryName: "Shrimp",
  ),
  IconForCategory(
    icon: "raw-meat.png",
    tags: [
      "raw-meat",
      "meat",
      "food",
      "butcher",
      "fresh",
      "beef",
      "poultry",
      "pork",
      "chicken",
      "steak"
    ],
    mostLikelyCategoryName: "Meat",
  ),
  IconForCategory(
    icon: "bread.png",
    tags: [
      "bread",
      "food",
      "baking",
      "loaf",
      "wheat",
      "yeast",
      "sandwich",
      "toast",
      "bakery",
      "fresh"
    ],
    mostLikelyCategoryName: "Bakery",
  ),
  IconForCategory(
    icon: "3d-printer.png",
    tags: [
      "3d",
      "printer",
      "printing",
      "technology",
      "manufacturing",
      "prototype",
      "innovation",
      "creative",
      "design",
      "plastic"
    ],
    mostLikelyCategoryName: "Printer",
  ),
  IconForCategory(
    icon: "milk.png",
    tags: [
      "milk",
      "dairy",
      "drink",
      "calcium",
      "white",
      "beverage",
      "cow",
      "nutrients",
      "healthy",
      "nutrition"
    ],
    mostLikelyCategoryName: "Dairy",
  ),
  IconForCategory(
    icon: "magic-box.png",
    tags: [
      "magic-box",
      "miscellaneous",
      "random",
      "items",
      "mystery",
      "surprise",
      "unknown",
      "assortment",
      "diverse",
      "variety"
    ],
    mostLikelyCategoryName: "Miscellaneous",
  ),
  IconForCategory(
    icon: "decrease.png",
    tags: [
      "decrease",
      "decreasing",
      "arrows",
      "downward",
      "reduce",
      "lower",
      "lessen",
      "diminish",
      "shrink",
      "decline",
      "outcome",
      "withdraw",
      "expense"
    ],
    mostLikelyCategoryName: "Decrease",
  ),
  IconForCategory(
    icon: "increase.png",
    tags: [
      "increase",
      "increasing",
      "arrows",
      "upward",
      "grow",
      "rise",
      "expand",
      "enlarge",
      "multiply",
      "improve",
      "income"
    ],
    mostLikelyCategoryName: "Increase",
  ),
  IconForCategory(
    icon: "house(1).png",
    tags: [
      "house",
      "home",
      "residence",
      "dwelling",
      "building",
      "property",
      "shelter",
      "habitat",
      "accommodation",
      "housing"
    ],
    mostLikelyCategoryName: "Housing",
  ),
  IconForCategory(
    icon: "water-bottle.png",
    tags: [
      "water-bottle",
      "water",
      "bottle",
      "hydration",
      "drink",
      "beverage",
      "refreshment",
      "fluid",
      "container",
      "healthy"
    ],
    mostLikelyCategoryName: "Water",
  ),
  IconForCategory(
    icon: "glass-of-water.png",
    tags: [
      "glass-of-water",
      "water",
      "glass",
      "hydration",
      "drink",
      "beverage",
      "refreshment",
      "fluid",
      "clear",
      "healthy"
    ],
    mostLikelyCategoryName: "Water",
  ),
  IconForCategory(
    icon: "cookies.png",
    tags: [
      "cookies",
      "food",
      "dessert",
      "sweet",
      "baked",
      "chocolate-chip",
      "snack",
      "treat",
      "biscuits",
      "delicious"
    ],
    mostLikelyCategoryName: "Sweets",
  ),
  IconForCategory(
    icon: "paper-bill.png",
    tags: [
      "paper-bill",
      "bill",
      "invoice",
      "payment",
      "finance",
      "expense",
      "receipt",
      "transaction",
      "money",
      "document"
    ],
    mostLikelyCategoryName: "Bill",
  ),
  IconForCategory(
    icon: "paper-ticket.png",
    tags: [
      "paper-ticket",
      "ticket",
      "admission",
      "entry",
      "pass",
      "event",
      "concert",
      "movie",
      "travel",
      "show",
      "bill",
      "invoice",
      "payment",
      "finance",
      "expense",
      "receipt",
    ],
    mostLikelyCategoryName: "Ticket",
  ),
  IconForCategory(
    icon: "gas-valve.png",
    tags: [
      "gas-valve",
      "gas",
      "valve",
      "utility",
      "energy",
      "pipe",
      "fuel",
      "natural-gas",
      "propane",
      "home"
    ],
    mostLikelyCategoryName: "Gas",
  ),
  IconForCategory(
    icon: "water-tap.png",
    tags: [
      "water-tap",
      "water",
      "tap",
      "faucet",
      "plumbing",
      "liquid",
      "flow",
      "drink",
      "bathroom",
      "kitchen"
    ],
    mostLikelyCategoryName: "Water",
  ),
  IconForCategory(
    icon: "atm-machine(2).png",
    tags: [
      "atm-machine(2)",
      "atm",
      "cash",
      "money",
      "banking",
      "withdrawal",
      "transaction",
      "finance",
      "automated",
      "machine"
    ],
    mostLikelyCategoryName: "Bank",
  ),
  IconForCategory(
    icon: "atm-machine(1).png",
    tags: [
      "atm-machine(1)",
      "atm",
      "cash",
      "money",
      "banking",
      "withdrawal",
      "transaction",
      "finance",
      "automated",
      "machine"
    ],
    mostLikelyCategoryName: "Bank",
  ),
  IconForCategory(
    icon: "sim-card.png",
    tags: [
      "sim-card",
      "sim",
      "card",
      "mobile",
      "communication",
      "phone",
      "cellular",
      "telecom",
      "network",
      "technology",
      "plan"
    ],
    mostLikelyCategoryName: "Phone",
  ),
  IconForCategory(
    icon: "christmas-tree.png",
    tags: [
      "christmas-tree",
      "christmas",
      "tree",
      "holiday",
      "festive",
      "celebration",
      "decor",
      "ornament",
      "seasonal",
      "winter"
    ],
    mostLikelyCategoryName: "Holidays",
  ),
  IconForCategory(
    icon: "confetti(2).png",
    tags: [
      "confetti(2)",
      "confetti",
      "celebration",
      "party",
      "event",
      "festive",
      "colorful",
      "paper",
      "fun",
      "scattered"
    ],
    mostLikelyCategoryName: "Parties",
  ),
  IconForCategory(
    icon: "safe-box.png",
    tags: [
      "safe-box",
      "safe",
      "security",
      "vault",
      "protection",
      "lock",
      "secure",
      "strongbox",
      "money",
      "safety"
    ],
    mostLikelyCategoryName: "Safe",
  ),
  IconForCategory(
    icon: "security-box.png",
    tags: [
      "security-box",
      "security",
      "box",
      "safe",
      "vault",
      "protection",
      "lock",
      "secure",
      "strongbox",
      "safety"
    ],
    mostLikelyCategoryName: "Safe",
  ),
  IconForCategory(
    icon: "curry.png",
    tags: [
      "curry",
      "food",
      "spicy",
      "cuisine",
      "dish",
      "flavorful",
      "meal",
      "cooking",
      "delicious",
      "stew"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "spaghetti.png",
    tags: [
      "spaghetti",
      "pasta",
      "italian",
      "food",
      "noodles",
      "dish",
      "cooking",
      "delicious",
      "meal",
      "culinary"
    ],
    mostLikelyCategoryName: "Food",
  ),
  IconForCategory(
    icon: "multivitamin.png",
    tags: [
      "multivitamin",
      "vitamin",
      "health",
      "supplement",
      "nutrition",
      "pill",
      "wellness",
      "diet",
      "medicine",
      "healthy"
    ],
    mostLikelyCategoryName: "Health",
  ),
  IconForCategory(
    icon: "gift-card.png",
    tags: [
      "gift-card",
      "present",
      "shopping",
      "voucher",
      "discount",
      "shopping",
      "retail",
      "holiday",
      "occasion"
    ],
    mostLikelyCategoryName: "Gift",
  ),
  IconForCategory(
    icon: "loan.png",
    tags: [
      "giving",
      "receiving",
      "loan",
      "finance",
      "money",
      "borrow",
      "lend",
      "debt",
      "credit",
      "bank",
      "investment",
      "payment",
    ],
    mostLikelyCategoryName: "Loan",
  ),
];
