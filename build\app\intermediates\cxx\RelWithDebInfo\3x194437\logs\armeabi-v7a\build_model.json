{"info": {"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, "cxxBuildFolder": "D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a", "soFolder": "D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\3x194437\\obj\\armeabi-v7a", "soRepublishFolder": "D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates\\cmake\\release\\obj\\armeabi-v7a", "abiPlatformVersion": 26, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-Wno-dev", "--no-warn-unused-cli"], "cFlagsList": [], "cppFlagsList": [], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx", "intermediatesBaseFolder": "D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates", "intermediatesFolder": "D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "D:\\App\\Android Studio\\Projects\\budget\\android\\app", "moduleBuildFile": "D:\\App\\Android Studio\\Projects\\budget\\android\\app\\build.gradle.kts", "makeFile": "D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973", "ndkFolderBeforeSymLinking": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973", "ndkVersion": "27.0.12077973", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\App\\Android Studio\\Projects\\budget\\android", "sdkFolder": "D:\\App\\Android Studio\\AndoirdSDK", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\prefab\\armeabi-v7a", "isActiveAbi": true, "fullConfigurationHash": "3x194437i523j1x26t396a6a533a3u6s3c6a5ht6266411u5o5b5z6b6g1sh", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.7.0.\n#   - $NDK is the path to NDK 27.0.12077973.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HD:/App/Android Studio/FlutterSDK/packages/flutter_tools/gradle/src/main/scripts\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=26\n-DANDROID_PLATFORM=android-26\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-<PERSON><PERSON><PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:/App/Android Studio/Projects/budget/build/app/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:/App/Android Studio/Projects/budget/build/app/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-BD:/App/Android Studio/Projects/budget/build/.cxx/RelWithDebInfo/$HASH/$ABI\n-GNinja\n-Wno-dev\n--no-warn-unused-cli", "configurationArguments": ["-HD:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=26", "-DANDROID_PLATFORM=android-26", "-DANDROID_ABI=armeabi-v7a", "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a", "-DANDROID_NDK=D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973", "-DCMAKE_ANDROID_NDK=D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973", "-DCMAKE_TOOLCHAIN_FILE=D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\3x194437\\obj\\armeabi-v7a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\3x194437\\obj\\armeabi-v7a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-BD:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\RelWithDebInfo\\3x194437\\armeabi-v7a", "-<PERSON><PERSON><PERSON><PERSON>", "-Wno-dev", "--no-warn-unused-cli"], "intermediatesParentFolder": "D:\\App\\Android Studio\\Projects\\budget\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\3x194437"}