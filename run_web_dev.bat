@echo off
echo ========================================
echo   Budget App - Web Development Server
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter and add it to your PATH
    pause
    exit /b 1
)

echo Flutter version:
flutter --version
echo.

REM Get dependencies (quick check)
echo Getting dependencies...
flutter pub get
echo.

echo.
echo ========================================
echo Starting development server on localhost:8080
echo ========================================
echo.
echo Development mode features:
echo - Hot reload enabled (press 'r' to reload)
echo - Hot restart enabled (press 'R' to restart)
echo - Debug mode with detailed error messages
echo - No build step (faster startup)
echo.
echo The app will open in your default browser
echo Press 'q' to quit or Ctrl+C to stop
echo.
echo NOTE: This is development mode with Flutter's dev server
echo For production testing, use run_web_server.bat instead
echo.
echo If Brave browser opens and closes immediately, try:
echo 1. Disable Brave Shields for localhost
echo 2. Use Chrome instead, or
echo 3. Use the production server (run_web_server.bat)
echo.

REM Run in development mode with web-browser instead of chrome specifically
flutter run -d web-server --web-port=8080 --web-hostname=localhost

echo.
echo Development server stopped.
pause
