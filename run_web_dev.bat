@echo off
echo ========================================
echo   Budget App - Web Development Server
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter and add it to your PATH
    pause
    exit /b 1
)

echo Flutter version:
flutter --version
echo.

REM Get dependencies (quick check)
echo Getting dependencies...
flutter pub get
echo.

echo.
echo ========================================
echo Starting development server on localhost:8080
echo ========================================
echo.
echo Development mode features:
echo - Hot reload enabled (press 'r' to reload)
echo - Hot restart enabled (press 'R' to restart)
echo - Debug mode with detailed error messages
echo - No build step (faster startup)
echo.
echo The app will open in Chrome browser
echo Press 'q' to quit or Ctrl+C to stop
echo.
echo NOTE: This is development mode with Flutter's dev server
echo For production testing, use run_web_server.bat instead
echo.

REM Run in development mode (faster startup, no build step)
flutter run -d chrome --web-port=8080 --web-hostname=localhost --debug

echo.
echo Development server stopped.
pause
