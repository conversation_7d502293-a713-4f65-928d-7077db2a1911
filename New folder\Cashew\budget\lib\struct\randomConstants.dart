import 'dart:math';

Random random = new Random();
List<int> randomInt = [
  random.nextInt(100),
  random.nextInt(100),
  random.nextInt(100),
  random.nextInt(100),
  random.nextInt(100),
  random.nextInt(100),
  random.nextInt(100),
  random.nextInt(100),
  random.nextInt(100),
  random.nextInt(100)
];
List<double> randomDouble = [
  random.nextDouble(),
  random.nextDouble(),
  random.nextDouble(),
  random.nextDouble(),
  random.nextDouble(),
  random.nextDouble(),
  random.nextDouble(),
  random.nextDouble(),
  random.nextDouble(),
  random.nextDouble()
];
