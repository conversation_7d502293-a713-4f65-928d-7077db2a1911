{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"autostuff_enabled": false, "last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "brave": {"accelerators": {"33000": ["Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5"], "33003": ["Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["Control+KeyT"], "34015": ["Control+KeyW", "Control+F4"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "AltGr", "Alt"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "brave_ads": {"notification_ads": [], "should_allow_ads_subdivision_targeting": false, "state": {"has_migrated": {"client": {"v7": true}, "confirmations": {"v8": true}, "v2": true}}}, "brave_search": {"last-used-ntp-search-engine": "search.brave.com"}, "default_accelerators": {"33000": ["Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5"], "33003": ["Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["Control+KeyT"], "34015": ["Control+KeyW", "Control+F4"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "Alt", "Alt", "AltGr"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "default_private_search_provider_data": {"alternate_urls": [], "contextual_search_url": "", "created_from_play_api": false, "date_created": "0", "doodle_url": "", "enforced_by_policy": false, "favicon_url": "https://cdn.search.brave.com/serp/favicon.ico", "featured_by_policy": false, "id": "0", "image_search_branding_label": "", "image_translate_source_language_param_key": "", "image_translate_target_language_param_key": "", "image_translate_url": "", "image_url": "", "image_url_post_params": "", "input_encodings": ["UTF-8"], "is_active": 0, "keyword": ":br", "last_modified": "0", "last_visited": "0", "logo_url": "", "new_tab_url": "", "originating_url": "", "policy_origin": 0, "preconnect_to_search_url": false, "prefetch_likely_navigations": false, "prepopulate_id": 550, "safe_for_autoreplace": true, "search_intent_params": [], "search_url_post_params": "", "short_name": "Brave", "starter_pack_id": 0, "suggestions_url": "https://search.brave.com/api/suggest?q={searchTerms}&rich=true&source=desktop", "suggestions_url_post_params": "", "synced_guid": "485bf7d3-0215-45af-87dc-************", "url": "https://search.brave.com/search?q={searchTerms}&source=desktop", "usage_count": 0}, "default_private_search_provider_guid": "485bf7d3-0215-45af-87dc-************", "enable_media_router_on_restart": true, "migrated_search_default_in_jp": true, "rewards": {"notifications": "{\"displayed\":[],\"notifications\":[]}", "scheduled_captcha": {"failed_attempts": 0, "id": "", "paused": false, "payment_id": ""}}, "search": {"default_version": 6}, "shields_settings_version": 4, "today": {"p3a_total_card_views": [{"day": **********.0, "value": 0.0}], "p3a_total_card_visits": [{"day": **********.0, "value": 0.0}], "p3a_total_sidebar_filter_usages": [{"day": **********.0, "value": 0.0}]}, "wallet": {"aurora_mainnet_migrated": true, "custom_networks": {"goerli_migrated": true}, "eip1559_chains_migrated": true, "is_compressed_nft_migrated": true, "is_spl_token_program_migrated": true, "keyrings": {}, "last_transaction_sent_time_dict": {}}, "webtorrent_enabled": true, "weekly_storage": {"Brave": {"P2A": {"new_tab_page_ad": {"opportunities": [{"day": **********.0, "value": 2.0}]}}}}}, "brave_shields": {"p3a_ads_allow_domain_count": 0, "p3a_ads_standard_domain_count": 0, "p3a_ads_strict_domain_count": 0, "p3a_first_reported_revision": 3, "p3a_fp_allow_domain_count": 0, "p3a_fp_standard_domain_count": 0, "p3a_fp_strict_domain_count": 0}, "brave_sync_v2": {"reset_devices_progress_token_time": "13394202373336255"}, "browser": {"available_dark_theme_options": "All", "edge_sidebar_visibility": {"_gaming_assist_": {"order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 2147483647}}, "add_app_to_bottom": true, "order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 2147483647}}, "edge_sidebar_visibility_debug": {"order_list": ["Search"], "order_raw_data": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "Search", "pos": "2147483647"}}}, "enable_text_prediction_v2": true, "has_seen_welcome_page": false, "hub_app_non_synced_debug_area": {}, "hub_app_non_synced_preferences": {"apps": {"06be1ebe-f23a-4bea-ae45-3120ad86cfea": {"last_path": ""}, "0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "25fe2d1d-e934-482a-a62f-ea1705db905d": {"last_path": ""}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"last_path": ""}, "3458dfd2-bf1b-4d00-a6dd-a74a59d523c7": {"last_path": ""}, "35a43603-bb38-4b53-ba20-932cb9117794": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": {"last_path": ""}, "a1a78183-6db3-4789-9e7c-84d157846d55": {"last_path": ""}, "bacc3c12-ebff-44b4-a0f8-ce8b69c9e047": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {}, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "skipped_profile_less_than_28_days"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}], "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 0, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 0, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 0, "hub_app_cleanup_v2_done": true}, "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_hub_app_in_sidebar_buttons": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}, "show_hub_app_in_sidebar_buttons_legacy": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}, "show_hub_app_in_sidebar_buttons_legacy_update_time": "13394201950157351", "time_of_last_normal_window_close": "13394202316217830", "toolbar_extensions_hub_button_visibility": 0, "underside_chat_bing_signed_in_status": false, "window_placement": {"bottom": 1022, "left": 10, "maximized": true, "right": 955, "top": 10, "work_area_bottom": 1032, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "browser_content_container_height": 960, "browser_content_container_width": 1920, "browser_content_container_x": 0, "browser_content_container_y": 72, "commerce_daily_metrics_last_update_time": "13394202373218987", "countryid_at_install": 18766, "custom_links": {"list": []}, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "13394201827538038"}, "dual_engine": {"consumer_mode": {"ie_user": false}, "consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "", "consumer_sitelist_version": "", "external_consumer_shared_cookie_data": {}, "shared_cookie_data": {}, "sitelist_has_consumer_data": false, "sitelist_has_enterprise_data": false, "sitelist_location": "", "sitelist_source": 0, "sitelist_version": ""}, "edge": {"msa_sso_info": {"allow_for_non_msa_profile": true}, "perf_center": {"performance_detector": true}, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 1}, "profile_sso_option": 1, "services": {"signin_scoped_device_id": "a59483d0-0e31-4418-99ec-eefb128b085f"}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":0,\"workspacesForExternalLinks\":[]}"}}, "edge_rewards": {"cache_data": "CAA=", "coachmark_promotions": {}, "hva_promotions": [], "refresh_status_muted_until": "13394806627321882"}, "edge_ux_config": {"assignmentcontext": "ZvRsKcXXoY739pqXVXDJ90zWE1haxnPMH0ZgE5k55NM=", "dataversion": "254470474", "experimentvariables": {"2f717976": {"edgeServerUX.sync.historyDataTypeEnabled": true}, "shop-60c": {"edgeServerUX.shopping.aablockth": 60, "edgeServerUX.shopping.block99": false}, "shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}, "shoprevenuattributionc": {"edgeServerUX.shopping.disableCashbackOnCouponCopy": false}}, "flights": {"2f717976": "31213786", "shop-60c": "31271455", "shopppdismisstreatment": "31004791", "shoprevenuattributionc": "31235886"}, "latestcorrelationid": "Ref A: CAC3C338378341F1A42EAB8521299D53 Ref B: DEL01EDGE0521 Ref C: 2025-06-12T11:45:05Z"}, "edge_wallet": {"trigger_funnel": {"records": []}}, "enterprise_profile_guid": "4da880d1-403b-4ebb-a462-9be4176df99b", "ephemeral_storage": {"first_party_storage_origins_to_cleanup": []}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "extension_hub_migration": true, "last_chrome_version": "137.1.79.123", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": []}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 129}, "gcm": {"product_category_for_subtypes": "com.brave.windows"}, "google": {"services": {"signin_scoped_device_id": "967888a5-638a-4070-b12b-3053335540b2"}}, "import_items_failure_state": {"reimport": {"ie_react": 62432}}, "in_product_help": {"new_badge": {"ComposeNudge": {"feature_enabled_time": "13394202373228140", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "13394202373228153", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13394202373227658", "recent_session_start_times": ["13394202373227658"], "session_last_active_time": "13394208664496334", "session_start_time": "13394202373227658"}, "intl": {"accept_languages": "en-GB,en,en-US", "selected_languages": "en-GB,en,en-US"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "local_browser_data_share": {"index_last_cleaned_time": "13394201887555980"}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"enable_media_router": true, "receiver_id_hash_token": "X0hHZgOKA3rrFqjVukdXjmAZb8FL2z7XPFAmYBwK0kgviOyCXwgaZdfBynAPibY0xtqTD9Q35ZkV/ZZqOF+vvw=="}, "muid": {"last_sync": "*****************", "values_seen": []}, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true, "m1": {"ad_measurement_enabled": false, "fledge_enabled": false, "topics_enabled": false}}, "profile": {"avatar_index": 20, "content_settings": {"did_migrate_adaptive_notification_quieting_to_cpss": true, "disable_quiet_permission_ui_time": {"notifications": "*****************"}, "enable_cpss": {"notifications": true}, "enable_quiet_permission_ui": {"notifications": false}, "exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "accessibility_events": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "braveShields": {}, "braveShieldsMetadata": {"http://localhost,*": {"last_modified": "13394202373337354", "setting": {"farbling_token": "8547410F0D94794A992BAD019DDDC6C4"}}}, "braveSpeedreader": {}, "brave_ethereum": {}, "brave_google_sign_in": {}, "brave_localhost_access": {}, "brave_open_ai_chat": {}, "brave_remember_1p_storage": {}, "brave_solana": {}, "brave_webcompat_audio": {}, "brave_webcompat_canvas": {}, "brave_webcompat_device_memory": {}, "brave_webcompat_event_source_pool": {}, "brave_webcompat_font": {}, "brave_webcompat_hardware_concurrency": {}, "brave_webcompat_keyboard": {}, "brave_webcompat_language": {}, "brave_webcompat_media_devices": {}, "brave_webcompat_none": {}, "brave_webcompat_plugins": {}, "brave_webcompat_screen": {}, "brave_webcompat_speech_synthesis": {}, "brave_webcompat_usb_device_serial_number": {}, "brave_webcompat_user_agent": {}, "brave_webcompat_web_sockets_pool": {}, "brave_webcompat_webgl": {}, "brave_webcompat_webgl2": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "13394202304675024", "setting": {}}}, "cookies": {"www.autodesk.com,www.autodesk.com": {"last_modified": "13394202306625732", "setting": 1}}, "cosmeticFiltering": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "fingerprintingV2": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "httpUpgradableResources": {}, "http_allowed": {}, "httpsUpgrades": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:49426,*": {"expiration": "13401978196341343", "last_modified": "13394202196341348", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:49936,*": {"expiration": "13401984755100899", "last_modified": "13394208755100902", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53207,*": {"expiration": "13401978316216468", "last_modified": "13394202316216472", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53324,*": {"expiration": "13401978467092317", "last_modified": "13394202467092322", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:56093,*": {"expiration": "13401978016113471", "last_modified": "13394202016113476", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "referrers": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "shieldsAds": {}, "shieldsCookiesV3": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13394208667096652", "setting": {"lastEngagementTime": 1.3394208667096646e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:49426,*": {"last_modified": "13394202173953317", "setting": {"lastEngagementTime": 1.3394202173953312e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:49936,*": {"last_modified": "13394208665060987", "setting": {"lastEngagementTime": 1.339420866506098e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:53207,*": {"last_modified": "13394202304676075", "setting": {"lastEngagementTime": 1.339420230467607e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:53324,*": {"last_modified": "13394202373679750", "setting": {"lastEngagementTime": 1.3394202373679742e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:56093,*": {"last_modified": "13394201827848200", "setting": {"lastEngagementTime": 1.3394201827848192e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "token_binding": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "129.0.2792.89", "creation_time": "13394201827292330", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "78460998-e470-438f-8062-f169c88d800a", "exit_type": "Normal", "family_member_role": "not_in_family", "has_seen_signin_fre": false, "is_relative_to_aad": false, "last_engagement_time": "13394208667096646", "last_time_obsolete_http_credentials_removed": 1749728287.332513, "last_time_password_store_metrics_reported": 1749728257.33215, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Profile 1", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_129.0.2792.89": 47.0}, "password_hash_data_list": [], "signin_fre_seen_time": "13394201827316023", "were_old_google_logins_removed": true}, "reset_prepopulated_engines": false, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13394202373", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "sessions": {"event_log": [{"crashed": false, "time": "13394201827328305", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394202016112134", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394202173540032", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394202196340241", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394202304220002", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394202316215228", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394202373210866", "type": 0}, {"restore_browser": true, "synchronous": true, "time": "13394202373217753", "type": 5}, {"errored_reading": false, "tab_count": 0, "time": "13394202373312460", "type": 1, "window_count": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394202467073096", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394208664477535", "type": 0}, {"restore_browser": true, "synchronous": true, "time": "13394208664484513", "type": 5}, {"errored_reading": false, "tab_count": 1, "time": "13394208664602964", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394208767031381", "type": 2, "window_count": 1}], "session_data_status": 3}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "13394288227387992", "pcb_supported": true}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-GB"], "dictionary": ""}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "edge_account_type": 0, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "feature_status_for_sync_to_signin": 5, "history_edge_supported": true, "history_type_disabled": false, "keep_everything_synced": false, "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "requested": false, "tabs": false, "tabs_edge_supported": true, "typed_urls": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "tab_groups": [], "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}, "video_enhancement": {"mode": "Non-AI enhancement"}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137", "link_handling_info": {"enabled_for_installed_apps": true}}}