import 'dart:async';
import 'dart:convert';

import 'package:budget/colors.dart';
import 'package:budget/database/binary_string_conversion.dart';
import 'package:budget/database/generatePreviewData.dart';
import 'package:budget/database/tables.dart';
import 'package:budget/firebase_options.dart';
import 'package:budget/functions.dart';
import 'package:budget/main.dart';
import 'package:budget/pages/aboutPage.dart';
import 'package:budget/pages/accountsPage.dart';
import 'package:budget/struct/connectivityManager.dart';
import 'package:budget/struct/databaseGlobal.dart';
import 'package:budget/struct/firebaseAuthGlobal.dart';
import 'package:budget/struct/platformDetection.dart';
import 'package:budget/struct/settings.dart';
import 'package:budget/struct/shareBudget.dart';

import 'package:budget/struct/syncClient.dart';

import 'package:budget/widgets/animatedExpanded.dart';
import 'package:budget/widgets/button.dart';
import 'package:budget/widgets/globalSnackbar.dart';
import 'package:budget/widgets/importDB.dart';
import 'package:budget/widgets/moreIcons.dart';
import 'package:budget/widgets/navigationFramework.dart';
import 'package:budget/widgets/navigationSidebar.dart';
import 'package:budget/widgets/openBottomSheet.dart';
import 'package:budget/widgets/openPopup.dart';
import 'package:budget/widgets/openSnackbar.dart';
import 'package:budget/widgets/framework/popupFramework.dart';
import 'package:budget/widgets/settingsContainers.dart';
import 'package:budget/widgets/tappable.dart';
import 'package:budget/widgets/textWidgets.dart';
import 'package:budget/widgets/util/saveFile.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:googleapis/abusiveexperiencereport/v1.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis/gmail/v1.dart' as gMail;
import 'package:google_sign_in/google_sign_in.dart' as signIn;
import 'package:http/http.dart' as http;
import 'package:shimmer/shimmer.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:io';
import 'package:budget/struct/randomConstants.dart';

// This function is replaced by ConnectivityManager
// Kept for backward compatibility
Future<bool> checkConnection() async {
  return await ConnectivityManager().checkConnection();
}

class GoogleAuthClient extends http.BaseClient {
  final Map<String, String> _headers;
  final http.Client _client = new http.Client();
  GoogleAuthClient(this._headers);
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    return _client.send(request..headers.addAll(_headers));
  }
}

signIn.GoogleSignIn? googleSignIn;
signIn.GoogleSignInAccount? googleUser;

// Track access revocation state
bool _accessRevoked = false;
// Track if we're in the middle of a sign-in attempt
bool _isSigningIn = false;

Future<bool> signInGoogle(
    {BuildContext? context,
    bool? waitForCompletion,
    bool? drivePermissions,
    bool? gMailPermissions,
    bool? drivePermissionsAttachments,
    bool? silentSignIn,
    bool forceNewAuth = false,
    Function()? next}) async {
  if (await checkLockedFeatureIfInDemoMode(context) == false) return false;
  if (appStateSettings["emailScanning"] == false) gMailPermissions = false;

  // Prevent multiple simultaneous sign-in attempts
  if (_isSigningIn) {
    print('Sign in already in progress');
    return false;
  }
  
  _isSigningIn = true;
  
  // Track if this is a retry after clearing data
  final isRetry = appStateSettings["isRetryLogin"] ?? false;
  if (isRetry) {
    print('Retrying login after clearing data...');
  }
  
  try {
    // Check if we're online - add a small delay first to prevent race conditions
    await Future.delayed(Duration(milliseconds: 500));
    bool isOnline = await checkInternetConnection();
    
    // If this is a retry after clearing data, wait a bit longer
    final isRetry = appStateSettings["isRetryLogin"] ?? false;
    if (isRetry) {
      await Future.delayed(Duration(seconds: 2));
    }

    // If we're offline, check for preserved authentication state
    if (!isOnline && googleUser == null) {
      print("Device is offline, checking for preserved authentication state");

      // First check if we have preserved user data from previous online session
      String? preservedEmail = appStateSettings["currentUserEmail"];
      bool hasSignedIn = appStateSettings["hasSignedIn"] ?? false;

      if (hasSignedIn && preservedEmail != null && preservedEmail.isNotEmpty) {
        print("Found preserved user data for offline mode: $preservedEmail");

        // Ensure offline mode is set
        updateSettings("isOfflineMode", true, updateGlobalState: true);
        updateSettings("skipTokenValidation", true, updateGlobalState: true);

        // Refresh UI
        accountsPageStateKey.currentState?.refreshState();
        settingsPageStateKey.currentState?.refreshState();

        // Show offline mode notification
        ConnectivityManager().showConnectivitySnackbar();

        if (waitForCompletion == true && context != null) {
          Navigator.of(context).pop();
        }

        if (next != null) next();

        return true;
      }

      // If no preserved data, check Firebase Auth state
      final firebaseUser = FirebaseAuthManager.instance.currentUser;
      if (firebaseUser != null) {
        print("Firebase Auth: Found cached user - ${firebaseUser.email}");

        // Update app state with Firebase user data
        updateSettings("currentUserEmail", firebaseUser.email ?? "", updateGlobalState: true);
        updateSettings("currentUserName", firebaseUser.displayName ?? "", updateGlobalState: true);
        updateSettings("currentUserPhotoUrl", firebaseUser.photoURL ?? "", updateGlobalState: true);
        updateSettings("isOfflineMode", true, updateGlobalState: true);
        updateSettings("hasSignedIn", true, updateGlobalState: true);
        updateSettings("skipTokenValidation", true, updateGlobalState: true);

        // Refresh UI
        accountsPageStateKey.currentState?.refreshState();
        settingsPageStateKey.currentState?.refreshState();

        // Show offline mode notification
        ConnectivityManager().showConnectivitySnackbar();

        if (waitForCompletion == true && context != null) {
          Navigator.of(context).pop();
        }

        if (next != null) next();

        return true;
      } else {
        // No user data available for offline mode
        if (context != null) {
          openSnackbar(
            SnackbarMessage(
              title: "offline-no-credentials".tr(),
              icon: Icons.cloud_off,
              timeout: Duration(milliseconds: 4000),
            ),
          );
        }

        if (waitForCompletion == true && context != null) {
          Navigator.of(context).pop();
        }

        return false;
      }
    }

    // Online authentication flow - skip if offline or token validation disabled
    bool skipTokenValidation = appStateSettings["skipTokenValidation"] ?? false;
    bool isOfflineMode = appStateSettings["isOfflineMode"] ?? false;

    if (!isOfflineMode && !skipTokenValidation && gMailPermissions == true &&
        googleUser != null &&
        !(await testIfHasGmailAccess())) {
      await signOutGoogle();
      googleSignIn = null;
      settingsPageStateKey.currentState?.refreshState();
    } else if (googleUser == null) {
      googleSignIn = null;
      settingsPageStateKey.currentState?.refreshState();
    }

    if (waitForCompletion == true && context != null) openLoadingPopup(context);
    if (googleUser == null) {
      List<String> scopes = [
        ...(drivePermissions == true ? [drive.DriveApi.driveAppdataScope] : []),
        ...(drivePermissionsAttachments == true
            ? [drive.DriveApi.driveFileScope]
            : []),
        ...(gMailPermissions == true
            ? [
                gMail.GmailApi.gmailReadonlyScope,
                gMail.GmailApi
                    .gmailModifyScope //We do this so the emails can be marked read
              ]
            : [])
      ];

      // Initialize GoogleSignIn with appropriate configuration based on platform
      print("=== Google Sign-In Debug Info ===");
      print("Platform: ${kIsWeb ? 'Web' : 'Native'}");
      print("Silent sign-in: $silentSignIn");
      print("Force new auth: $forceNewAuth");
      print("Drive permissions: $drivePermissions");
      print("Current user: ${googleUser?.email ?? 'None'}");
      print("Scopes: $scopes");

      if (kIsWeb) {
        // Web implementation - use the correct client ID for web
        print("Using web client ID: 299372626077-9t0vnmomck2ipg36diprn1r87ibs1era.apps.googleusercontent.com");
        googleSignIn = signIn.GoogleSignIn(
          clientId: "299372626077-9t0vnmomck2ipg36diprn1r87ibs1era.apps.googleusercontent.com",
          scopes: scopes
        );
      } else if (getPlatform() == PlatformOS.isIOS) {
        // iOS implementation
        print("Using iOS client ID: ${DefaultFirebaseOptions.currentPlatform.iosClientId}");
        googleSignIn = signIn.GoogleSignIn(
          clientId: DefaultFirebaseOptions.currentPlatform.iosClientId,
          scopes: scopes
        );
      } else if (isDesktopPlatform()) {
        // Desktop implementation - the GoogleSignInDart package should already be registered in main.dart
        print("Using desktop standard configuration");
        googleSignIn = signIn.GoogleSignIn.standard(scopes: scopes);
      } else {
        // Android implementation
        print("Using Android standard configuration");
        googleSignIn = signIn.GoogleSignIn.standard(scopes: scopes);
      }

      // If forceNewAuth is true, we need to sign out first to clear any existing tokens
      // This ensures we get a fresh authentication dialog from Google
      if (forceNewAuth) {
        print("Forcing new authentication with complete cleanup");

        // First, disconnect from Google to revoke any existing tokens
        try {
          await googleSignIn?.disconnect();
          print("Successfully disconnected from Google");
        } catch (e) {
          print("Error disconnecting from Google: $e");
          // Continue anyway - we'll try to clean up everything else
        }

        // Then sign out to clear session state
        try {
          await googleSignIn?.signOut();
          print("Successfully signed out from Google");
        } catch (e) {
          print("Error signing out from Google: $e");
          // Continue anyway - we'll try to clean up everything else
        }

        // Clear Firebase Auth state
        await FirebaseAuthManager.instance.signOut();
        print("Cleared Firebase auth data");

        // Clear any other cached state
        googleUser = null;

        // On all platforms, ensure we get a completely fresh authentication
        // by creating a new GoogleSignIn instance
        googleSignIn = null;

        // Wait a moment to ensure all cleanup is complete
        await Future.delayed(Duration(milliseconds: 500));
        print("Completed full authentication cleanup");
      }

      // Determine sign-in method based on parameters
      signIn.GoogleSignInAccount? account;

      if (forceNewAuth) {
        // Always use interactive sign-in when forcing new auth
        // This is critical for handling revoked access cases
        print("Using interactive sign-in with forceNewAuth=true");

        // Force disconnect to ensure we get a completely fresh authentication
        await googleSignIn?.disconnect();

        // Use interactive sign-in to get a new token
        account = await googleSignIn?.signIn();

        // If we got an account after forcing new auth, this means the user has
        // successfully re-authenticated after access was revoked
        if (account != null) {
          print("Successfully re-authenticated after access was revoked");

          // Reset any error flags
          errorSigningInDuringCloud = false;
        }
      } else if (silentSignIn == true) {
        // Use appropriate silent sign-in method based on platform
        if (kIsWeb) {
          // Web doesn't support silent sign-in well, use interactive
          account = await googleSignIn?.signIn();
        } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
          // On desktop, try silent sign-in first, but be prepared for it to fail
          try {
            account = await googleSignIn?.signInSilently();
          } catch (e) {
            print("Silent sign-in failed on desktop: $e");
            // If silent sign-in fails on desktop and we're not forcing new auth,
            // we'll still return null and let the caller handle it
            account = null;
          }
        } else {
          // Standard silent sign-in for mobile platforms
          account = await googleSignIn?.signInSilently();
        }
      } else {
        // Regular interactive sign-in
        print("Attempting interactive sign-in...");
        account = await googleSignIn?.signIn();
      }

      print("Sign-in result: ${account != null ? 'Success - ${account!.email}' : 'Failed - no account returned'}");

      if (account != null) {
        googleUser = account;

        // Sign in to Firebase with Google credentials
        final firebaseUser = await FirebaseAuthManager.instance.signInWithGoogle();
        if (firebaseUser == null) {
          print("Failed to sign in to Firebase with Google credentials");
          // Continue with Google Sign-In only, but log the issue
        } else {
          print("Successfully signed in to Firebase: ${firebaseUser.email}");
        }

        // Firebase Auth handles token validation automatically
        print("Authentication completed successfully");

        // Update app settings
        await updateSettings(
          "currentUserEmail",
          googleUser?.email ?? "",
          updateGlobalState: true,
          forceGlobalStateUpdate:
              context == null || getIsFullScreen(context) ? true : false,
        );
        await updateSettings("hasSignedIn", true, updateGlobalState: true);

        // Check if this is the first login
        int numLogins = appStateSettings["numLogins"] ?? 0;
        bool isFirstLogin = numLogins <= 1;

        // For first login, we need special handling to prevent automatic logout
        if (isFirstLogin) {
          print("First login detected, applying special first-login handling");

          // CRITICAL: For first login, ALWAYS set to online mode
          // This is the most important change - we force online mode for first login
          // regardless of actual connectivity status
          await updateSettings("isOfflineMode", false, updateGlobalState: true);

          // Disable token validation completely for first login session
          // This prevents the app from checking token validity and potentially logging out
          await updateSettings("skipTokenValidation", true, updateGlobalState: true);

          print("First login: Forced online mode and disabled token validation");

          // Trigger initial data sync for first-time login
          try {
            print("Triggering initial data sync for first-time login");
            await Future.delayed(Duration(milliseconds: 1000)); // Give time for auth to settle

            // Import any existing data from the cloud
            if (appStateSettings["backupSync"] == true) {
              await syncData(context);
            }
          } catch (e) {
            print("Error during first-time sync: $e");
          }
        } else {
          // For subsequent logins, use normal connectivity check but be more lenient
          try {
            bool isStillOnline = await checkInternetConnection();
            await updateSettings("isOfflineMode", !isStillOnline, updateGlobalState: true);
          } catch (e) {
            // If connectivity check fails, default to online mode
            print("Connectivity check failed, defaulting to online mode: $e");
            await updateSettings("isOfflineMode", false, updateGlobalState: true);
          }

          // For non-first logins, enable token validation
          await updateSettings("skipTokenValidation", false, updateGlobalState: true);
        }

        // Increment login count to track first-time login
        await updateSettings("numLogins", numLogins + 1, updateGlobalState: false);

        // For first login or retry after clearing data, handle setup
        final isRetry = appStateSettings["isRetryLogin"] ?? false;
        if (isFirstLogin || isRetry) {
          print("${isRetry ? 'Retry login' : 'First login'}: Setting up authentication");

          // Clear the retry flag if it was set
          if (isRetry) {
            await updateSettings("isRetryLogin", false, updateGlobalState: true);
          }

          // Mark the first login time
          final now = DateTime.now();
          final nowString = now.toIso8601String();
          await updateSettings("firstLoginTime", nowString, updateGlobalState: true);

          print("${isRetry ? 'Retry' : 'First'} login completed at $nowString");
        }

        // Store user display name and photo URL if available
        if (account.displayName != null) {
          await updateSettings("currentUserName", account.displayName, updateGlobalState: true);
        }
        if (account.photoUrl != null) {
          await updateSettings("currentUserPhotoUrl", account.photoUrl, updateGlobalState: true);
        }

        // Reset any error flags
        errorSigningInDuringCloud = false;

        // Refresh UI
        accountsPageStateKey.currentState?.refreshState();
        settingsPageStateKey.currentState?.refreshState();
      } else {
        throw ("Login failed");
      }
    }

    if (waitForCompletion == true && context != null)
      Navigator.of(context).pop();
    if (next != null) next();

    if (appStateSettings["hasSignedIn"] == false) {
      updateSettings("hasSignedIn", true, updateGlobalState: false);
    }

    return true;
  } catch (e) {
    print("Sign-in error: $e");
    
    // Ensure we reset the signing in flag
    _isSigningIn = false;
    
    if (waitForCompletion == true && context != null && context.mounted) {
      Navigator.of(context).pop();
    }

    // Set retry flag for network-related errors
    if (e.toString().contains('network') || 
        e.toString().contains('timeout') ||
        e.toString().contains('connection')) {
      await updateSettings("isRetryLogin", true, updateGlobalState: true);
    }

    // Handle authentication errors
    String errorString = e.toString().toLowerCase();
    bool isAuthError = errorString.contains('401') ||
                      errorString.contains('unauthorized') ||
                      errorString.contains('unauthenticated') ||
                      errorString.contains('invalid_grant') ||
                      errorString.contains('access_denied') ||
                      errorString.contains('revoked');

    if (isAuthError) {
      await signOutGoogle();
    }

    // Show appropriate error message based on error type
    String errorTitle = "sign-in-error".tr();
    String errorDescription = "sign-in-error-description".tr();

    if (errorString.contains("network") || errorString.contains("connection")) {
      errorTitle = "network-error".tr();
      errorDescription = "check-internet-connection".tr();
    } else if (errorString.contains("canceled") || errorString.contains("cancelled") || errorString.contains("aborted")) {
      errorTitle = "sign-in-cancelled".tr();
      errorDescription = "sign-in-cancelled-description".tr();
    } else if (errorString.contains("timeout")) {
      errorTitle = "sign-in-timeout".tr();
      errorDescription = "sign-in-timeout-description".tr();
    } else if (errorString.contains("permission") || errorString.contains("access")) {
      errorTitle = "permission-denied".tr();
      errorDescription = "permission-denied-description".tr();
    }

    // Show the appropriate error message
    openSnackbar(
      SnackbarMessage(
        title: errorTitle,
        description: errorDescription,
        icon: appStateSettings["outlinedIcons"]
            ? Icons.error_outlined
            : Icons.error_rounded,
        timeout: Duration(milliseconds: 5000),
      ),
    );

    // Clear user data and update settings
    await Future.wait([
      updateSettings("currentUserEmail", "", updateGlobalState: true),
      updateSettings("hasSignedIn", false, updateGlobalState: true),
      updateSettings("isOfflineMode", false, updateGlobalState: true),
      updateSettings("skipTokenValidation", false, updateGlobalState: true),
      updateSettings("firstLoginTime", null, updateGlobalState: true),
      updateSettings("isRetryLogin", false, updateGlobalState: true),
    ]);

    // Reset the signing in flag
    _isSigningIn = false;

    if (runningCloudFunctions) {
      errorSigningInDuringCloud = true;
    }

    throw ("Error signing in");
  }
}

/// Shows a snackbar to the user explaining they need to re-authenticate
/// Returns true if re-authentication was successful, false otherwise
Future<bool> showReauthenticationDialog(BuildContext context) async {
  print("Showing re-authentication snackbar");

  // Create a completer to handle the async result
  Completer<bool> completer = Completer<bool>();

  // Show snackbar with tap instruction in the description
  openSnackbar(
    SnackbarMessage(
      title: 'access-revoked'.tr(),
      icon: appStateSettings["outlinedIcons"]
          ? Icons.security_outlined
          : Icons.security_rounded,
      timeout: Duration(milliseconds: 4000),
      onTap: () async {
        // Handle sign-in again action
        await _handleReauthentication(context, completer);
      },
    ),
  );

  // Wait for the user to either tap the action button or for the snackbar to dismiss
  // If the snackbar is dismissed without action, complete with false
  Future.delayed(Duration(milliseconds: 8500), () {
    if (!completer.isCompleted) {
      completer.complete(false);
    }
  });

  return completer.future;
}

/// Helper method to handle the re-authentication process
Future<void> _handleReauthentication(BuildContext context, Completer<bool> completer) async {
  print("User chose to sign in again");

  // Clear any existing tokens and credentials
  await signOutGoogle();

  // User chose to sign in again
  bool signInSuccess = await signInGoogle(
    context: context,
    waitForCompletion: true,
    drivePermissions: true,
    silentSignIn: false, // Force interactive sign-in
    forceNewAuth: true, // Force new authentication to clear any invalid tokens
  );

  if (signInSuccess) {
    print("Re-authentication successful");

    // Reset error flags
    errorSigningInDuringCloud = false;

    // Show success message
    openSnackbar(
      SnackbarMessage(
        title: "access-restored".tr(),
        icon: appStateSettings["outlinedIcons"]
            ? Icons.check_circle_outlined
            : Icons.check_circle_rounded,
        timeout: Duration(milliseconds: 2500),
      ),
    );

    // Try to restore any interrupted operations
    if (appStateSettings["autoBackups"] == true) {
      // Schedule a backup for later to avoid overwhelming the user
      Future.delayed(Duration(seconds: 5), () async {
        try {
          if (context.mounted) {
            await createBackupInBackground(context);
          }
        } catch (e) {
          print("Error scheduling backup after re-authentication: $e");
        }
      });
    }
  } else {
    print("Re-authentication failed");

    // Show error message
    openSnackbar(
      SnackbarMessage(
        title: "sign-in-error".tr(),
        icon: appStateSettings["outlinedIcons"]
            ? Icons.error_outlined
            : Icons.error_rounded,
        timeout: Duration(milliseconds: 4000),
      ),
    );
  }

  // Complete the completer with the result
  if (!completer.isCompleted) {
    completer.complete(signInSuccess);
  }
}

Future<bool> testIfHasGmailAccess() async {
  print("TESTING GMAIL");

  // Skip Gmail access test if offline or token validation is disabled
  bool skipTokenValidation = appStateSettings["skipTokenValidation"] ?? false;
  bool isOfflineMode = appStateSettings["isOfflineMode"] ?? false;

  if (isOfflineMode || skipTokenValidation) {
    print("Skipping Gmail access test - offline mode or token validation disabled");
    return true; // Assume access is available when offline
  }

  try {
    final authHeaders = await googleUser!.authHeaders;
    final authenticateClient = GoogleAuthClient(authHeaders);
    gMail.GmailApi gmailApi = gMail.GmailApi(authenticateClient);
    gMail.ListMessagesResponse results = await gmailApi.users.messages
        .list(googleUser!.id.toString(), maxResults: 1);
  } catch (e) {
    print(e.toString());
    print("NO GMAIL");

    // Check if error is related to authentication
    String errorMessage = e.toString().toLowerCase();
    if (errorMessage.contains('401') ||
        errorMessage.contains('unauthorized') ||
        errorMessage.contains('invalid_grant') ||
        errorMessage.contains('access_denied')) {
      // This is likely due to revoked access
      print("Gmail access appears to be revoked");
    }

    return false;
  }
  return true;
}

Future<bool> signOutGoogle() async {
  try {
    print('Starting sign out process...');

    // 1. Clean up authentication state
    print('Cleaning up authentication state...');

    // 2. Sign out from Firebase Auth
    try {
      await FirebaseAuthManager.instance.signOut();
      print('Firebase Auth sign-out completed');
    } catch (e) {
      print('Error during Firebase sign-out: $e');
      // Continue with cleanup even if sign-out fails
    }

    // 3. Clear Google Sign-In session
    try {
      await googleSignIn?.signOut();
      googleUser = null;
      print('Google Sign-Out completed');
    } catch (e) {
      print('Error during Google sign-out: $e');
      // Continue with cleanup even if sign-out fails
    }

    // 4. Reset all relevant settings
    await Future.wait([
      updateSettings("currentUserEmail", "", updateGlobalState: true),
      updateSettings("hasSignedIn", false, updateGlobalState: true),
      updateSettings("isOfflineMode", false, updateGlobalState: true),
      updateSettings("currentUserName", "", updateGlobalState: true),
      updateSettings("currentUserPhotoUrl", "", updateGlobalState: true),
      updateSettings("skipTokenValidation", false, updateGlobalState: true),
      updateSettings("firstLoginTime", null, updateGlobalState: true),
    ]);
    print('Reset all user settings');

    // Force refresh UI components
    Future.microtask(() {
      try {
        accountsPageStateKey.currentState?.refreshState();
        settingsPageStateKey.currentState?.refreshState();
      } catch (e) {
        print('Error refreshing UI: $e');
      }
    });

    print('Sign out completed successfully');
    return true;
  } catch (e) {
    print('Error during sign out: $e');
    // Even if there's an error, try to clean up as much as possible
    googleUser = null;
    googleSignIn = null;
    return false;
  }
}

Future<bool> refreshGoogleSignIn() async {
  try {
    print("Refreshing Google Sign-In");

    // Check if we need to refresh authentication
    if (googleUser == null) {
      print("No user found, attempting fresh authentication");

      // Attempt fresh sign-in
      bool success = await signInGoogle(
        silentSignIn: false,
        drivePermissions: true,
        forceNewAuth: true,
      );

      return success && googleUser != null;
    } else {
      // Normal refresh flow

      // Sign out first to clear any invalid tokens
      await signOutGoogle();

      // Try silent sign-in first based on platform
      bool silentSignInSuccess;

      if (kIsWeb) {
        // Web doesn't support silent sign-in well, use interactive
        silentSignInSuccess = false;
      } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        // On desktop, try silent sign-in but be prepared for it to fail
        try {
          silentSignInSuccess = await signInGoogle(
            silentSignIn: true,
            drivePermissions: true,
          );

          if (!silentSignInSuccess) {
            print("Silent sign-in failed on desktop, will try interactive sign-in");
          }
        } catch (e) {
          print("Error during silent sign-in on desktop: $e");
          silentSignInSuccess = false;
        }
      } else {
        // Mobile platforms
        silentSignInSuccess = await signInGoogle(
          silentSignIn: true,
          drivePermissions: true,
        );
      }

      // If silent sign-in fails, try interactive sign-in
      if (!silentSignInSuccess) {
        print("Silent sign-in failed, trying interactive sign-in");
        bool interactiveSuccess = await signInGoogle(
          silentSignIn: false,
          drivePermissions: true,
        );

        return interactiveSuccess && googleUser != null;
      }

      // Return the result of the sign-in attempt
      return silentSignInSuccess && googleUser != null;
    }
  } catch (e) {
    print("Error refreshing Google Sign-In: $e");

    // Check if this is an authentication error
    String errorMessage = e.toString().toLowerCase();
    bool isAuthError = errorMessage.contains('401') ||
                      errorMessage.contains('unauthorized') ||
                      errorMessage.contains('unauthenticated') ||
                      errorMessage.contains('invalid_grant') ||
                      errorMessage.contains('access_denied') ||
                      errorMessage.contains('revoked');

    if (isAuthError) {
      // Handle authentication error by signing out
      await signOutGoogle();
      print("Handled authentication error by signing out");
    }

    return false;
  }
}

Future<bool> signInAndSync(BuildContext context,
    {required dynamic Function() next}) async {
  dynamic result = true;
  if (getPlatform() == PlatformOS.isIOS &&
      navigatorKey.currentContext != null &&
      appStateSettings["hasSignedIn"] != true) {
    result = await openPopup(
      navigatorKey.currentContext!,
      icon: appStateSettings["outlinedIcons"]
          ? Icons.badge_outlined
          : Icons.badge_rounded,
      title: "backups".tr(),
      description: "google-drive-backup-disclaimer".tr(),
      onSubmitLabel: "continue".tr(),
      onSubmit: () {
        Navigator.pop(navigatorKey.currentContext!, true);
      },
      onCancel: () {
        Navigator.pop(navigatorKey.currentContext!);
      },
      onCancelLabel: "cancel".tr(),
    );
  }

  // Convert result to boolean to avoid type error
  bool shouldContinue = (result == true);
  if (!shouldContinue) return false;
  loadingIndeterminateKey.currentState?.setVisibility(true);
  try {
    // First, try to sign in
    bool signInSuccess = await signInGoogle(
      context: context,
      waitForCompletion: false,
      drivePermissions: true,
      next: next,
    );

    // If sign-in failed, show re-authentication dialog
    if (!signInSuccess || googleUser == null) {
      loadingIndeterminateKey.currentState?.setVisibility(false);
      bool reAuthSuccess = await showReauthenticationDialog(context);
      if (!reAuthSuccess) {
        return false;
      }
      loadingIndeterminateKey.currentState?.setVisibility(true);
    }

    // Update username if needed
    if (appStateSettings["username"] == "" && googleUser != null) {
      updateSettings("username", googleUser?.displayName ?? "",
          pagesNeedingRefresh: [0], updateGlobalState: false);
    }

    // Proceed with sync operations if user is logged in
    if (googleUser != null) {
      try {
        loadingIndeterminateKey.currentState?.setVisibility(true);
        await syncData(context);

        loadingIndeterminateKey.currentState?.setVisibility(true);
        await syncPendingQueueOnServer();

        loadingIndeterminateKey.currentState?.setVisibility(true);
        await getCloudBudgets();

        loadingIndeterminateKey.currentState?.setVisibility(true);
        await createBackupInBackground(context);
      } catch (syncError) {
        print("Error during sync operations: $syncError");
        loadingIndeterminateKey.currentState?.setVisibility(false);

        // Handle sync error
        String errorString = syncError.toString().toLowerCase();
        bool isAuthError = errorString.contains('401') ||
                          errorString.contains('unauthorized') ||
                          errorString.contains('unauthenticated') ||
                          errorString.contains('invalid_grant') ||
                          errorString.contains('access_denied') ||
                          errorString.contains('revoked');

        if (isAuthError) {
          // If it was an auth error, try to re-authenticate and sync again
          await signOutGoogle();
          bool reAuthSuccess = await showReauthenticationDialog(context);
          if (reAuthSuccess && googleUser != null) {
            loadingIndeterminateKey.currentState?.setVisibility(true);
            return await signInAndSync(context, next: next);
          }
        } else {
          // For other errors, just show the error
          openSnackbar(
            SnackbarMessage(
              title: "Sync Error".tr(),
              icon: appStateSettings["outlinedIcons"]
                  ? Icons.error_outlined
                  : Icons.error_rounded,
              timeout: Duration(milliseconds: 4000),
            ),
          );
        }
      }
    } else {
      throw ("cannot sync data - user not logged in");
    }

    loadingIndeterminateKey.currentState?.setVisibility(false);
    return true;
  } catch (e) {
    print("Error syncing data after login!");
    print(e.toString());
    loadingIndeterminateKey.currentState?.setVisibility(false);

    // Provide more helpful error messages based on error type
    String errorTitle = "sync-error".tr();
    String errorDescription = "please-sign-in-again".tr();

    // Check for specific error types
    String errorString = e.toString().toLowerCase();

    if (errorString.contains("network") || errorString.contains("connection")) {
      errorTitle = "network-error".tr();
      errorDescription = "check-internet-connection".tr();
    } else if (errorString.contains("user not logged in") || errorString.contains("cannot sync data")) {
      errorTitle = "sync-error-not-logged-in".tr();
      errorDescription = "sync-error-not-logged-in-description".tr();
    } else if (errorString.contains("permission") || errorString.contains("access")) {
      errorTitle = "permission-denied".tr();
      errorDescription = "permission-denied-sync-description".tr();
    } else if (errorString.contains("timeout")) {
      errorTitle = "sync-timeout".tr();
      errorDescription = "sync-timeout-description".tr();
    }

    // Add tap instruction to the description
    errorDescription += "\n\n" + "tap-to-try-again".tr();

    openSnackbar(
      SnackbarMessage(
        title: errorTitle,
        description: errorDescription,
        icon: appStateSettings["outlinedIcons"]
            ? Icons.error_outlined
            : Icons.error_rounded,
        timeout: Duration(milliseconds: 5000),
        onTap: () async {
          // Try to sign in again
          await signInGoogle(
            context: context,
            waitForCompletion: true,
            drivePermissions: true,
            silentSignIn: false,
            forceNewAuth: true,
          );
        },
      ),
    );

    return false;
  }
}

Future<void> createBackupInBackground(context) async {
  if (appStateSettings["hasSignedIn"] == false) return;
  if (errorSigningInDuringCloud == true) return;
  if (kIsWeb && !entireAppLoaded) return;
  // print(entireAppLoaded);
  print("Last backup: " + appStateSettings["lastBackup"]);
  //Only run this once, don't run again if the global state changes (e.g. when changing a setting)
  // Update: Does this still run when global state changes? I don't think so...
  // If the entire app is loaded and we want to do an auto backup, lets do it no matter what!
  // if (entireAppLoaded == false || entireAppLoaded) {
  if (appStateSettings["autoBackups"] == true) {
    DateTime lastUpdate = DateTime.parse(appStateSettings["lastBackup"]);
    DateTime nextPlannedBackup = lastUpdate
        .add(Duration(days: appStateSettings["autoBackupsFrequency"]));
    print("next backup planned on " + nextPlannedBackup.toString());
    if (DateTime.now().millisecondsSinceEpoch >=
        nextPlannedBackup.millisecondsSinceEpoch) {
      print("auto backing up");

      bool hasSignedIn = false;
      if (googleUser == null) {
        hasSignedIn = await signInGoogle(
            context: context,
            gMailPermissions: false,
            waitForCompletion: false,
            silentSignIn: true);
      } else {
        hasSignedIn = true;
      }
      if (hasSignedIn == false) {
        return;
      }
      await createBackup(context, silentBackup: true, deleteOldBackups: true);
    } else {
      print("backup already made today");
    }
  }
  // }
  return;
}

Future forceDeleteDB() async {
  if (kIsWeb) {
    final html.Storage localStorage = html.window.localStorage;
    localStorage.clear();
  } else {
    final dbFolder = await getApplicationDocumentsDirectory();
    final dbFile = File(p.join(dbFolder.path, 'db.sqlite'));
    await dbFile.delete();
  }
}

bool openDatabaseCorruptedPopup(BuildContext context) {
  if (isDatabaseCorrupted) {
    openPopup(
      context,
      icon: appStateSettings["outlinedIcons"]
          ? Icons.heart_broken_outlined
          : Icons.heart_broken_rounded,
      title: "database-corrupted".tr(),
      description: "database-corrupted-description".tr(),
      barrierDismissible: false,
      onSubmit: () async {
        Navigator.pop(context);
        await importDB(context, ignoreOverwriteWarning: true);
      },
      onSubmitLabel: "import-backup".tr(),
      onCancel: () async {
        Navigator.pop(context);
        await openLoadingPopupTryCatch(() async {
          await forceDeleteDB();
          await sharedPreferences.clear();
        });
        restartAppPopup(context);
      },
      onCancelLabel: "reset".tr(),
    );
    // Lock the side navigation
    lockAppWaitForRestart = true;
    appStateKey.currentState?.refreshAppState();
    return true;
  }
  return false;
}

Future<void> createBackup(
  context, {
  bool? silentBackup,
  bool deleteOldBackups = false,
  String? clientIDForSync,
}) async {
  try {
    if (silentBackup == false || silentBackup == null) {
      loadingIndeterminateKey.currentState?.setVisibility(true);
    }

    // Always save the current state of debug settings
    // This ensures the settings are consistent in the backup
    await updateSettings("savedDangerousDebugFlags", allowDangerousDebugFlags,
        updateGlobalState: true);

    // Make sure rememberDebugSettings is also saved
    // This ensures the setting is properly synced between devices
    if (appStateSettings["rememberDebugSettings"] == null) {
      // If not set, default to false
      await updateSettings("rememberDebugSettings", false,
          updateGlobalState: true);
    }

    // If debug settings should be remembered, save all current debug settings
    if (appStateSettings["rememberDebugSettings"] == true) {
      print("Debug settings will be included in backup - saving all current debug settings");

      // Save all debug page settings to ensure they're included in backups
      await updateSettings("savedManuallyActivatedPro",
          appStateSettings["manuallyActivatedPro"] == true,
          updateGlobalState: false);

      await updateSettings("savedShowCumulativeSpending",
          appStateSettings["showCumulativeSpending"] == true,
          updateGlobalState: false);

      await updateSettings("savedRemoveZeroTransactionEntries",
          appStateSettings["removeZeroTransactionEntries"] == true,
          updateGlobalState: false);

      await updateSettings("savedIgnorePastAmountSpent",
          appStateSettings["ignorePastAmountSpent"] == true,
          updateGlobalState: false);

      await updateSettings("savedShowPastSpendingTrajectory",
          appStateSettings["showPastSpendingTrajectory"] == true,
          updateGlobalState: false);

      await updateSettings("savedCircularProgressRotation",
          appStateSettings["circularProgressRotation"] == true,
          updateGlobalState: false);

      await updateSettings("savedNonCompactTransactions",
          appStateSettings["nonCompactTransactions"] == true,
          updateGlobalState: false);

      await updateSettings("savedRestrictAmountOfInitiallyLoadedTransactions",
          appStateSettings["restrictAmountOfInitiallyLoadedTransactions"] == true,
          updateGlobalState: false);

      await updateSettings("savedFadeTransactionNameOverflows",
          appStateSettings["fadeTransactionNameOverflows"] == true,
          updateGlobalState: false);

      await updateSettings("savedShowFAQAndHelpLink",
          appStateSettings["showFAQAndHelpLink"] == true,
          updateGlobalState: false);

      await updateSettings("savedBatterySaver",
          appStateSettings["batterySaver"] == true,
          updateGlobalState: false);

      await updateSettings("savedNotificationScanningDebug",
          appStateSettings["notificationScanningDebug"] == true,
          updateGlobalState: false);

      await updateSettings("savedColorTintCategoryIcon",
          appStateSettings["colorTintCategoryIcon"] == true,
          updateGlobalState: false);

      await updateSettings("savedAccountColorfulAmountsWithArrows",
          appStateSettings["accountColorfulAmountsWithArrows"] == true,
          updateGlobalState: false);

      await updateSettings("savedNetTotalsColorful",
          appStateSettings["netTotalsColorful"] == true,
          updateGlobalState: false);

      await updateSettings("savedEmailScanning",
          appStateSettings["emailScanning"] == true,
          updateGlobalState: false);

      await updateSettings("savedEmailScanningPullToRefresh",
          appStateSettings["emailScanningPullToRefresh"] == true,
          updateGlobalState: false);

      await updateSettings("savedSharedBudgets",
          appStateSettings["sharedBudgets"] == true,
          updateGlobalState: false);

      await updateSettings("savedSyncEveryChange",
          appStateSettings["syncEveryChange"] == true,
          updateGlobalState: false);

      await updateSettings("savedIOSEmulate",
          appStateSettings["iOSEmulate"] == true,
          updateGlobalState: false);

      await updateSettings("savedIOSAnimatedGoo",
          appStateSettings["iOSAnimatedGoo"] == true,
          updateGlobalState: false);

      await updateSettings("savedIncognitoKeyboard",
          appStateSettings["incognitoKeyboard"] == true,
          updateGlobalState: false);

      await updateSettings("savedDisableShadows",
          appStateSettings["disableShadows"] == true,
          updateGlobalState: false);

      await updateSettings("savedShowCashewProPage",
          appStateSettings["showCashewProPage"] == true,
          updateGlobalState: false);

      print("All debug settings saved for backup: rememberDebugSettings=${appStateSettings["rememberDebugSettings"]}, savedDangerousDebugFlags=${appStateSettings["savedDangerousDebugFlags"]}");
    } else {
      print("Debug settings will NOT be included in backup (rememberDebugSettings is disabled)");
    }

    // Backup all settings
    await backupSettings();
  } catch (e) {
    if (silentBackup == false || silentBackup == null) {
      Navigator.of(context).maybePop();
    }
    // Provide more helpful error messages based on error type
    String errorTitle = "backup-error".tr();
    String errorDescription = e.toString();

    // Check for specific error types
    String errorString = e.toString().toLowerCase();

    if (errorString.contains("network") || errorString.contains("connection")) {
      errorTitle = "network-error".tr();
      errorDescription = "check-internet-connection".tr();
    } else if (errorString.contains("permission") || errorString.contains("access")) {
      errorTitle = "permission-denied".tr();
      errorDescription = "permission-denied-backup-description".tr();
    } else if (errorString.contains("storage") || errorString.contains("space")) {
      errorTitle = "storage-error".tr();
      errorDescription = "insufficient-storage-description".tr();
    }

    openSnackbar(
      SnackbarMessage(
        title: errorTitle,
        description: errorDescription,
        icon: appStateSettings["outlinedIcons"]
            ? Icons.error_outlined
            : Icons.error_rounded,
        timeout: Duration(milliseconds: 5000),
      ),
    );
  }

  try {
    if (deleteOldBackups)
      await deleteRecentBackups(context, appStateSettings["backupLimit"],
          silentDelete: true);

    // Check if we're offline or token validation is disabled
    bool skipTokenValidation = appStateSettings["skipTokenValidation"] ?? false;
    bool isOfflineMode = appStateSettings["isOfflineMode"] ?? false;

    if (isOfflineMode || skipTokenValidation) {
      print("Skipping backup creation - offline mode or token validation disabled");
      if (context != null) {
        openSnackbar(
          SnackbarMessage(
            title: "backup-offline".tr(),
            description: "backup-offline-description".tr(),
            icon: Icons.cloud_off,
            timeout: Duration(milliseconds: 3000),
          ),
        );
      }
      return;
    }

    DBFileInfo currentDBFileInfo = await getCurrentDBFileInfo();

    final authHeaders = await googleUser!.authHeaders;
    final authenticateClient = GoogleAuthClient(authHeaders);
    final driveApi = drive.DriveApi(authenticateClient);

    var media = new drive.Media(
        currentDBFileInfo.mediaStream, currentDBFileInfo.dbFileBytes.length);

    var driveFile = new drive.File();
    final timestamp =
        DateFormat("yyyy-MM-dd-hhmmss").format(DateTime.now().toUtc());
    // -$timestamp
    driveFile.name =
        "db-v$schemaVersionGlobal-${getCurrentDeviceName()}.sqlite";
    if (clientIDForSync != null)
      driveFile.name =
          getCurrentDeviceSyncBackupFileName(clientIDForSync: clientIDForSync);
    driveFile.modifiedTime = DateTime.now().toUtc();
    driveFile.parents = ["appDataFolder"];

    await driveApi.files.create(driveFile, uploadMedia: media);

    if (clientIDForSync == null)
      openSnackbar(
        SnackbarMessage(
          title: "backup-created".tr(),
          description: driveFile.name,
          icon: appStateSettings["outlinedIcons"]
              ? Icons.backup_outlined
              : Icons.backup_rounded,
        ),
      );
    if (clientIDForSync == null)
      updateSettings("lastBackup", DateTime.now().toString(),
          pagesNeedingRefresh: [], updateGlobalState: false);

    if (silentBackup == false || silentBackup == null) {
      loadingIndeterminateKey.currentState?.setVisibility(false);
    }
  } catch (e) {
    if (silentBackup == false || silentBackup == null) {
      loadingIndeterminateKey.currentState?.setVisibility(false);
    }

    // Handle authentication errors
    String errorString = e.toString().toLowerCase();
    bool isAuthError = errorString.contains('401') ||
                      errorString.contains('unauthorized') ||
                      errorString.contains('unauthenticated') ||
                      errorString.contains('invalid_grant') ||
                      errorString.contains('access_denied') ||
                      errorString.contains('revoked');

    bool handled = false;
    if (isAuthError) {
      await signOutGoogle();
      handled = true;
    }

    if (!handled) {
      // Provide more helpful error messages based on error type
      String errorTitle = "backup-error".tr();
      String errorDescription = e.toString();

      // Check for specific error types
      String errorString = e.toString().toLowerCase();

      if (errorString.contains("network") || errorString.contains("connection")) {
        errorTitle = "network-error".tr();
        errorDescription = "check-internet-connection".tr();
      } else if (errorString.contains("permission") || errorString.contains("access")) {
        errorTitle = "permission-denied".tr();
        errorDescription = "permission-denied-backup-description".tr();
      } else if (errorString.contains("storage") || errorString.contains("space")) {
        errorTitle = "storage-error".tr();
        errorDescription = "insufficient-storage-description".tr();
      } else if (errorString.contains("quota") || errorString.contains("limit")) {
        errorTitle = "quota-exceeded".tr();
        errorDescription = "quota-exceeded-description".tr();
      }

      // Add tap instruction to the description
      errorDescription += "\n\n" + "tap-to-try-again".tr();

      openSnackbar(
        SnackbarMessage(
          title: errorTitle,
          description: errorDescription,
          icon: appStateSettings["outlinedIcons"]
              ? Icons.error_outlined
              : Icons.error_rounded,
          timeout: Duration(milliseconds: 5000),
          onTap: () async {
            // Try to create backup again
            await createBackup(context, silentBackup: silentBackup, deleteOldBackups: deleteOldBackups);
          },
        ),
      );
    }
  }
}

Future<void> deleteRecentBackups(context, amountToKeep,
    {bool? silentDelete}) async {
  try {
    // Check if we're offline or token validation is disabled
    bool skipTokenValidation = appStateSettings["skipTokenValidation"] ?? false;
    bool isOfflineMode = appStateSettings["isOfflineMode"] ?? false;

    if (isOfflineMode || skipTokenValidation) {
      print("Skipping backup deletion - offline mode or token validation disabled");
      return;
    }

    if (silentDelete == false || silentDelete == null) {
      loadingIndeterminateKey.currentState?.setVisibility(true);
    }

    final authHeaders = await googleUser!.authHeaders;
    final authenticateClient = GoogleAuthClient(authHeaders);
    final driveApi = drive.DriveApi(authenticateClient);
    if (driveApi == null) {
      throw "Failed to login to Google Drive";
    }

    drive.FileList fileList = await driveApi.files.list(
      spaces: 'appDataFolder',
      $fields: 'files(id, name, modifiedTime, size)',
    );
    List<drive.File>? files = fileList.files;
    if (files == null) {
      throw "No backups found.";
    }

    int index = 0;
    files.forEach((file) {
      // subtract 1 because we just made a backup
      if (index >= amountToKeep - 1) {
        // only delete excess backups that don't belong to a client sync
        if (!isSyncBackupFile(file.name)) deleteBackup(driveApi, file.id ?? "");
      }
      if (!isSyncBackupFile(file.name)) index++;
    });
    if (silentDelete == false || silentDelete == null) {
      loadingIndeterminateKey.currentState?.setVisibility(false);
    }
  } catch (e) {
    if (silentDelete == false || silentDelete == null) {
      loadingIndeterminateKey.currentState?.setVisibility(false);
    }
    openSnackbar(
      SnackbarMessage(
          title: e.toString(),
          icon: appStateSettings["outlinedIcons"]
              ? Icons.error_outlined
              : Icons.error_rounded),
    );
  }
}

Future<void> deleteBackup(drive.DriveApi driveApi, String fileId) async {
  try {
    await driveApi.files.delete(fileId);
  } catch (e) {
    openSnackbar(SnackbarMessage(title: e.toString()));
  }
}

Future<void> chooseBackup(context,
    {bool isManaging = false,
    bool isClientSync = false,
    bool hideDownloadButton = false}) async {
  try {
    openBottomSheet(
      context,
      BackupManagement(
        isManaging: isManaging,
        isClientSync: isClientSync,
        hideDownloadButton: hideDownloadButton,
      ),
    );
  } catch (e) {
    Navigator.of(context).pop();
    openSnackbar(
      SnackbarMessage(
          title: e.toString(),
          icon: appStateSettings["outlinedIcons"]
              ? Icons.error_outlined
              : Icons.error_rounded),
    );
  }
}

Future<void> loadBackup(
    BuildContext context, drive.DriveApi driveApi, drive.File file) async {
  try {
    openLoadingPopup(context);

    await cancelAndPreventSyncOperation();

    List<int> dataStore = [];
    dynamic response = await driveApi.files
        .get(file.id ?? "", downloadOptions: drive.DownloadOptions.fullMedia);
    response.stream.listen(
      (data) {
        print("Data: ${data.length}");
        dataStore.insertAll(dataStore.length, data);
      },
      onDone: () async {
        await overwriteDefaultDB(Uint8List.fromList(dataStore));

        // if this is added, it doesn't restore the database properly on web
        // await database.close();
        Navigator.of(context).pop();
        await resetLanguageToSystem(context);
        await updateSettings("databaseJustImported", true,
            pagesNeedingRefresh: [], updateGlobalState: false);
        print(appStateSettings);
        openSnackbar(
          SnackbarMessage(
              title: "backup-restored".tr(),
              icon: appStateSettings["outlinedIcons"]
                  ? Icons.settings_backup_restore_outlined
                  : Icons.settings_backup_restore_rounded),
        );
        Navigator.pop(context);
        restartAppPopup(
          context,
          description: kIsWeb
              ? "refresh-required-to-load-backup".tr()
              : "restart-required-to-load-backup".tr(),
          // codeBlock: file.name.toString() +
          //     (file.modifiedTime == null
          //         ? ""
          //         : ("\n" +
          //             getWordedDateShort(
          //               file.modifiedTime!,
          //               showTodayTomorrow: false,
          //               includeYear: true,
          //             ))),
        );
      },
      onError: (error) {
        openSnackbar(
          SnackbarMessage(
              title: error.toString(),
              icon: appStateSettings["outlinedIcons"]
                  ? Icons.error_outlined
                  : Icons.error_rounded),
        );
      },
    );
  } catch (e) {
    Navigator.of(context).pop();
    openSnackbar(
      SnackbarMessage(
          title: e.toString(),
          icon: appStateSettings["outlinedIcons"]
              ? Icons.error_outlined
              : Icons.error_rounded),
    );
  }
}

class GoogleAccountLoginButton extends StatefulWidget {
  const GoogleAccountLoginButton({
    super.key,
    this.navigationSidebarButton = false,
    this.onTap,
    this.isButtonSelected = false,
    this.isOutlinedButton = true,
    this.forceButtonName,
  });
  final bool navigationSidebarButton;
  final Function? onTap;
  final bool isButtonSelected;
  final bool isOutlinedButton;
  final String? forceButtonName;

  @override
  State<GoogleAccountLoginButton> createState() =>
      _GoogleAccountLoginButtonState();
}

class _GoogleAccountLoginButtonState extends State<GoogleAccountLoginButton> {
  loginWithSync() {
    signInAndSync(
      widget.navigationSidebarButton
          ? navigatorKey.currentContext ?? context
          : context,
      next: () {
        setState(() {});
        if (widget.navigationSidebarButton) {
          if (widget.onTap != null) widget.onTap!();
        } else {
          // Navigator.push(
          //   context,
          //   MaterialPageRoute(
          //     builder: (context) => AccountsPage(),
          //   ),
          // );
          pushRoute(context, AccountsPage());
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.navigationSidebarButton == true) {
      return AnimatedSwitcher(
        duration: Duration(milliseconds: 600),
        child: googleUser == null
            ? getPlatform() == PlatformOS.isIOS
                ? NavigationSidebarButton(
                    key: ValueKey("login"),
                    label: "backup".tr(),
                    icon: MoreIcons.google_drive,
                    iconScale: 0.87,
                    onTap: loginWithSync,
                    isSelected: false,
                  )
                : NavigationSidebarButton(
                    key: ValueKey("login"),
                    label: "login".tr(),
                    icon: MoreIcons.google,
                    onTap: loginWithSync,
                    isSelected: false,
                  )
            : _buildEnhancedSidebarButton(),
      );
    }
    return googleUser == null
        ? Padding(
            padding:
                EdgeInsetsDirectional.symmetric(vertical: 5, horizontal: 4),
            child: getPlatform() == PlatformOS.isIOS
                ? SettingsContainer(
                    isOutlined: widget.isOutlinedButton,
                    onTap: () async {
                      loginWithSync();
                    },
                    title: widget.forceButtonName ?? "backup".tr(),
                    icon: MoreIcons.google_drive,
                    iconScale: 0.87,
                  )
                : SettingsContainer(
                    isOutlined: widget.isOutlinedButton,
                    onTap: () async {
                      loginWithSync();
                    },
                    title: widget.forceButtonName ?? "login".tr(),
                    icon: widget.forceButtonName == null
                        ? MoreIcons.google
                        : MoreIcons.google_drive,
                    iconScale: widget.forceButtonName == null ? 1 : 0.87,
                  ),
          )
        : _buildEnhancedUserButton(context);
  }

  /// Builds a sidebar button for the user
  Widget _buildEnhancedSidebarButton() {
    // Determine icon based on platform
    bool outlined = appStateSettings["outlinedIcons"] == true;
    IconData userIcon = outlined ? Icons.person_outlined : Icons.person_rounded;

    return getPlatform() == PlatformOS.isIOS
      ? NavigationSidebarButton(
          key: ValueKey("user"),
          label: "backup".tr(),
          icon: MoreIcons.google_drive,
          iconScale: 0.87,
          onTap: () async {
            if (widget.onTap != null) widget.onTap!();
          },
          isSelected: widget.isButtonSelected,
        )
      : NavigationSidebarButton(
          key: ValueKey("user"),
          label: googleUser!.displayName ?? "",
          icon: widget.forceButtonName == null ? userIcon : MoreIcons.google_drive,
          iconScale: widget.forceButtonName == null ? 1 : 0.87,
          onTap: () async {
            if (widget.onTap != null) widget.onTap!();
          },
          isSelected: widget.isButtonSelected,
        );
  }

  /// Builds a user button
  Widget _buildEnhancedUserButton(BuildContext context) {
    // Determine icon based on platform
    bool outlined = appStateSettings["outlinedIcons"] == true;
    IconData userIcon = outlined ? Icons.person_outlined : Icons.person_rounded;

    return Padding(
      padding: EdgeInsetsDirectional.symmetric(vertical: 5, horizontal: 4),
      child: SettingsContainerOpenPage(
        openPage: AccountsPage(),
        title: widget.forceButtonName ?? googleUser!.displayName ?? "",
        icon: widget.forceButtonName == null ? userIcon : MoreIcons.google_drive,
        iconScale: widget.forceButtonName == null ? 1 : 0.87,
        isOutlined: widget.isOutlinedButton,
      ),
    );
  }
}

Future<(drive.DriveApi? driveApi, List<drive.File>?)> getDriveFiles() async {
  try {
    // If no user is logged in, try to sign in
    if (googleUser == null) {
      await signInGoogle(drivePermissions: true);
      if (googleUser == null) {
        // If still null after sign-in attempt, return null
        return (null, null);
      }
    }

    // Check if we're offline or token validation is disabled
    bool skipTokenValidation = appStateSettings["skipTokenValidation"] ?? false;
    bool isOfflineMode = appStateSettings["isOfflineMode"] ?? false;

    if (isOfflineMode || skipTokenValidation) {
      print("Skipping backup list retrieval - offline mode or token validation disabled");
      return (null, null);
    }

    // Get auth headers and create API client
    final authHeaders = await googleUser!.authHeaders;
    final authenticateClient = GoogleAuthClient(authHeaders);
    drive.DriveApi driveApi = drive.DriveApi(authenticateClient);

    drive.FileList fileList = await driveApi.files.list(
      spaces: 'appDataFolder',
      $fields: 'files(id, name, modifiedTime, size)',
    );
    return (driveApi, fileList.files);
  } catch (e) {
    print("Error in getDriveFiles: $e");

    // Handle authentication errors
    String errorString = e.toString().toLowerCase();
    bool isAuthError = errorString.contains('401') ||
                      errorString.contains('unauthorized') ||
                      errorString.contains('unauthenticated') ||
                      errorString.contains('invalid_grant') ||
                      errorString.contains('access_denied') ||
                      errorString.contains('revoked');

    bool handled = false;
    if (isAuthError) {
      await signOutGoogle();
      handled = true;
    }

    if (handled) {
      // If it was an auth error and the user successfully re-authenticated, try again
      if (googleUser != null) {
        return await getDriveFiles();
      }
    } else {
      // For non-auth errors, show a generic error message
      openSnackbar(
        SnackbarMessage(
            title: e.toString(),
            icon: appStateSettings["outlinedIcons"]
                ? Icons.error_outlined
                : Icons.error_rounded),
      );
    }
  }
  return (null, null);
}

class BackupManagement extends StatefulWidget {
  const BackupManagement({
    Key? key,
    required this.isManaging,
    required this.isClientSync,
    this.hideDownloadButton = false,
  }) : super(key: key);

  final bool isManaging;
  final bool isClientSync;
  final bool hideDownloadButton;

  @override
  State<BackupManagement> createState() => _BackupManagementState();
}

class _BackupManagementState extends State<BackupManagement> {
  List<drive.File> filesState = [];
  List<int> deletedIndices = [];
  late drive.DriveApi driveApiState;
  UniqueKey dropDownKey = UniqueKey();
  bool isLoading = true;
  bool autoBackups = appStateSettings["autoBackups"];
  bool backupSync = appStateSettings["backupSync"];

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () async {
      (drive.DriveApi?, List<drive.File>?) result = await getDriveFiles();
      drive.DriveApi? driveApi = result.$1;
      List<drive.File>? files = result.$2;
      if (files == null || driveApi == null) {
        setState(() {
          filesState = [];
          isLoading = false;
        });
      } else {
        setState(() {
          filesState = files;
          driveApiState = driveApi;
          isLoading = false;
        });
        bottomSheetControllerGlobal.snapToExtent(0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isClientSync) {
      if (filesState.length > 0) {
        print(appStateSettings["devicesHaveBeenSynced"]);
        filesState =
            filesState.where((file) => isSyncBackupFile(file.name)).toList();
        updateSettings("devicesHaveBeenSynced", filesState.length,
            updateGlobalState: false);
      }
    } else {
      if (filesState.length > 0) {
        filesState =
            filesState.where((file) => !isSyncBackupFile(file.name)).toList();
        updateSettings("numBackups", filesState.length,
            updateGlobalState: false);
      }
    }
    Iterable<MapEntry<int, drive.File>> filesMap = filesState.asMap().entries;
    return PopupFramework(
      title: widget.isClientSync
          ? "devices".tr().capitalizeFirst
          : widget.isManaging
              ? "backups".tr()
              : "restore-a-backup".tr(),
      subtitle: widget.isClientSync
          ? "manage-syncing-info".tr()
          : widget.isManaging
              ? appStateSettings["backupLimit"].toString() +
                  " " +
                  "stored-backups".tr()
              : "overwrite-warning".tr(),
      child: Column(
        children: [
          widget.isClientSync && kIsWeb == false
              ? Row(
                  children: [
                    Expanded(
                      child: AboutInfoBox(
                        title: "web-app".tr(),
                        link: "",
                        color: appStateSettings["materialYou"]
                            ? Theme.of(context).colorScheme.secondaryContainer
                            : getColor(context, "lightDarkAccentHeavyLight"),
                        padding: EdgeInsetsDirectional.only(
                          start: 5,
                          end: 5,
                          bottom: 10,
                          top: 5,
                        ),
                      ),
                    ),
                  ],
                )
              : SizedBox.shrink(),
          widget.isManaging && widget.isClientSync == false
              ? SettingsContainerSwitch(
                  enableBorderRadius: true,
                  onSwitched: (value) {
                    updateSettings("autoBackups", value,
                        pagesNeedingRefresh: [], updateGlobalState: false);
                    setState(() {
                      autoBackups = value;
                    });
                  },
                  initialValue: appStateSettings["autoBackups"],
                  title: "auto-backups".tr(),
                  description: "auto-backups-description".tr(),
                  icon: appStateSettings["outlinedIcons"]
                      ? Icons.cloud_done_outlined
                      : Icons.cloud_done_rounded,
                )
              : SizedBox.shrink(),
          widget.isClientSync
              ? SettingsContainerSwitch(
                  enableBorderRadius: true,
                  onSwitched: (value) {
                    // Only update global is the sidebar is shown
                    updateSettings("backupSync", value,
                        pagesNeedingRefresh: [],
                        updateGlobalState: getIsFullScreen(context));
                    setState(() {
                      backupSync = value;
                    });
                    // Future.delayed(Duration(milliseconds: 100), () {
                    //   bottomSheetControllerGlobal.snapToExtent(0);
                    // });
                  },
                  initialValue: appStateSettings["backupSync"],
                  title: "sync-data".tr(),
                  description: "sync-data-description".tr(),
                  icon: appStateSettings["outlinedIcons"]
                      ? Icons.cloud_sync_outlined
                      : Icons.cloud_sync_rounded,
                )
              : SizedBox.shrink(),
          // Only allow sync on every change for web
          // Only on web, disabled automatically in initializeSettings if not web
          widget.isClientSync && kIsWeb
              ? AnimatedExpanded(
                  expand: backupSync,
                  child: SettingsContainerSwitch(
                    enableBorderRadius: true,
                    onSwitched: (value) {
                      updateSettings("syncEveryChange", value,
                          pagesNeedingRefresh: [], updateGlobalState: false);
                    },
                    initialValue: appStateSettings["syncEveryChange"],
                    title: "sync-every-change".tr(),
                    descriptionWithValue: (value) {
                      return value
                          ? "sync-every-change-description1".tr()
                          : "sync-every-change-description2".tr();
                    },
                    icon: appStateSettings["outlinedIcons"]
                        ? Icons.all_inbox_outlined
                        : Icons.all_inbox_rounded,
                  ),
                )
              : SizedBox.shrink(),
          widget.isManaging && widget.isClientSync == false
              ? AnimatedExpanded(
                  expand: autoBackups,
                  child: SettingsContainerDropdown(
                    enableBorderRadius: true,
                    items: ["1", "2", "3", "7", "10", "14"],
                    onChanged: (value) {
                      updateSettings("autoBackupsFrequency", int.parse(value),
                          pagesNeedingRefresh: [], updateGlobalState: false);
                    },
                    initial:
                        appStateSettings["autoBackupsFrequency"].toString(),
                    title: "backup-frequency".tr(),
                    description: "number-of-days".tr(),
                    icon: appStateSettings["outlinedIcons"]
                        ? Icons.event_repeat_outlined
                        : Icons.event_repeat_rounded,
                  ),
                )
              : SizedBox.shrink(),



          widget.isManaging &&
                  widget.isClientSync == false &&
                  appStateSettings["showBackupLimit"]
              ? SettingsContainerDropdown(
                  enableBorderRadius: true,
                  key: dropDownKey,
                  verticalPadding: 5,
                  title: "backup-limit".tr(),
                  icon: Icons.format_list_numbered_rtl_outlined,
                  initial: appStateSettings["backupLimit"].toString(),
                  items: ["10", "15", "20", "30"],
                  onChanged: (value) {
                    if (int.parse(value) < appStateSettings["backupLimit"]) {
                      openPopup(
                        context,
                        icon: appStateSettings["outlinedIcons"]
                            ? Icons.delete_outlined
                            : Icons.delete_rounded,
                        title: "change-limit".tr(),
                        description: "change-limit-warning".tr(),
                        onSubmit: () async {
                          updateSettings("backupLimit", int.parse(value),
                              updateGlobalState: false);
                          Navigator.pop(context);
                        },
                        onSubmitLabel: "change".tr(),
                        onCancel: () {
                          Navigator.pop(context);
                          setState(() {
                            dropDownKey = UniqueKey();
                          });
                        },
                        onCancelLabel: "cancel".tr(),
                      );
                    } else {
                      updateSettings("backupLimit", int.parse(value),
                          updateGlobalState: false);
                    }
                  },
                )
              : SizedBox.shrink(),
          if ((widget.isManaging == false && widget.isClientSync == false) ==
              false)
            SizedBox(height: 10),
          isLoading
              ? Column(
                  children: [
                    for (int i = 0;
                        i <
                            (widget.isClientSync
                                ? appStateSettings["devicesHaveBeenSynced"]
                                : appStateSettings["numBackups"]);
                        i++)
                      LoadingShimmerDriveFiles(
                          isManaging: widget.isManaging, i: i),
                  ],
                )
              : SizedBox.shrink(),
          ...filesMap
              .map(
                (MapEntry<int, drive.File> file) => AnimatedSizeSwitcher(
                  child: deletedIndices.contains(file.key)
                      ? Container(
                          key: ValueKey(1),
                        )
                      : Padding(
                          padding:
                              const EdgeInsetsDirectional.only(bottom: 8.0),
                          child: Tappable(
                            onTap: () async {
                              if (!widget.isManaging) {
                                final result = await openPopup(
                                  context,
                                  title: "load-backup".tr(),
                                  subtitle: getWordedDateShortMore(
                                        (file.value.modifiedTime ??
                                                DateTime.now())
                                            .toLocal(),
                                        includeTime: true,
                                        includeYear: true,
                                        showTodayTomorrow: false,
                                      ) +
                                      "\n" +
                                      getWordedTime(
                                          navigatorKey.currentContext?.locale
                                              .toString(),
                                          (file.value.modifiedTime ??
                                                  DateTime.now())
                                              .toLocal()),
                                  beforeDescriptionWidget: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                      top: 8,
                                      bottom: 5,
                                    ),
                                    child: CodeBlock(
                                        text: (file.value.name ?? "No name")),
                                  ),
                                  description: "load-backup-warning".tr(),
                                  icon: appStateSettings["outlinedIcons"]
                                      ? Icons.warning_outlined
                                      : Icons.warning_rounded,
                                  onSubmit: () async {
                                    Navigator.pop(context, true);
                                  },
                                  onSubmitLabel: "load".tr(),
                                  onCancelLabel: "cancel".tr(),
                                  onCancel: () {
                                    Navigator.pop(context);
                                  },
                                );
                                if (result == true)
                                  loadBackup(
                                      context, driveApiState, file.value);
                              }
                              // else {
                              //   await openPopup(
                              //     context,
                              //     title: "Backup Details",
                              //     description: (file.value.name ?? "") +
                              //         "\n" +
                              //         (file.value.size ?? "") +
                              //         "\n" +
                              //         (file.value.description ?? ""),
                              //     icon: appStateSettings["outlinedIcons"] ? Icons.warning_outlined : Icons.warning_rounded,
                              //     onSubmit: () async {
                              //       Navigator.pop(context, true);
                              //     },
                              //     onSubmitLabel: "Close",
                              //   );
                              // }
                            },
                            borderRadius: 15,
                            color: widget.isClientSync &&
                                    isCurrentDeviceSyncBackupFile(
                                        file.value.name)
                                ? Theme.of(context)
                                    .colorScheme
                                    .primary
                                    .withOpacity(0.4)
                                : appStateSettings["materialYou"]
                                    ? Theme.of(context)
                                        .colorScheme
                                        .secondaryContainer
                                    : getColor(
                                        context, "lightDarkAccentHeavyLight"),
                            child: Container(
                              padding: EdgeInsetsDirectional.symmetric(
                                  horizontal: 20, vertical: 15),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Row(
                                      children: [
                                        Icon(
                                          widget.isClientSync
                                              ? appStateSettings[
                                                      "outlinedIcons"]
                                                  ? Icons.devices_outlined
                                                  : Icons.devices_rounded
                                              : appStateSettings[
                                                      "outlinedIcons"]
                                                  ? Icons.description_outlined
                                                  : Icons.description_rounded,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .secondary,
                                          size: 30,
                                        ),
                                        SizedBox(
                                            width:
                                                widget.isClientSync ? 17 : 13),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              TextFont(
                                                text: getTimeAgo(
                                                  (file.value.modifiedTime ??
                                                          DateTime.now())
                                                      .toLocal(),
                                                ).capitalizeFirst,
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                                maxLines: 2,
                                              ),
                                              TextFont(
                                                text: (isSyncBackupFile(
                                                        file.value.name)
                                                    ? getDeviceFromSyncBackupFileName(
                                                            file.value.name) +
                                                        " " +
                                                        "sync"
                                                    : file.value.name ??
                                                        "No name"),
                                                fontSize: 14,
                                                maxLines: 2,
                                              ),
                                              // isSyncBackupFile(
                                              //         file.value.name)
                                              //     ? Padding(
                                              //         padding:
                                              //             const EdgeInsetsDirectional
                                              //                 .only(top: 3),
                                              //         child: TextFont(
                                              //           text:
                                              //               file.value.name ??
                                              //                   "",
                                              //           fontSize: 11,
                                              //           maxLines: 2,
                                              //         ),
                                              //       )
                                              //     : SizedBox.shrink()
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  widget.isManaging
                                      ? Row(
                                          children: [
                                            widget.hideDownloadButton
                                                ? SizedBox.shrink()
                                                : Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .only(
                                                      start: 8.0,
                                                    ),
                                                    child: Builder(
                                                        builder: (boxContext) {
                                                      return ButtonIcon(
                                                        color: appStateSettings[
                                                                "materialYou"]
                                                            ? Theme.of(context)
                                                                .colorScheme
                                                                .onSecondaryContainer
                                                                .withOpacity(
                                                                    0.08)
                                                            : getColor(context,
                                                                    "lightDarkAccentHeavy")
                                                                .withOpacity(
                                                                    0.7),
                                                        onTap: () {
                                                          saveDriveFileToDevice(
                                                            boxContext:
                                                                boxContext,
                                                            driveApi:
                                                                driveApiState,
                                                            fileToSave:
                                                                file.value,
                                                          );
                                                        },
                                                        icon: Icons
                                                            .download_rounded,
                                                      );
                                                    }),
                                                  ),
                                            Padding(
                                              padding:
                                                  const EdgeInsetsDirectional
                                                      .only(start: 5),
                                              child: ButtonIcon(
                                                color: appStateSettings[
                                                        "materialYou"]
                                                    ? Theme.of(context)
                                                        .colorScheme
                                                        .onSecondaryContainer
                                                        .withOpacity(0.08)
                                                    : getColor(context,
                                                            "lightDarkAccentHeavy")
                                                        .withOpacity(0.7),
                                                onTap: () {
                                                  openPopup(
                                                    context,
                                                    icon: appStateSettings[
                                                            "outlinedIcons"]
                                                        ? Icons.delete_outlined
                                                        : Icons.delete_rounded,
                                                    title: "delete-backup".tr(),
                                                    subtitle:
                                                        getWordedDateShortMore(
                                                              (file.value.modifiedTime ??
                                                                      DateTime
                                                                          .now())
                                                                  .toLocal(),
                                                              includeTime: true,
                                                              includeYear: true,
                                                              showTodayTomorrow:
                                                                  false,
                                                            ) +
                                                            "\n" +
                                                            getWordedTime(
                                                                navigatorKey
                                                                    .currentContext
                                                                    ?.locale
                                                                    .toString(),
                                                                (file.value.modifiedTime ??
                                                                        DateTime
                                                                            .now())
                                                                    .toLocal()),
                                                    beforeDescriptionWidget:
                                                        Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .only(
                                                        top: 8,
                                                        bottom: 5,
                                                      ),
                                                      child: CodeBlock(
                                                        text: (file.value
                                                                    .name ??
                                                                "No name") +
                                                            "\n" +
                                                            convertBytesToMB(file
                                                                        .value
                                                                        .size ??
                                                                    "0")
                                                                .toStringAsFixed(
                                                                    2) +
                                                            " MB",
                                                      ),
                                                    ),
                                                    description: (widget
                                                            .isClientSync
                                                        ? "delete-sync-backup-warning"
                                                            .tr()
                                                        : null),
                                                    onSubmit: () async {
                                                      Navigator.pop(context);
                                                      loadingIndeterminateKey
                                                          .currentState!
                                                          .setVisibility(true);
                                                      await deleteBackup(
                                                          driveApiState,
                                                          file.value.id ?? "");
                                                      openSnackbar(
                                                        SnackbarMessage(
                                                            title:
                                                                "deleted-backup"
                                                                    .tr(),
                                                            description: (file
                                                                    .value
                                                                    .name ??
                                                                "No name"),
                                                            icon: Icons
                                                                .delete_rounded),
                                                      );
                                                      setState(() {
                                                        deletedIndices
                                                            .add(file.key);
                                                      });
                                                      // bottomSheetControllerGlobal
                                                      //     .snapToExtent(0);
                                                      if (widget.isClientSync)
                                                        updateSettings(
                                                            "devicesHaveBeenSynced",
                                                            appStateSettings[
                                                                    "devicesHaveBeenSynced"] -
                                                                1,
                                                            updateGlobalState:
                                                                false);
                                                      if (widget.isManaging) {
                                                        updateSettings(
                                                            "numBackups",
                                                            appStateSettings[
                                                                    "numBackups"] -
                                                                1,
                                                            updateGlobalState:
                                                                false);
                                                      }
                                                      loadingIndeterminateKey
                                                          .currentState!
                                                          .setVisibility(false);
                                                    },
                                                    onSubmitLabel:
                                                        "delete".tr(),
                                                    onCancel: () {
                                                      Navigator.pop(context);
                                                    },
                                                    onCancelLabel:
                                                        "cancel".tr(),
                                                  );
                                                },
                                                icon: appStateSettings[
                                                        "outlinedIcons"]
                                                    ? Icons.close_outlined
                                                    : Icons.close_rounded,
                                              ),
                                            ),
                                          ],
                                        )
                                      : SizedBox.shrink(),
                                ],
                              ),
                            ),
                          ),
                        ),
                ),
              )
              .toList(),
        ],
      ),
    );
  }
}

double convertBytesToMB(String bytesString) {
  try {
    int bytes = int.parse(bytesString);
    double megabytes = bytes / (1024 * 1024);
    return megabytes;
  } catch (e) {
    print("Error parsing bytes string: $e");
    return 0.0; // or throw an exception, depending on your requirements
  }
}

class LoadingShimmerDriveFiles extends StatelessWidget {
  const LoadingShimmerDriveFiles({
    Key? key,
    required this.isManaging,
    required this.i,
  }) : super(key: key);

  final bool isManaging;
  final int i;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      period:
          Duration(milliseconds: (1000 + randomDouble[i % 10] * 520).toInt()),
      baseColor: appStateSettings["materialYou"]
          ? Theme.of(context).colorScheme.secondaryContainer
          : getColor(context, "lightDarkAccentHeavyLight"),
      highlightColor: appStateSettings["materialYou"]
          ? Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.2)
          : getColor(context, "lightDarkAccentHeavy").withAlpha(20),
      child: Padding(
        padding: const EdgeInsetsDirectional.only(bottom: 8.0),
        child: Tappable(
          onTap: () {},
          borderRadius: 15,
          color: appStateSettings["materialYou"]
              ? Theme.of(context)
                  .colorScheme
                  .secondaryContainer
                  .withOpacity(0.5)
              : getColor(context, "lightDarkAccentHeavy").withOpacity(0.5),
          child: Container(
              padding:
                  EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 15),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          appStateSettings["outlinedIcons"]
                              ? Icons.description_outlined
                              : Icons.description_rounded,
                          color: Theme.of(context).colorScheme.secondary,
                          size: 30,
                        ),
                        SizedBox(width: 13),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadiusDirectional.all(
                                      Radius.circular(5)),
                                  color: Colors.white,
                                ),
                                height: 20,
                                width: 70 + randomDouble[i % 10] * 120 + 13,
                              ),
                              SizedBox(height: 6),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadiusDirectional.all(
                                      Radius.circular(5)),
                                  color: Colors.white,
                                ),
                                height: 14,
                                width: 90 + randomDouble[i % 10] * 120,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 13),
                  isManaging
                      ? Row(
                          children: [
                            ButtonIcon(
                                onTap: () {},
                                icon: appStateSettings["outlinedIcons"]
                                    ? Icons.close_outlined
                                    : Icons.close_rounded),
                            SizedBox(width: 5),
                            ButtonIcon(
                                onTap: () {},
                                icon: appStateSettings["outlinedIcons"]
                                    ? Icons.close_outlined
                                    : Icons.close_rounded),
                          ],
                        )
                      : SizedBox.shrink(),
                ],
              )),
        ),
      ),
    );
  }
}

Future<bool> saveDriveFileToDevice({
  required BuildContext boxContext,
  required drive.DriveApi driveApi,
  required drive.File fileToSave,
}) async {
  List<int> dataStore = [];
  dynamic response = await driveApi.files
      .get(fileToSave.id!, downloadOptions: drive.DownloadOptions.fullMedia);
  await for (var data in response.stream) {
    dataStore.insertAll(dataStore.length, data);
  }
  String fileName = "cashew-" +
      ((fileToSave.name ?? "") +
              (fileToSave.modifiedTime ?? DateTime.now()).toString())
          .replaceAll(".sqlite", "")
          .replaceAll(".", "-")
          .replaceAll("-", "-")
          .replaceAll(" ", "-")
          .replaceAll(":", "-") +
      ".sql";

  return await saveFile(
    boxContext: boxContext,
    dataStore: dataStore,
    dataString: null,
    fileName: fileName,
    successMessage: "backup-downloaded-success".tr(),
    errorMessage: "error-downloading".tr(),
  );
}

bool openBackupReminderPopupCheck(BuildContext context) {
  if ((appStateSettings["currentUserEmail"] == null ||
          appStateSettings["currentUserEmail"] == "") &&
      ((appStateSettings["numLogins"] + 1) % 7 == 0) &&
      appStateSettings["canShowBackupReminderPopup"] == true) {
    openPopup(
      context,
      icon: MoreIcons.google_drive,
      iconScale: 0.9,
      title: "backup-your-data-reminder".tr(),
      description: "backup-your-data-reminder-description".tr() +
          " " +
          "google-drive".tr(),
      onSubmitLabel: "backup".tr().capitalizeFirst,
      onSubmit: () async {
        Navigator.pop(context);
        await signInAndSync(context, next: () {});
      },
      onCancelLabel: "never".tr().capitalizeFirst,
      onCancel: () {
        Navigator.pop(context);
        updateSettings("canShowBackupReminderPopup", false,
            updateGlobalState: false);
      },
      onExtraLabel: "later".tr().capitalizeFirst,
      onExtra: () {
        Navigator.pop(context);
      },
    );
    return true;
  }
  return false;
}
