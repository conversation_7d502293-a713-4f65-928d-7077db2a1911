<lint-module
    format="1"
    dir="D:\App\Android Studio\Projects\budget\android\app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.0"
    buildFolder="D:\App\Android Studio\Projects\budget\build\app"
    bootClassPath="D:\App\Android Studio\AndoirdSDK\platforms\android-35\android.jar;D:\App\Android Studio\AndoirdSDK\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
