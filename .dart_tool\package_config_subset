reorderable_grid_view
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/reorderable_grid_view-ad913fc157323c3dca9e05628e575f54b3c2b8b6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/reorderable_grid_view-ad913fc157323c3dca9e05628e575f54b3c2b8b6/lib/
_discoveryapis_commons
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_discoveryapis_commons-1.0.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_discoveryapis_commons-1.0.7/lib/
_fe_analyzer_shared
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib/
_flutterfire_internals
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.53/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.53/lib/
analyzer
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.11.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.11.0/lib/
analyzer_plugin
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer_plugin-0.11.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer_plugin-0.11.3/lib/
animated_fractionally_sized_box
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/animated_fractionally_sized_box-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/animated_fractionally_sized_box-2.0.1/lib/
animations
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/animations-2.0.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/animations-2.0.11/lib/
app_links
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_links-6.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_links-6.4.0/lib/
app_links_linux
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_linux-1.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_linux-1.0.3/lib/
app_links_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_platform_interface-2.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/
app_links_web
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_web-1.0.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_web-1.0.4/lib/
app_settings
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_settings-5.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_settings-5.2.0/lib/
archive
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/lib/
auto_size_text
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text-3.0.0/lib/
boolean_selector
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
build
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.4.2/lib/
build_config
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.2/lib/
build_daemon
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_daemon-4.0.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_daemon-4.0.4/lib/
build_resolvers
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_resolvers-2.4.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_resolvers-2.4.4/lib/
build_runner
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner-2.4.15/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner-2.4.15/lib/
build_runner_core
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner_core-8.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner_core-8.0.0/lib/
built_collection
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_value-8.9.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_value-8.9.5/lib/
carousel_slider
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/carousel_slider-5.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/carousel_slider-5.0.0/lib/
chalkdart
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/chalkdart-2.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/chalkdart-2.4.0/lib/
characters
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/lib/
charcode
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/charcode-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/charcode-1.4.0/lib/
checked_yaml
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
cli_util
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cli_util-0.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cli_util-0.4.2/lib/
clock
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/lib/
cloud_firestore
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.5/lib/
cloud_firestore_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.5/lib/
cloud_firestore_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.5/lib/
code_builder
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/code_builder-4.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/code_builder-4.10.1/lib/
collection
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/lib/
confetti
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/confetti-0.8.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/confetti-0.8.0/lib/
connectivity_plus
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-5.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-5.0.2/lib/
connectivity_plus_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/lib/
convert
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/lib/
cross_file
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2/lib/
csv
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csv-6.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csv-6.0.0/lib/
dart_style
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-2.3.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-2.3.8/lib/
dbus
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/lib/
device_frame
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_frame-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_frame-1.2.0/lib/
device_info_plus
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-11.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-11.4.0/lib/
device_info_plus_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/
device_preview
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_preview-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_preview-1.2.0/lib/
drift
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/drift-2.22.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/drift-2.22.1/lib/
drift_dev
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/drift_dev-2.22.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/drift_dev-2.22.1/lib/
easy_localization
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/easy_localization-3.0.7+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/easy_localization-3.0.7+1/lib/
easy_logger
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/easy_logger-0.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/easy_logger-0.0.2/lib/
equatable
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7/lib/
expandable_page_view
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/expandable_page_view-1.0.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/expandable_page_view-1.0.17/lib/
fake_async
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/lib/
file_picker
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-10.1.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-10.1.7/lib/
file_selector_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib/
file_selector_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_auth
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.5.1/lib/
firebase_auth_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.6.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.6.1/lib/
firebase_auth_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.14.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.14.1/lib/
firebase_core
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.12.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.12.1/lib/
firebase_core_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/
firebase_core_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.21.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.21.1/lib/
fixnum
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib/
fl_chart
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fl_chart-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fl_chart-1.0.0/lib/
flutter_charset_detector
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector-5.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector-5.0.0/lib/
flutter_charset_detector_android
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_android-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_android-3.0.0/lib/
flutter_charset_detector_darwin
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_darwin-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_darwin-1.1.0/lib/
flutter_charset_detector_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_platform_interface-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_platform_interface-1.1.0/lib/
flutter_charset_detector_web
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_web-2.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_web-2.0.0/lib/
flutter_colorpicker
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_colorpicker-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_colorpicker-1.1.0/lib/
flutter_displaymode
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_displaymode-0.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_displaymode-0.6.0/lib/
flutter_haptic_feedback
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_haptic_feedback-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_haptic_feedback-1.0.1/lib/
flutter_launcher_icons
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/lib/
flutter_lazy_indexed_stack
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lazy_indexed_stack-0.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lazy_indexed_stack-0.0.6/lib/
flutter_local_notifications
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications-19.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications-19.2.0/lib/
flutter_local_notifications_linux
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/lib/
flutter_local_notifications_platform_interface
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.0.0/lib/
flutter_local_notifications_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.27/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.27/lib/
flutter_secure_storage
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/
flutter_secure_storage_linux
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib/
flutter_secure_storage_macos
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib/
flutter_secure_storage_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/
flutter_secure_storage_web
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/
flutter_secure_storage_windows
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/
flutter_signature_pad
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_signature_pad-3.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_signature_pad-3.0.1/lib/
flutter_staggered_grid_view
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/lib/
flutter_sticky_header
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_sticky_header-0.8.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_sticky_header-0.8.0/lib/
flutter_timezone
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_timezone-4.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_timezone-4.1.0/lib/
freezed_annotation
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/freezed_annotation-2.4.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/
frontend_server_client
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
glob
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/lib/
google_identity_services_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3/lib/
google_sign_in
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-6.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-6.3.0/lib/
google_sign_in_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-6.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-6.2.0/lib/
google_sign_in_ios
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-5.8.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-5.8.1/lib/
google_sign_in_platform_interface
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib/
google_sign_in_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/lib/
googleapis
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/googleapis-14.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/googleapis-14.0.0/lib/
gradient_borders
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gradient_borders-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gradient_borders-1.0.1/lib/
graphs
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2/lib/
gtk
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gtk-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gtk-2.1.0/lib/
home_widget
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/home_widget-0.7.0+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/home_widget-0.7.0+1/lib/
html
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.5/lib/
http
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.3.0/lib/
http_multi_server
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4/lib/
image_picker
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+22/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+22/lib/
image_picker_for_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
in_app_purchase
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase-3.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase-3.2.1/lib/
in_app_purchase_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_android-0.4.0+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_android-0.4.0+1/lib/
in_app_purchase_platform_interface
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_platform_interface-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_platform_interface-1.4.0/lib/
in_app_purchase_storekit
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_storekit-0.3.21/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_storekit-0.3.21/lib/
in_app_review
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review-2.0.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review-2.0.10/lib/
in_app_review_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review_platform_interface-2.0.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review_platform_interface-2.0.5/lib/
intl
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2/lib/
io
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5/lib/
js
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
local_auth
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth-2.3.0/lib/
local_auth_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_android-1.0.48/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_android-1.0.48/lib/
local_auth_darwin
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_darwin-1.4.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_darwin-1.4.3/lib/
local_auth_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/
local_auth_windows
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_windows-1.0.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/
logging
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/lib/
macros
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/macros-0.1.3-main.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/macros-0.1.3-main.0/lib/
matcher
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
material_symbols_icons
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_symbols_icons-4.2811.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_symbols_icons-4.2811.0/lib/
math_expressions
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/math_expressions-2.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/math_expressions-2.7.0/lib/
meta
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0/lib/
nested
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0/lib/
notification_listener_service
2.16
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/notification_listener_service-0.3.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/notification_listener_service-0.3.4/lib/
package_config
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0/lib/
package_info_plus
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0/lib/
package_info_plus_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/
path
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/lib/
path_provider
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.16/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.16/lib/
path_provider_foundation
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
pausable_timer
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pausable_timer-3.1.0+3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pausable_timer-3.1.0+3/lib/
permission_handler
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1/lib/
posix
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.2/lib/
provider
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.4/lib/
pub_semver
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0/lib/
quick_actions
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions-1.1.0/lib/
quick_actions_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_android-1.0.20/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_android-1.0.20/lib/
quick_actions_ios
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_ios-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_ios-1.2.0/lib/
quick_actions_platform_interface
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_platform_interface-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_platform_interface-1.1.0/lib/
recaptcha_enterprise_flutter
2.16
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/recaptcha_enterprise_flutter-18.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/recaptcha_enterprise_flutter-18.7.0/lib/
recase
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/recase-4.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/recase-4.1.0/lib/
sa3_liquid
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sa3_liquid-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sa3_liquid-1.0.1/lib/
scrollable_positioned_list
2.15
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib/
share_plus
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-10.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-10.1.4/lib/
share_plus_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/lib/
shared_preferences
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.8/lib/
shared_preferences_foundation
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2/lib/
shelf_web_socket
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib/
shimmer
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shimmer-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shimmer-3.0.0/lib/
simple_animations
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/simple_animations-5.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/simple_animations-5.1.0/lib/
sliver_tools
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sliver_tools-0.2.12/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sliver_tools-0.2.12/lib/
source_gen
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0/lib/
source_span
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqlite3
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3-2.7.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3-2.7.5/lib/
sqlite3_flutter_libs
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.32/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.32/lib/
sqlparser
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqlparser-0.40.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqlparser-0.40.0/lib/
stack_trace
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib/
sticky_and_expandable_list
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sticky_and_expandable_list-1.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sticky_and_expandable_list-1.1.3/lib/
stream_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib/
supercharged
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged-2.1.1/lib/
supercharged_dart
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged_dart-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged_dart-2.1.1/lib/
system_theme
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/system_theme-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/system_theme-3.1.2/lib/
system_theme_web
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/system_theme_web-0.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/system_theme_web-0.0.3/lib/
term_glyph
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/lib/
timer_builder
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timer_builder-2.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timer_builder-2.0.0/lib/
timezone
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timezone-0.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timezone-0.10.1/lib/
timing
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.2/lib/
transparent_image
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/transparent_image-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/transparent_image-2.0.1/lib/
typed_data
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib/
universal_html
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_html-2.2.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_html-2.2.4/lib/
universal_io
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_io-2.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_io-2.2.2/lib/
url_launcher
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1/lib/
url_launcher_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.15/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.15/lib/
url_launcher_ios
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.2/lib/
url_launcher_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.0/lib/
url_launcher_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib/
value_layout_builder
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/value_layout_builder-0.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/value_layout_builder-0.5.0/lib/
vector_math
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib/
visibility_detector
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/visibility_detector-0.4.0+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/visibility_detector-0.4.0+2/lib/
vm_service
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/lib/
watcher
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1/lib/
web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-0.1.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-0.1.6/lib/
web_socket_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.2/lib/
win32
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.12.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.12.0/lib/
win32_registry
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-2.1.0/lib/
xdg_directories
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/lib/
_macros
3.5
file:///D:/App/Android%20Studio/FlutterSDK/bin/cache/dart-sdk/pkg/_macros/
file:///D:/App/Android%20Studio/FlutterSDK/bin/cache/dart-sdk/pkg/_macros/lib/
sky_engine
3.7
file:///D:/App/Android%20Studio/FlutterSDK/bin/cache/pkg/sky_engine/
file:///D:/App/Android%20Studio/FlutterSDK/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///D:/App/Android%20Studio/FlutterSDK/packages/flutter/
file:///D:/App/Android%20Studio/FlutterSDK/packages/flutter/lib/
flutter_localizations
3.7
file:///D:/App/Android%20Studio/FlutterSDK/packages/flutter_localizations/
file:///D:/App/Android%20Studio/FlutterSDK/packages/flutter_localizations/lib/
flutter_test
3.7
file:///D:/App/Android%20Studio/FlutterSDK/packages/flutter_test/
file:///D:/App/Android%20Studio/FlutterSDK/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///D:/App/Android%20Studio/FlutterSDK/packages/flutter_web_plugins/
file:///D:/App/Android%20Studio/FlutterSDK/packages/flutter_web_plugins/lib/
budget
3.0
file:///D:/App/Android%20Studio/Projects/budget/
file:///D:/App/Android%20Studio/Projects/budget/lib/
implicitly_animated_reorderable_list
2.12
file:///D:/App/Android%20Studio/Projects/budget/packages/implicitly_animated_reorderable_list-0.4.2-modified/
file:///D:/App/Android%20Studio/Projects/budget/packages/implicitly_animated_reorderable_list-0.4.2-modified/lib/
sliding_sheet
2.12
file:///D:/App/Android%20Studio/Projects/budget/packages/sliding_sheet-0.5.2-modified/
file:///D:/App/Android%20Studio/Projects/budget/packages/sliding_sheet-0.5.2-modified/lib/
2
