[{"merged": "dev.fluttercommunity.plus.share.share_plus-release-5:/drawable-xxhdpi-v4/ic_call_answer_video.png", "source": "dev.fluttercommunity.plus.share.share_plus-core-1.13.1-21:/drawable-xxhdpi-v4/ic_call_answer_video.png"}, {"merged": "dev.fluttercommunity.plus.share.share_plus-release-5:/drawable-xxhdpi-v4/ic_call_decline_low.png", "source": "dev.fluttercommunity.plus.share.share_plus-core-1.13.1-21:/drawable-xxhdpi-v4/ic_call_decline_low.png"}, {"merged": "dev.fluttercommunity.plus.share.share_plus-release-5:/drawable-xxhdpi-v4/ic_call_answer_low.png", "source": "dev.fluttercommunity.plus.share.share_plus-core-1.13.1-21:/drawable-xxhdpi-v4/ic_call_answer_low.png"}, {"merged": "dev.fluttercommunity.plus.share.share_plus-release-5:/drawable-xxhdpi-v4/ic_call_answer.png", "source": "dev.fluttercommunity.plus.share.share_plus-core-1.13.1-21:/drawable-xxhdpi-v4/ic_call_answer.png"}, {"merged": "dev.fluttercommunity.plus.share.share_plus-release-5:/drawable-xxhdpi-v4/ic_call_decline.png", "source": "dev.fluttercommunity.plus.share.share_plus-core-1.13.1-21:/drawable-xxhdpi-v4/ic_call_decline.png"}, {"merged": "dev.fluttercommunity.plus.share.share_plus-release-5:/drawable-xxhdpi-v4/ic_call_answer_video_low.png", "source": "dev.fluttercommunity.plus.share.share_plus-core-1.13.1-21:/drawable-xxhdpi-v4/ic_call_answer_video_low.png"}]