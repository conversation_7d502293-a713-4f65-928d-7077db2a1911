#!/usr/bin/env python3
"""
Duplicate Removal <PERSON> for Translation CSV
==========================================

This script detects and removes duplicate entries from the English language column
in the translations.csv file while preserving all translation strings.

Features:
- Detects duplicate English text entries
- Removes duplicate rows (keeps first occurrence)
- Preserves all existing translations
- Creates backup before modification
- Provides detailed report of removed duplicates

Usage:
    python remove_duplicates.py
"""

import csv
import os
import shutil
from datetime import datetime
from collections import defaultdict


def create_backup(file_path):
    """Create a backup of the original file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    shutil.copy2(file_path, backup_path)
    print(f"✅ Backup created: {backup_path}")
    return backup_path


def detect_duplicates(file_path):
    """Detect duplicate entries based on English text"""
    print(f"🔍 Analyzing file: {file_path}")
    
    duplicates = defaultdict(list)
    unique_entries = {}
    rows_to_keep = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
        if not rows:
            print("❌ File is empty")
            return [], []
            
        # Get header
        header = rows[0]
        print(f"📋 Header: {header}")
        
        # Assume English is in column 1 (index 1)
        english_col_index = 1
        if len(header) > 1:
            print(f"🇺🇸 English column (index {english_col_index}): {header[english_col_index]}")
        
        # Process each row
        for row_index, row in enumerate(rows):
            if row_index == 0:  # Skip header
                rows_to_keep.append(row)
                continue
                
            if len(row) <= english_col_index:
                print(f"⚠️  Row {row_index + 1} has insufficient columns, skipping")
                continue
                
            key = row[0].strip()  # Translation key
            english_text = row[english_col_index].strip()  # English text
            
            # Skip empty entries
            if not key or not english_text:
                rows_to_keep.append(row)
                continue
            
            # Create a unique identifier (key + english text)
            unique_id = f"{key}|{english_text}"
            
            if unique_id in unique_entries:
                # This is a duplicate
                duplicates[unique_id].append({
                    'row_index': row_index + 1,
                    'key': key,
                    'english_text': english_text,
                    'row_data': row
                })
                print(f"🔄 Duplicate found at row {row_index + 1}: '{key}' -> '{english_text[:50]}...'")
            else:
                # This is unique, keep it
                unique_entries[unique_id] = {
                    'row_index': row_index + 1,
                    'key': key,
                    'english_text': english_text,
                    'row_data': row
                }
                rows_to_keep.append(row)
        
        return rows_to_keep, duplicates
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return [], []


def save_cleaned_file(file_path, cleaned_rows):
    """Save the cleaned data to the file"""
    try:
        with open(file_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(cleaned_rows)
        print(f"✅ Cleaned file saved: {file_path}")
        return True
    except Exception as e:
        print(f"❌ Error saving file: {e}")
        return False


def generate_report(duplicates, original_count, final_count):
    """Generate a detailed report of the duplicate removal process"""
    print("\n" + "="*60)
    print("📊 DUPLICATE REMOVAL REPORT")
    print("="*60)
    
    print(f"📈 Original entries: {original_count}")
    print(f"📉 Final entries: {final_count}")
    print(f"🗑️  Duplicates removed: {original_count - final_count}")
    print(f"💾 Space saved: {len(duplicates)} duplicate groups")
    
    if duplicates:
        print(f"\n🔍 Detailed duplicate analysis:")
        for i, (unique_id, duplicate_list) in enumerate(duplicates.items(), 1):
            key, english_text = unique_id.split('|', 1)
            print(f"\n{i}. Key: '{key}'")
            print(f"   English: '{english_text[:80]}{'...' if len(english_text) > 80 else ''}'")
            print(f"   Duplicate rows removed: {[dup['row_index'] for dup in duplicate_list]}")
    
    print("\n✅ Duplicate removal completed successfully!")


def main():
    """Main function to remove duplicates from translations.csv"""
    file_path = "assets\translations\translations.csv"
    
    print("🚀 Starting Duplicate Removal Tool")
    print("="*50)
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    # Create backup
    backup_path = create_backup(file_path)
    
    # Detect and remove duplicates
    cleaned_rows, duplicates = detect_duplicates(file_path)
    
    if not cleaned_rows:
        print("❌ No data to process")
        return
    
    original_count = len(cleaned_rows) + sum(len(dup_list) for dup_list in duplicates.values())
    final_count = len(cleaned_rows)
    
    if not duplicates:
        print("🎉 No duplicates found! File is already clean.")
        # Remove backup since no changes were made
        os.remove(backup_path)
        print(f"🗑️  Backup removed: {backup_path}")
        return
    
    # Save cleaned file
    if save_cleaned_file(file_path, cleaned_rows):
        generate_report(duplicates, original_count, final_count)
    else:
        print("❌ Failed to save cleaned file")
        # Restore from backup
        shutil.copy2(backup_path, file_path)
        print(f"🔄 File restored from backup: {backup_path}")


if __name__ == "__main__":
    main()
