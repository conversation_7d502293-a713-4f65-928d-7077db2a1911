name: budget
description: A budget and financial tracking application designed for you

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 5.4.3+416

environment:
  sdk: ">= 3.0.0"

dependencies:
  flutter:
    sdk: flutter
  simple_animations: ^5.0.2
  sa3_liquid: ^1.0.1
  intl: ^0.18.1
  animations: ^2.0.11
  flutter_sticky_header: ^0.6.5
  sticky_and_expandable_list: ^1.1.3
  fl_chart: ^0.68.0
  # database dependencies
  drift: ^2.14.0
  sqlite3_flutter_libs: ^0.5.0
  path_provider: ^2.1.3
  path: ^1.9.0
  math_expressions: ^2.5.0
  share_plus: ^10.0.0
  google_sign_in: ^6.2.1
  googleapis: ^13.1.0
  universal_html: ^2.2.4
  flutter_charset_detector: ^1.0.2
  file_picker:
    git:
      # This fork implements https://github.com/miguelpruivo/flutter_file_picker/issues/1404
      url: https://github.com/melWiss/flutter_file_picker.git
  auto_size_text: ^3.0.0
  shared_preferences: ^2.2.3
  csv: ^6.0.0
  sliding_sheet:
    path: ./packages/sliding_sheet-0.5.2-modified/
  implicitly_animated_reorderable_list:
    path: ./packages/implicitly_animated_reorderable_list-0.4.2-modified/
  shimmer: ^3.0.0
  carousel_slider: ^4.2.1
  # flutter_notification_listener: ^1.3.2
  # installed_apps: ^1.3.1
  url_launcher: ^6.2.6
  flutter_launcher_icons: ^0.10.0
  package_info_plus: ^8.0.0
  # mrx_charts: ^0.1.3
  transparent_image: ^2.0.0
  pausable_timer: ^3.1.0+3
  animated_fractionally_sized_box: ^2.0.1
  flutter_local_notifications: ^17.2.1+1
  system_theme: ^3.0.0
  firebase_core: ^3.2.0
  firebase_auth: ^5.1.2
  cloud_firestore: ^5.1.0
  firebase_core_web: ^2.17.3
  firebase_auth_web: ^5.12.4
  recaptcha_enterprise_flutter: ^18.5.1
  sliver_tools: ^0.2.12
  reorderable_grid_view:
    git:
      url: https://github.com/jameskokoska/reorderable_grid_view
  local_auth: ^2.2.0
  timer_builder: ^2.0.0
  device_info_plus: ^10.1.0
  flutter_lazy_indexed_stack: ^0.0.6 
  app_settings: ^5.1.1
  provider: ^6.1.2
  in_app_review: ^2.0.9 
  flutter_timezone: ^1.0.8
  device_preview: ^1.1.0 
  scrollable_positioned_list: ^0.3.8
  easy_localization: ^3.0.7
  # simple_shadow: ^0.3.1
  quick_actions: ^1.0.7
  # feature_discovery: ^0.14.1
  gradient_borders: ^1.0.1
  in_app_purchase: ^3.2.0
  visibility_detector: ^0.4.0+2
  confetti: ^0.7.0
  image_picker: ^1.1.2
  flutter_displaymode: ^0.6.0
  notification_listener_service: ^0.3.3
  home_widget: ^0.5.0
  material_symbols_icons: ^4.2768.0
  app_links: ^6.1.4
  expandable_page_view: ^1.0.17
  flutter_staggered_grid_view: ^0.7.0
  flutter_colorpicker: ^1.1.0
  
# flutter pub run flutter_launcher_icons:main
flutter_icons:
  android: true
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icon/icon-web.png"
    background_color: "#ffffff"
    theme_color: "#5f85c2"

dev_dependencies:
  drift_dev: ^2.14.0
  build_runner: ^2.4.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  assets:
    - assets/fonts/
    - assets/categories/
    - assets/icon/
    - assets/landing/
    - assets/images/
    - assets/static/
    - assets/icons/fun/
    - assets/translations/generated/
    - assets/static/generated/
    - assets/static/generated/currencies.json
    - assets/static/language-names.json

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

  fonts:
    - family: Avenir
      fonts:
        - asset: assets/fonts/AvenirLTStd-Roman.otf
        - asset: assets/fonts/AvenirLTStd-Black.otf
          weight: 700
    # Inter is the font family fallback
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
    - family: DMSans
      fonts:
        - asset: assets/fonts/DMSans-Regular.ttf
        - asset: assets/fonts/DMSans-Bold.ttf
          weight: 700
    - family: Metropolis
      fonts:
        - asset: assets/fonts/Metropolis-Regular.otf
        - asset: assets/fonts/Metropolis-Bold.otf
          weight: 700
    - family: RobotoCondensed
      fonts:
        - asset: assets/fonts/RobotoCondensed-Regular.ttf
        - asset: assets/fonts/RobotoCondensed-Bold.ttf
          weight: 700
    - family: Inconsolata
      fonts:
        - asset: assets/fonts/Inconsolata-Regular.ttf
        - asset: assets/fonts/Inconsolata-Bold.ttf
          weight: 700
    - family: Icons
      fonts:
        - asset: assets/icons/Icons.ttf
