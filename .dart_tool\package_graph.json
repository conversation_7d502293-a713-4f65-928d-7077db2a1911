{"roots": ["budget"], "packages": [{"name": "budget", "version": "5.4.3+416", "dependencies": ["animated_fractionally_sized_box", "animations", "app_links", "app_settings", "auto_size_text", "carousel_slider", "cloud_firestore", "confetti", "connectivity_plus", "csv", "device_info_plus", "device_preview", "drift", "easy_localization", "expandable_page_view", "file_picker", "firebase_auth", "firebase_auth_web", "firebase_core", "firebase_core_web", "fl_chart", "flutter", "flutter_charset_detector", "flutter_colorpicker", "flutter_displaymode", "flutter_haptic_feedback", "flutter_launcher_icons", "flutter_lazy_indexed_stack", "flutter_local_notifications", "flutter_secure_storage", "flutter_signature_pad", "flutter_staggered_grid_view", "flutter_sticky_header", "flutter_timezone", "google_sign_in", "googlea<PERSON>", "gradient_borders", "home_widget", "image_picker", "implicitly_animated_reorderable_list", "in_app_purchase", "in_app_review", "intl", "local_auth", "material_symbols_icons", "math_expressions", "notification_listener_service", "package_info_plus", "path", "path_provider", "pausable_timer", "permission_handler", "provider", "quick_actions", "recaptcha_enterprise_flutter", "reorderable_grid_view", "sa3_liquid", "scrollable_positioned_list", "share_plus", "shared_preferences", "shimmer", "simple_animations", "sliding_sheet", "sliver_tools", "sqlite3_flutter_libs", "sticky_and_expandable_list", "system_theme", "timer_builder", "transparent_image", "universal_html", "url_launcher", "visibility_detector"], "devDependencies": ["build_runner", "drift_dev"]}, {"name": "build_runner", "version": "2.4.15", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "drift_dev", "version": "2.22.1", "dependencies": ["analyzer", "analyzer_plugin", "args", "build", "build_config", "build_resolvers", "charcode", "cli_util", "collection", "dart_style", "drift", "io", "json_annotation", "logging", "meta", "package_config", "path", "pub_semver", "recase", "source_gen", "source_span", "sqlite3", "sqlparser", "stream_transform", "string_scanner", "yaml"]}, {"name": "connectivity_plus", "version": "5.0.2", "dependencies": ["connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "js", "meta", "nm"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "flutter_haptic_feedback", "version": "1.0.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_signature_pad", "version": "3.0.1", "dependencies": ["flutter"]}, {"name": "flutter_colorpicker", "version": "1.1.0", "dependencies": ["flutter"]}, {"name": "flutter_staggered_grid_view", "version": "0.7.0", "dependencies": ["flutter"]}, {"name": "expandable_page_view", "version": "1.0.17", "dependencies": ["flutter"]}, {"name": "app_links", "version": "6.4.0", "dependencies": ["app_links_linux", "app_links_platform_interface", "app_links_web", "flutter"]}, {"name": "material_symbols_icons", "version": "4.2811.0", "dependencies": ["args", "<PERSON><PERSON><PERSON>", "flutter", "glob", "path"]}, {"name": "home_widget", "version": "0.7.0+1", "dependencies": ["flutter", "path_provider", "path_provider_foundation"]}, {"name": "notification_listener_service", "version": "0.3.4", "dependencies": ["flutter"]}, {"name": "flutter_displaymode", "version": "0.6.0", "dependencies": ["flutter"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "confetti", "version": "0.8.0", "dependencies": ["flutter", "vector_math"]}, {"name": "visibility_detector", "version": "0.4.0+2", "dependencies": ["flutter"]}, {"name": "in_app_purchase", "version": "3.2.1", "dependencies": ["flutter", "in_app_purchase_android", "in_app_purchase_platform_interface", "in_app_purchase_storekit"]}, {"name": "gradient_borders", "version": "1.0.1", "dependencies": ["flutter"]}, {"name": "quick_actions", "version": "1.1.0", "dependencies": ["flutter", "quick_actions_android", "quick_actions_ios", "quick_actions_platform_interface"]}, {"name": "easy_localization", "version": "3.0.7+1", "dependencies": ["args", "easy_logger", "flutter", "flutter_localizations", "intl", "path", "shared_preferences"]}, {"name": "scrollable_positioned_list", "version": "0.3.8", "dependencies": ["collection", "flutter"]}, {"name": "device_preview", "version": "1.2.0", "dependencies": ["collection", "device_frame", "flutter", "flutter_localizations", "freezed_annotation", "json_annotation", "provider", "shared_preferences"]}, {"name": "flutter_timezone", "version": "4.1.0", "dependencies": ["flutter", "flutter_web_plugins"]}, {"name": "in_app_review", "version": "2.0.10", "dependencies": ["flutter", "in_app_review_platform_interface"]}, {"name": "provider", "version": "6.1.4", "dependencies": ["collection", "flutter", "nested"]}, {"name": "app_settings", "version": "5.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_lazy_indexed_stack", "version": "0.0.6", "dependencies": ["flutter"]}, {"name": "device_info_plus", "version": "11.4.0", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "timer_builder", "version": "2.0.0", "dependencies": ["flutter"]}, {"name": "local_auth", "version": "2.3.0", "dependencies": ["flutter", "local_auth_android", "local_auth_darwin", "local_auth_platform_interface", "local_auth_windows"]}, {"name": "reorderable_grid_view", "version": "2.2.6", "dependencies": ["flutter"]}, {"name": "sliver_tools", "version": "0.2.12", "dependencies": ["flutter"]}, {"name": "recaptcha_enterprise_flutter", "version": "18.7.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "firebase_auth_web", "version": "5.14.1", "dependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "meta", "web"]}, {"name": "firebase_core_web", "version": "2.21.1", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "cloud_firestore", "version": "5.6.5", "dependencies": ["cloud_firestore_platform_interface", "cloud_firestore_web", "collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_auth", "version": "5.5.1", "dependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_core", "version": "3.12.1", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "system_theme", "version": "3.1.2", "dependencies": ["flutter", "system_theme_web"]}, {"name": "flutter_local_notifications", "version": "19.2.0", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "flutter_local_notifications_windows", "timezone"]}, {"name": "animated_fractionally_sized_box", "version": "2.0.1", "dependencies": ["flutter"]}, {"name": "pausable_timer", "version": "3.1.0+3", "dependencies": ["clock"]}, {"name": "transparent_image", "version": "2.0.1", "dependencies": []}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "flutter_launcher_icons", "version": "0.14.3", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "carousel_slider", "version": "5.0.0", "dependencies": ["flutter"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "implicitly_animated_reorderable_list", "version": "0.4.2", "dependencies": ["async", "flutter", "meta"]}, {"name": "sliding_sheet", "version": "0.5.2", "dependencies": ["flutter"]}, {"name": "csv", "version": "6.0.0", "dependencies": []}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "auto_size_text", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "file_picker", "version": "10.1.7", "dependencies": ["cross_file", "ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "web", "win32"]}, {"name": "flutter_charset_detector", "version": "5.0.0", "dependencies": ["flutter", "flutter_charset_detector_android", "flutter_charset_detector_darwin", "flutter_charset_detector_platform_interface", "flutter_charset_detector_web"]}, {"name": "universal_html", "version": "2.2.4", "dependencies": ["async", "charcode", "collection", "csslib", "html", "meta", "source_span", "typed_data", "universal_io"]}, {"name": "googlea<PERSON>", "version": "14.0.0", "dependencies": ["_discoveryapis_commons", "http"]}, {"name": "google_sign_in", "version": "6.3.0", "dependencies": ["flutter", "google_sign_in_android", "google_sign_in_ios", "google_sign_in_platform_interface", "google_sign_in_web"]}, {"name": "share_plus", "version": "10.1.4", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "web", "win32"]}, {"name": "math_expressions", "version": "2.7.0", "dependencies": ["petitparser", "vector_math"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "sqlite3_flutter_libs", "version": "0.5.32", "dependencies": ["flutter"]}, {"name": "drift", "version": "2.22.1", "dependencies": ["async", "collection", "convert", "meta", "path", "sqlite3", "stack_trace", "stream_channel", "web"]}, {"name": "fl_chart", "version": "1.0.0", "dependencies": ["equatable", "flutter", "vector_math"]}, {"name": "sticky_and_expandable_list", "version": "1.1.3", "dependencies": ["flutter"]}, {"name": "flutter_sticky_header", "version": "0.8.0", "dependencies": ["flutter", "value_layout_builder"]}, {"name": "animations", "version": "2.0.11", "dependencies": ["flutter"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "sa3_liquid", "version": "1.0.1", "dependencies": ["flutter", "supercharged"]}, {"name": "simple_animations", "version": "5.1.0", "dependencies": ["flutter"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "web_socket_channel", "version": "3.0.2", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.1", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "http", "version": "1.3.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "2.3.8", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "8.0.0", "dependencies": ["async", "build", "build_config", "build_resolvers", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.4.4", "dependencies": ["analyzer", "async", "build", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform", "yaml"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.4.2", "dependencies": ["analyzer", "async", "convert", "crypto", "glob", "logging", "meta", "package_config", "path"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "analyzer", "version": "6.11.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "macros", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "source_gen", "version": "1.5.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "source_span", "yaml"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "analyzer_plugin", "version": "0.11.3", "dependencies": ["analyzer", "collection", "dart_style", "pub_semver", "yaml"]}, {"name": "sqlparser", "version": "0.40.0", "dependencies": ["charcode", "collection", "meta", "source_span"]}, {"name": "sqlite3", "version": "2.7.5", "dependencies": ["collection", "ffi", "meta", "path", "typed_data", "web"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "recase", "version": "4.1.0", "dependencies": []}, {"name": "charcode", "version": "1.4.0", "dependencies": []}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "1.2.4", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "app_links_web", "version": "1.0.4", "dependencies": ["app_links_platform_interface", "flutter", "flutter_web_plugins", "web"]}, {"name": "app_links_platform_interface", "version": "2.0.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "app_links_linux", "version": "1.0.3", "dependencies": ["app_links_platform_interface", "flutter", "gtk"]}, {"name": "<PERSON><PERSON><PERSON>", "version": "2.4.0", "dependencies": ["args"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+22", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "in_app_purchase_storekit", "version": "0.3.21", "dependencies": ["collection", "flutter", "in_app_purchase_platform_interface", "json_annotation"]}, {"name": "in_app_purchase_platform_interface", "version": "1.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "in_app_purchase_android", "version": "0.4.0+1", "dependencies": ["collection", "flutter", "in_app_purchase_platform_interface"]}, {"name": "quick_actions_platform_interface", "version": "1.1.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "quick_actions_ios", "version": "1.2.0", "dependencies": ["flutter", "quick_actions_platform_interface"]}, {"name": "quick_actions_android", "version": "1.0.20", "dependencies": ["flutter", "quick_actions_platform_interface"]}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "easy_logger", "version": "0.0.2", "dependencies": ["flutter"]}, {"name": "freezed_annotation", "version": "2.4.4", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "device_frame", "version": "1.2.0", "dependencies": ["flutter", "freezed_annotation"]}, {"name": "in_app_review_platform_interface", "version": "2.0.5", "dependencies": ["flutter", "platform", "plugin_platform_interface", "url_launcher"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "win32_registry", "version": "2.1.0", "dependencies": ["ffi", "meta", "win32"]}, {"name": "win32", "version": "5.12.0", "dependencies": ["ffi"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "device_info_plus_platform_interface", "version": "7.0.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "local_auth_windows", "version": "1.0.11", "dependencies": ["flutter", "local_auth_platform_interface"]}, {"name": "local_auth_platform_interface", "version": "1.0.10", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "local_auth_darwin", "version": "1.4.3", "dependencies": ["flutter", "intl", "local_auth_platform_interface"]}, {"name": "local_auth_android", "version": "1.0.48", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "intl", "local_auth_platform_interface"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "firebase_auth_platform_interface", "version": "7.6.1", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "5.4.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "cloud_firestore_web", "version": "4.4.5", "dependencies": ["_flutterfire_internals", "cloud_firestore_platform_interface", "collection", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "cloud_firestore_platform_interface", "version": "6.6.5", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "system_theme_web", "version": "0.0.3", "dependencies": ["flutter", "flutter_web_plugins", "web"]}, {"name": "timezone", "version": "0.10.1", "dependencies": ["http", "path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "9.0.0", "dependencies": ["plugin_platform_interface"]}, {"name": "flutter_local_notifications_windows", "version": "1.0.0", "dependencies": ["ffi", "flutter", "flutter_local_notifications_platform_interface", "meta", "timezone", "xml"]}, {"name": "flutter_local_notifications_linux", "version": "6.0.0", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "checked_yaml", "version": "2.0.3", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.0", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.15", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.8", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.27", "dependencies": ["flutter"]}, {"name": "flutter_charset_detector_platform_interface", "version": "1.1.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_charset_detector_web", "version": "2.0.0", "dependencies": ["flutter", "flutter_charset_detector_platform_interface", "flutter_web_plugins"]}, {"name": "flutter_charset_detector_darwin", "version": "1.1.0", "dependencies": ["flutter", "flutter_charset_detector_platform_interface"]}, {"name": "flutter_charset_detector_android", "version": "3.0.0", "dependencies": ["flutter", "flutter_charset_detector_platform_interface"]}, {"name": "universal_io", "version": "2.2.2", "dependencies": ["collection", "meta", "typed_data"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "html", "version": "0.15.5", "dependencies": ["csslib", "source_span"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "_discoveryapis_commons", "version": "1.0.7", "dependencies": ["http", "http_parser", "meta"]}, {"name": "google_sign_in_web", "version": "0.12.4+4", "dependencies": ["flutter", "flutter_web_plugins", "google_identity_services_web", "google_sign_in_platform_interface", "http", "web"]}, {"name": "google_sign_in_platform_interface", "version": "2.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "google_sign_in_ios", "version": "5.8.1", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "google_sign_in_android", "version": "6.2.0", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "share_plus_platform_interface", "version": "5.0.2", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_android", "version": "2.2.16", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "value_layout_builder", "version": "0.5.0", "dependencies": ["flutter"]}, {"name": "supercharged", "version": "2.1.1", "dependencies": ["flutter", "supercharged_dart"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "web_socket", "version": "0.1.6", "dependencies": ["web"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "built_value", "version": "8.9.5", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "macros", "version": "0.1.3-main.0", "dependencies": ["_macros"]}, {"name": "_fe_analyzer_shared", "version": "76.0.0", "dependencies": ["meta"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "gtk", "version": "2.1.0", "dependencies": ["ffi", "flutter", "meta"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "_flutterfire_internals", "version": "1.3.53", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "google_identity_services_web", "version": "0.3.3", "dependencies": ["meta", "web"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "supercharged_dart", "version": "2.1.1", "dependencies": []}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "_macros", "version": "0.3.3", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "posix", "version": "6.0.2", "dependencies": ["ffi", "meta", "path"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}], "configVersion": 1}