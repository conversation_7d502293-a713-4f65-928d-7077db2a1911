<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="_discoveryapis_commons">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/_discoveryapis_commons-1.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="_fe_analyzer_shared">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="_flutterfire_internals">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.53/lib" />
            </list>
          </value>
        </entry>
        <entry key="_macros">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../FlutterSDK/bin/cache/dart-sdk/pkg/_macros/lib" />
            </list>
          </value>
        </entry>
        <entry key="analyzer">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="analyzer_plugin">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer_plugin-0.11.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="animated_fractionally_sized_box">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/animated_fractionally_sized_box-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="animations">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/animations-2.0.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_links-6.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_linux-1.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_web-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_settings">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_settings-5.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="archive">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="auto_size_text">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_config">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_daemon">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_daemon-4.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_resolvers">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_resolvers-2.4.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner-2.4.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner_core">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner_core-8.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_collection">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_value">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/built_value-8.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="carousel_slider">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/carousel_slider-5.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="chalkdart">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/chalkdart-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="charcode">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/charcode-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="checked_yaml">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="cli_util">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cli_util-0.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_firestore">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_firestore_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="cloud_firestore_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="code_builder">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/code_builder-4.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="confetti">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/confetti-0.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-5.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="csslib">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="csv">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/csv-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="dart_style">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-2.3.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="dbus">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_frame">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/device_frame-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-11.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_preview">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/device_preview-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="drift">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/drift-2.22.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="drift_dev">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/drift_dev-2.22.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="easy_localization">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/easy_localization-3.0.7+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="easy_logger">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/easy_logger-0.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="equatable">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="expandable_page_view">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/expandable_page_view-1.0.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_picker">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-10.1.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_macos">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.14.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.21.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fl_chart">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/fl_chart-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../FlutterSDK/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_charset_detector">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_charset_detector_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_android-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_charset_detector_ios">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_ios-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_charset_detector_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_platform_interface-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_colorpicker">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_colorpicker-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_displaymode">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_displaymode-0.6.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_haptic_feedback">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_haptic_feedback-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_launcher_icons">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lazy_indexed_stack">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lazy_indexed_stack-0.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications-19.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_localizations">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../FlutterSDK/packages/flutter_localizations/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.27/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_macos">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_signature_pad">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_signature_pad-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_staggered_grid_view">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_sticky_header">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_sticky_header-0.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../FlutterSDK/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_timezone">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_timezone-4.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../FlutterSDK/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/freezed_annotation-2.4.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="frontend_server_client">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="glob">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_identity_services_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-6.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-6.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_ios">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-5.8.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="googleapis">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/googleapis-14.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="gradient_borders">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/gradient_borders-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="graphs">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="gtk">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/gtk-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="home_widget">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/home_widget-0.7.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="html">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_multi_server">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+22/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_for_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_ios">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_macos">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_purchase">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_purchase_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_android-0.4.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_purchase_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_platform_interface-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_purchase_storekit">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_storekit-0.3.21/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_review">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review-2.0.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_review_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review_platform_interface-2.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="io">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_android-1.0.48/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth_darwin">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_darwin-1.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_windows-1.0.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="macros">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/macros-0.1.3-main.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_symbols_icons">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/material_symbols_icons-4.2811.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="math_expressions">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/math_expressions-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nm">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="notification_listener_service">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/notification_listener_service-0.3.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_config">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pausable_timer">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/pausable_timer-3.1.0+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_apple">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_html">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="pool">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="posix">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="pub_semver">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pubspec_parse">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="quick_actions">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="quick_actions_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_android-1.0.20/lib" />
            </list>
          </value>
        </entry>
        <entry key="quick_actions_ios">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_ios-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="quick_actions_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_platform_interface-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="recaptcha_enterprise_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/recaptcha_enterprise_flutter-18.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="recase">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/recase-4.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="reorderable_grid_view">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/git/reorderable_grid_view-ad913fc157323c3dca9e05628e575f54b3c2b8b6//lib" />
            </list>
          </value>
        </entry>
        <entry key="sa3_liquid">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sa3_liquid-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="scrollable_positioned_list">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="share_plus">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-10.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="share_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shimmer">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shimmer-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="simple_animations">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/simple_animations-5.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../FlutterSDK/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="sliver_tools">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sliver_tools-0.2.12/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_gen">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sprintf">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqlite3">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3-2.7.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqlite3_flutter_libs">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.32/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqlparser">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sqlparser-0.40.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sticky_and_expandable_list">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sticky_and_expandable_list-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="supercharged">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="supercharged_dart">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged_dart-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="system_theme">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/system_theme-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="system_theme_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/system_theme_web-0.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="timer_builder">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/timer_builder-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="timezone">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/timezone-0.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="timing">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="transparent_image">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/transparent_image-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="universal_html">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/universal_html-2.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="universal_io">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/universal_io-2.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="value_layout_builder">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/value_layout_builder-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="visibility_detector">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/visibility_detector-0.4.0+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="watcher">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-0.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket_channel">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.12.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32_registry">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/git/reorderable_grid_view-ad913fc157323c3dca9e05628e575f54b3c2b8b6//lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/_discoveryapis_commons-1.0.7/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.53/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.11.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer_plugin-0.11.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/animated_fractionally_sized_box-2.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/animations-2.0.11/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_links-6.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_linux-1.0.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_links_web-1.0.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/app_settings-5.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text-3.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.4.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_daemon-4.0.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_resolvers-2.4.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner-2.4.15/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/build_runner_core-8.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/built_value-8.9.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/carousel_slider-5.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/chalkdart-2.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/charcode-1.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cli_util-0.4.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/code_builder-4.10.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/confetti-0.8.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-5.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/csv-6.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-2.3.8/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/device_frame-1.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-11.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/device_preview-1.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/drift-2.22.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/drift_dev-2.22.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/easy_localization-3.0.7+1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/easy_logger-0.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/expandable_page_view-1.0.17/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-10.1.7/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.5.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.6.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.14.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.12.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.21.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/fl_chart-1.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector-1.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_android-1.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_ios-1.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_charset_detector_platform_interface-1.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_colorpicker-1.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_displaymode-0.6.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_haptic_feedback-1.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lazy_indexed_stack-0.0.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications-19.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.27/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_signature_pad-3.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_sticky_header-0.8.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_timezone-4.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/freezed_annotation-2.4.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-6.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-6.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-5.8.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/googleapis-14.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/gradient_borders-1.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/gtk-2.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/home_widget-0.7.0+1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+22/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase-3.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_android-0.4.0+1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_platform_interface-1.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_purchase_storekit-0.3.21/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review-2.0.10/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review_platform_interface-2.0.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth-2.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_android-1.0.48/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_darwin-1.4.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/local_auth_windows-1.0.11/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/macros-0.1.3-main.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/material_symbols_icons-4.2811.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/math_expressions-2.7.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/notification_listener_service-0.3.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.16/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/pausable_timer-3.1.0+3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions-1.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_android-1.0.20/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_ios-1.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/quick_actions_platform_interface-1.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/recaptcha_enterprise_flutter-18.7.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/recase-4.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sa3_liquid-1.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/scrollable_positioned_list-0.3.8/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-10.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.8/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/shimmer-3.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/simple_animations-5.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sliver_tools-0.2.12/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3-2.7.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.32/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sqlparser-0.40.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/sticky_and_expandable_list-1.1.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged-2.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged_dart-2.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/system_theme-3.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/system_theme_web-0.0.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/timer_builder-2.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/timezone-0.10.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/transparent_image-2.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/universal_html-2.2.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/universal_io-2.2.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.15/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/value_layout_builder-0.5.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/visibility_detector-0.4.0+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-0.1.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.12.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-2.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/lib" />
      <root url="file://$PROJECT_DIR$/../../FlutterSDK/bin/cache/dart-sdk/pkg/_macros/lib" />
      <root url="file://$PROJECT_DIR$/../../FlutterSDK/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$PROJECT_DIR$/../../FlutterSDK/packages/flutter/lib" />
      <root url="file://$PROJECT_DIR$/../../FlutterSDK/packages/flutter_localizations/lib" />
      <root url="file://$PROJECT_DIR$/../../FlutterSDK/packages/flutter_test/lib" />
      <root url="file://$PROJECT_DIR$/../../FlutterSDK/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>