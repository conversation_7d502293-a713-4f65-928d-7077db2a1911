@echo off
echo ========================================
echo   Budget App - Serve Built Web Files
echo ========================================
echo.

REM Check if build directory exists
if not exist "build\web" (
    echo ERROR: No web build found!
    echo.
    echo Please build the web version first:
    echo 1. Run: flutter build web
    echo 2. Or use: run_web_server.bat
    echo.
    pause
    exit /b 1
)

echo Found web build in: build\web\
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting local server using Python on localhost:8080...
    echo.
    echo The app is available at: http://localhost:8080
    echo Press Ctrl+C to stop the server
    echo.
    
    REM Open browser automatically
    start http://localhost:8080
    
    REM Start Python server
    cd build\web
    python -m http.server 8080
    cd ..\..
    goto :end
)

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting local server using Node.js on localhost:8080...
    echo.
    echo The app is available at: http://localhost:8080
    echo Press Ctrl+C to stop the server
    echo.
    
    REM Open browser automatically
    start http://localhost:8080
    
    REM Start Node.js server
    npx serve build\web -p 8080
    goto :end
)

REM No server available
echo.
echo ERROR: No suitable server found!
echo.
echo To serve the web files, you need either:
echo 1. Python 3: python -m http.server 8080
echo 2. Node.js: npx serve build\web -p 8080
echo.
echo Install one of these and try again.
echo.
echo Alternatively, you can open build\web\index.html directly
echo (Note: Some features may not work when opening directly)
echo.

:end
echo.
echo Server stopped.
pause
