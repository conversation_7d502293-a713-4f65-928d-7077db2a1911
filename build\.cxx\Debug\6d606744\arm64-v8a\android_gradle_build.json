{"buildFiles": ["D:\\App\\Android Studio\\FlutterSDK\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\Debug\\6d606744\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\App\\Android Studio\\AndoirdSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\App\\Android Studio\\Projects\\budget\\build\\.cxx\\Debug\\6d606744\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\App\\Android Studio\\AndoirdSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}