<FrameLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/widget_container"
    android:layout_margin="6dp">
	<ImageView
        android:id="@+id/widget_background"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:scaleType="fitXY"
        android:src="@drawable/widget_background"
    />
	<LinearLayout
		xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center_vertical|center_horizontal"
        android:padding="8dp">
		<TextView
            android:id="@+id/net_worth_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:gravity="center_vertical|center_horizontal"
            android:layout_centerInParent="true"
            android:padding="2dp"
            android:textSize="15sp"
            android:textStyle="bold"
            android:textColor="#000000"
        />
		<TextView
            android:id="@+id/net_worth_amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:gravity="center_vertical|center_horizontal"
            android:layout_centerInParent="true"
            android:padding="2dp"
            android:textSize="21sp"
            android:textStyle="bold"
            android:textColor="#000000"
        />
		<TextView
            android:id="@+id/net_worth_transactions_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical|center_horizontal"
            android:layout_centerInParent="true"
            android:padding="2dp"
            android:text="Tap to configure widget"
            android:textSize="13sp"
            android:textColor="#000000"
        />
	</LinearLayout>
</FrameLayout>