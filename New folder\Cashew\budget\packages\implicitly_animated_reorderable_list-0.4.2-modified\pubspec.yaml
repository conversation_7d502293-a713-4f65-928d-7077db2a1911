name: implicitly_animated_reorderable_list
description: A Flutter ListView that implicitly animates between the changes of
  two lists with the support to reorder its items.
version: 0.4.2
homepage: https://github.com/BendixMa/implicitly_animated_reorderable_list/tree/master/example/lib/ui

environment:
  sdk: '>=2.12.0 <3.0.0'

dependencies:
  flutter:
    sdk: flutter
  async: ^2.8.1
  meta: ^1.7.0

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter: null
