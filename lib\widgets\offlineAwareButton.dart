import 'package:budget/main.dart'; // Import for entireAppLoaded
import 'package:budget/struct/settings.dart';
import 'package:budget/functions.dart';
import 'package:budget/widgets/globalSnackbar.dart';
import 'package:budget/widgets/openSnackbar.dart';
import 'package:budget/widgets/outlinedButtonStacked.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

/// A wrapper widget for buttons that should be disabled in offline mode
/// It will blur the button and show a snackbar message when tapped in offline mode
class OfflineAwareButton extends StatelessWidget {
  final Widget child;
  final VoidCallback onTap;
  final bool isBackupRelated;

  const OfflineAwareButton({
    Key? key,
    required this.child,
    required this.onTap,
    this.isBackupRelated = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Check if we're in offline mode
    final bool isOfflineMode = appStateSettings["isOfflineMode"] == true;

    // If we're not in offline mode or this isn't a backup-related button,
    // just return the child as-is
    if (!isOfflineMode || !isBackupRelated) {
      return child;
    }

    // In offline mode, wrap the child with a blur effect and handle taps
    return Stack(
      children: [
        // The original button with reduced opacity
        Opacity(
          opacity: 0.5,
          child: child,
        ),

        // Invisible overlay to capture taps
        Positioned.fill(
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(15),
              onTap: () {
                // Show a snackbar message when tapped in offline mode
                // Only if the app is fully loaded (not during startup)
                if (entireAppLoaded) {
                  openSnackbar(
                    SnackbarMessage(
                      title: "offline-mode".tr(),
                      icon: Icons.cloud_off,
                      timeout: const Duration(milliseconds: 2500),
                    ),
                  );
                }
              },
            ),
          ),
        ),
      ],
    );
  }
}

/// A version of OutlinedButtonStacked that is aware of offline mode
/// It will be blurred and show a snackbar message when tapped in offline mode
class OfflineAwareOutlinedButton extends StatelessWidget {
  final String text;
  final IconData iconData;
  final VoidCallback onTap;
  final bool isBackupRelated;
  final double? fontSize;
  final Widget? afterWidget;
  final bool alignStart;
  final EdgeInsetsDirectional? padding;
  final EdgeInsetsDirectional? afterWidgetPadding;
  final bool? alignBeside;
  final bool filled;
  final bool transitionWhenFilled;
  final Widget? infoButton;
  final double iconScale;
  final double? borderRadius;
  final bool showToggleSwitch;

  const OfflineAwareOutlinedButton({
    Key? key,
    required this.text,
    required this.iconData,
    required this.onTap,
    this.isBackupRelated = true,
    this.fontSize,
    this.afterWidget,
    this.alignStart = false,
    this.padding,
    this.afterWidgetPadding,
    this.alignBeside,
    this.filled = false,
    this.transitionWhenFilled = true,
    this.infoButton,
    this.iconScale = 1,
    this.borderRadius,
    this.showToggleSwitch = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OfflineAwareButton(
      isBackupRelated: isBackupRelated,
      onTap: onTap,
      child: OutlinedButtonStacked(
        text: text,
        iconData: iconData,
        onTap: onTap,
        fontSize: fontSize,
        afterWidget: afterWidget,
        alignStart: alignStart,
        padding: padding,
        afterWidgetPadding: afterWidgetPadding,
        alignBeside: alignBeside,
        filled: filled,
        transitionWhenFilled: transitionWhenFilled,
        infoButton: infoButton,
        iconScale: iconScale,
        borderRadius: borderRadius,
        showToggleSwitch: showToggleSwitch,
      ),
    );
  }
}
