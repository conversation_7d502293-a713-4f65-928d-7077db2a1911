import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:budget/struct/settings.dart';

/// Determines if the current platform is a desktop platform
/// 
/// Returns true for Windows, macOS, and Linux
bool isDesktopPlatform() {
  if (kIsWeb) return false;
  
  try {
    return Platform.isWindows || Platform.isMacOS || Platform.isLinux;
  } catch (e) {
    return false;
  }
}

/// Enhanced platform detection that includes desktop platforms
/// 
/// Extends the existing PlatformOS enum to handle desktop platforms
enum ExtendedPlatformOS {
  isIOS,
  isAndroid,
  isMacOS,
  isWindows,
  isLinux,
  web,
  unknown
}

/// Get the current platform with enhanced desktop support
/// 
/// @param ignoreEmulation Whether to ignore iOS emulation setting
/// @return The detected platform
ExtendedPlatformOS getExtendedPlatform({bool ignoreEmulation = false}) {
  if (kIsWeb) {
    return ExtendedPlatformOS.web;
  }
  
  try {
    if (appStateSettings["iOSEmulate"] == true && ignoreEmulation == false) {
      return ExtendedPlatformOS.isIOS;
    } else if (Platform.isIOS) {
      return ExtendedPlatformOS.isIOS;
    } else if (Platform.isAndroid) {
      return ExtendedPlatformOS.isAndroid;
    } else if (Platform.isMacOS) {
      return ExtendedPlatformOS.isMacOS;
    } else if (Platform.isWindows) {
      return ExtendedPlatformOS.isWindows;
    } else if (Platform.isLinux) {
      return ExtendedPlatformOS.isLinux;
    }
  } catch (e) {
    print("Error detecting platform: $e");
  }
  
  return ExtendedPlatformOS.unknown;
}
