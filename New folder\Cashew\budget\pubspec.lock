# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _discoveryapis_commons:
    dependency: transitive
    description:
      name: _discoveryapis_commons
      sha256: f8bb1fdbd77f3d5c1d62b5b0eca75fbf1e41bf4f6c62628f880582e2182ae45d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: b46f62516902afb04befa4b30eb6a12ac1f58ca8cb25fb9d632407259555dd3d
      url: "https://pub.dev"
    source: hosted
    version: "1.3.39"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  analyzer_plugin:
    dependency: transitive
    description:
      name: analyzer_plugin
      sha256: c1d5f167683de03d5ab6c3b53fc9aeefc5d59476e7810ba7bbddff50c6f4392d
      url: "https://pub.dev"
    source: hosted
    version: "0.11.2"
  animated_fractionally_sized_box:
    dependency: "direct main"
    description:
      name: animated_fractionally_sized_box
      sha256: "145e1702391fee56ba08d1a9e64f0169948b308be8e70252f6e4fb45f1ab187a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  animations:
    dependency: "direct main"
    description:
      name: animations
      sha256: d3d6dcfb218225bbe68e87ccf6378bbb2e32a94900722c5f81611dad089911cb
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  app_links:
    dependency: "direct main"
    description:
      name: app_links
      sha256: ae5f9a1b7d40d26178f605414be81ed4260350b4fae8259fe5ca4f89fe70c4af
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  app_links_linux:
    dependency: transitive
    description:
      name: app_links_linux
      sha256: f5f7173a78609f3dfd4c2ff2c95bd559ab43c80a87dc6a095921d96c05688c81
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  app_links_platform_interface:
    dependency: transitive
    description:
      name: app_links_platform_interface
      sha256: "05f5379577c513b534a29ddea68176a4d4802c46180ee8e2e966257158772a3f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  app_links_web:
    dependency: transitive
    description:
      name: app_links_web
      sha256: "74586ed5f3c4786341e82a0fa43c39ec3f13108a550f74e80d8bf68aa11349d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  app_settings:
    dependency: "direct main"
    description:
      name: app_settings
      sha256: "09bc7fe0313a507087bec1a3baf555f0576e816a760cbb31813a88890a09d9e5"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "0c8368c9b3f0abbc193b9d6133649a614204b528982bebc7026372d61677ce3a"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.7"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  auto_size_text:
    dependency: "direct main"
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "43865b79fbb78532e4bff7c33087aa43b1d488c4fdef014eaef568af6d8016dc"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "5f02d73eb2ba16483e693f80bee4f088563a820e47d1027d4cdfe62b5bb43e65"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "64e12b0521812d1684b1917bc80945625391cb9bdd4312536b1d69dcb6133ed8"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "3ac61a79bfb6f6cc11f693591063a7f19a7af628dc52f141743edac5c16e8c22"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.9"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "6d6ee4276b1c5f34f21fdf39425202712d2be82019983d52f351c94aafbc2c41"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.10"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "598a2a682e2a7a90f08ba39c0aaa9374c5112340f0a2e275f61b59389543d166"
      url: "https://pub.dev"
    source: hosted
    version: "8.6.1"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb98c0f6d12c920a02ee2d998da788bca066ca5f148492b7085ee23372b12306
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: "66f86e916d285c1a93d3b79587d94bd71984a66aac4ff74e524cfa7877f1395c"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.5"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  cloud_firestore:
    dependency: "direct main"
    description:
      name: cloud_firestore
      sha256: "240c1c3598e62ad58ee665b6df9c65172d2fbe4742770c21ed060e013cb8e037"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: "5b5a9c2b5a85bf995f12e7447c4197d7ad659533d642d3d904ccbb509f83d62a"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.9"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: "898e9f65548df65ca7b2cff9f32cf233424ceccff1d870b8819ea2b8050d5b39"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "4ad01d6e56db961d29661561effde45e519939fdaeb46c351275b182eac70189"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  confetti:
    dependency: "direct main"
    description:
      name: confetti
      sha256: "979aafde2428c53947892c95eb244466c109c129b7eee9011f0a66caaca52267"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "55d7b444feb71301ef6b8838dbc1ae02e63dd48c8773f3810ff53bb1e2945b32"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  csv:
    dependency: "direct main"
    description:
      name: csv
      sha256: c6aa2679b2a18cb57652920f674488d89712efaf4d3fdf2e537215b35fc19d6c
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: f4f1f73ab3fd2afcbcca165ee601fe980d966af6a21b5970c6c9376955c528ad
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "6f07cba3f7b3448d42d015bfd3d53fe12e5b36da2423f23838efc1d5fb31a263"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.8"
  device_frame:
    dependency: transitive
    description:
      name: device_frame
      sha256: afe76182aec178d171953d9b4a50a43c57c7cf3c77d8b09a48bf30c8fa04dd9d
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: eead12d1a1ed83d8283ab4c2f3fca23ac4082f29f25f29dff0f758f57d06ec91
      url: "https://pub.dev"
    source: hosted
    version: "10.1.0"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: d3b01d5868b50ae571cd1dc6e502fc94d956b665756180f7b16ead09e836fd64
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  device_preview:
    dependency: "direct main"
    description:
      name: device_preview
      sha256: "2f097bf31b929e15e6756dbe0ec1bcb63952ab9ed51c25dc5a2c722d2b21fdaf"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  drift:
    dependency: "direct main"
    description:
      name: drift
      sha256: "6acedc562ffeed308049f78fb1906abad3d65714580b6745441ee6d50ec564cd"
      url: "https://pub.dev"
    source: hosted
    version: "2.18.0"
  drift_dev:
    dependency: "direct dev"
    description:
      name: drift_dev
      sha256: d9b020736ea85fff1568699ce18b89fabb3f0f042e8a7a05e84a3ec20d39acde
      url: "https://pub.dev"
    source: hosted
    version: "2.18.0"
  easy_localization:
    dependency: "direct main"
    description:
      name: easy_localization
      sha256: fa59bcdbbb911a764aa6acf96bbb6fa7a5cf8234354fc45ec1a43a0349ef0201
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  easy_logger:
    dependency: transitive
    description:
      name: easy_logger
      sha256: c764a6e024846f33405a2342caf91c62e357c24b02c04dbc712ef232bf30ffb7
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  expandable_page_view:
    dependency: "direct main"
    description:
      name: expandable_page_view
      sha256: "210dc6961cfc29f7ed42867824eb699c9a4b9b198a7c04b8bdc1c05844969dc6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.17"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: ed5337a5660c506388a9f012be0288fb38b49020ce2b45fe1f8b8323fe429f99
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  file_picker:
    dependency: "direct main"
    description:
      path: "."
      ref: HEAD
      resolved-ref: ccbb9a384491cc0759f94618bb3859411e228091
      url: "https://github.com/melWiss/flutter_file_picker.git"
    source: git
    version: "6.0.1"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "045d372bf19b02aeb69cacf8b4009555fb5f6f0b7ad8016e5f46dd1387ddd492"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2+1"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: b15c3da8bd4908b9918111fa486903f5808e388b8d1c559949f584725a6594d6
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+3"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: "0aa47a725c346825a2bd396343ce63ac00bda6eff2fbc43eabe99737dede8262"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: d3547240c20cabf205c7c7f01a50ecdbc413755814d6677f3cb366f04abcead0
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  firebase_auth:
    dependency: "direct main"
    description:
      name: firebase_auth
      sha256: a41b56878fa6aef3ea52962329b47eee333672d4b0ecc406e071b9fc729f242c
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      sha256: d1c68097588f3b75ef79a22102ff96c311735c254353bccf6824d19f1a7e86b9
      url: "https://pub.dev"
    source: hosted
    version: "7.4.2"
  firebase_auth_web:
    dependency: "direct main"
    description:
      name: firebase_auth_web
      sha256: e66ec0ae5697ee39ccd4865d6887cb0df220dd4ea0b21404910c68ca4c1a731a
      url: "https://pub.dev"
    source: hosted
    version: "5.12.4"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "5159984ce9b70727473eb388394650677c02c925aaa6c9439905e1f30966a4d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "1003a5a03a61fc9a22ef49f37cbcb9e46c86313a7b2e7029b9390cf8c6fc32cb"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  firebase_core_web:
    dependency: "direct main"
    description:
      name: firebase_core_web
      sha256: "23509cb3cddfb3c910c143279ac3f07f06d3120f7d835e4a5d4b42558e978712"
      url: "https://pub.dev"
    source: hosted
    version: "2.17.3"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  fl_chart:
    dependency: "direct main"
    description:
      name: fl_chart
      sha256: d0f0d49112f2f4b192481c16d05b6418bd7820e021e265a3c22db98acf7ed7fb
      url: "https://pub.dev"
    source: hosted
    version: "0.68.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_charset_detector:
    dependency: "direct main"
    description:
      name: flutter_charset_detector
      sha256: edc87079f3c8917f2fa5620e594d10deef81bbf3d7a89cf99441dec955a06d9f
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  flutter_charset_detector_android:
    dependency: transitive
    description:
      name: flutter_charset_detector_android
      sha256: "2439a3bf418fe0fb962fc3dc9d0401c3561a497017bc587b9c48a47490f9c428"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_charset_detector_ios:
    dependency: transitive
    description:
      name: flutter_charset_detector_ios
      sha256: e0e2c7b819cd1f7c9a50da94bf7f0e6d7a095eb64e81196ec758e77793903d43
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  flutter_charset_detector_platform_interface:
    dependency: transitive
    description:
      name: flutter_charset_detector_platform_interface
      sha256: "1c09ed7b314a5a9dde76057b98b7d35458ba881eed03d5e5b6f7f74b4869d18c"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter_colorpicker:
    dependency: "direct main"
    description:
      name: flutter_colorpicker
      sha256: "969de5f6f9e2a570ac660fb7b501551451ea2a1ab9e2097e89475f60e07816ea"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter_displaymode:
    dependency: "direct main"
    description:
      name: flutter_displaymode
      sha256: "42c5e9abd13d28ed74f701b60529d7f8416947e58256e6659c5550db719c57ef"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  flutter_launcher_icons:
    dependency: "direct main"
    description:
      name: flutter_launcher_icons
      sha256: a9de6706cd844668beac27c0aed5910fa0534832b3c2cad61a5fd977fce82a5d
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0"
  flutter_lazy_indexed_stack:
    dependency: "direct main"
    description:
      name: flutter_lazy_indexed_stack
      sha256: e5529b516890475465c8c34b23d611e9c46b23c745c08edf471d1b6f899f5c9f
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: dd6676d8c2926537eccdf9f72128bbb2a9d0814689527b17f92c248ff192eaf3
      url: "https://pub.dev"
    source: hosted
    version: "17.2.1+2"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: "33f741ef47b5f63cc7f78fe75eeeac7e19f171ff3c3df054d84c1e38bedb6a03"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0+1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "950e77c2bbe1692bc0874fc7fb491b96a4dc340457f4ea1641443d0a6c1ea360"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.15"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_sticky_header:
    dependency: "direct main"
    description:
      name: flutter_sticky_header
      sha256: "017f398fbb45a589e01491861ca20eb6570a763fd9f3888165a978e11248c709"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  flutter_test:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_timezone:
    dependency: "direct main"
    description:
      name: flutter_timezone
      sha256: "06b35132c98fa188db3c4b654b7e1af7ccd01dfe12a004d58be423357605fb24"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: aeac15850ef1b38ee368d4c53ba9a847e900bb2c53a4db3f6881cbb3cb684338
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "9482364c9f8b7bd36902572ebc3a7c2b5c8ee57a9c93e6eb5099c1a9ec5265d8"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1+1"
  google_sign_in:
    dependency: "direct main"
    description:
      name: google_sign_in
      sha256: "0b8787cb9c1a68ad398e8010e8c8766bfa33556d2ab97c439fb4137756d7308f"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      sha256: f58a17ac07d783d000786a6c313fa4a0d2ee599a346d69b24fc48fb378d5d150
      url: "https://pub.dev"
    source: hosted
    version: "6.1.16"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      sha256: a058c9880be456f21e2e8571c1126eaacd570bdc5b6c6d9d15aea4bdf22ca9fe
      url: "https://pub.dev"
    source: hosted
    version: "5.7.6"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: e69553c0fc6a76216e9d06a8c3767e291ad9be42171f879aab7ab708569d4393
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: d606264c7a1a526a3aa79d938b85a601d8589731a478bd4a3dcbdeb14a572228
      url: "https://pub.dev"
    source: hosted
    version: "0.12.4+1"
  googleapis:
    dependency: "direct main"
    description:
      name: googleapis
      sha256: "4eefba93b5f714d6c2bcc1695ff6fb09d36684d2a06912285d3981069796da10"
      url: "https://pub.dev"
    source: hosted
    version: "13.1.0"
  gradient_borders:
    dependency: "direct main"
    description:
      name: gradient_borders
      sha256: b1cd969552c83f458ff755aa68e13a0327d09f06c3f42f471b423b01427f21f8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  gtk:
    dependency: transitive
    description:
      name: gtk
      sha256: e8ce9ca4b1df106e4d72dad201d345ea1a036cc12c360f1a7d5a758f78ffa42c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  home_widget:
    dependency: "direct main"
    description:
      name: home_widget
      sha256: "29565bfee4b32eaf9e7e8b998d504618b779a74b2b1ac62dd4dac7468e66f1a3"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.6"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "8e9d133755c3e84c73288363e6343157c383a0c6c56fc51afcc5d4d7180306d6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: d6a6e78821086b0b737009b09363018309bbc6de3fd88cc5c26bc2bb44a4957f
      url: "https://pub.dev"
    source: hosted
    version: "0.8.8+2"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "50bc9ae6a77eea3a8b11af5eb6c661eeb858fdd2f734c2a4fd17086922347ef7"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "76ec722aeea419d03aa915c2c96bf5b47214b053899088c9abb4086ceecf97a7"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.8+4"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "9ec26d410ff46f483c5519c29c02ef0e02e13a543f882b152d4bfd2f06802f80"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  implicitly_animated_reorderable_list:
    dependency: "direct main"
    description:
      path: "packages/implicitly_animated_reorderable_list-0.4.2-modified"
      relative: true
    source: path
    version: "0.4.2"
  in_app_purchase:
    dependency: "direct main"
    description:
      name: in_app_purchase
      sha256: "960f26a08d9351fb8f89f08901f8a829d41b04d45a694b8f776121d9e41dcad6"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  in_app_purchase_android:
    dependency: transitive
    description:
      name: in_app_purchase_android
      sha256: "3d84d1a001fa138bed09e0cd95e9dbce268dce8b6f63bda7cf69cbf9135fbfbb"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.6"
  in_app_purchase_platform_interface:
    dependency: transitive
    description:
      name: in_app_purchase_platform_interface
      sha256: "1d353d38251da5b9fea6635c0ebfc6bb17a2d28d0e86ea5e083bf64244f1fb4c"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  in_app_purchase_storekit:
    dependency: transitive
    description:
      name: in_app_purchase_storekit
      sha256: "9a087c3c051266642468f031737c5a09f6fd90ff2b21d953e130563f3fd4cb34"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.17"
  in_app_review:
    dependency: "direct main"
    description:
      name: in_app_review
      sha256: "99869244d09adc76af16bf8fd731dd13cef58ecafd5917847589c49f378cbb30"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  in_app_review_platform_interface:
    dependency: transitive
    description:
      name: in_app_review_platform_interface
      sha256: fed2c755f2125caa9ae10495a3c163aa7fab5af3585a9c62ef4a6920c5b45f10
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: c1b2e9b5ea78c45e1a0788d29606ba27dc5f71f019f32ca5140f61ef071838cf
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "78eb209deea09858f5269f5a5b02be4049535f568c07b275096836f01ea323fa"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.0"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: b46c5e37c19120a8a01918cfaf293547f47269f7cb4b0058f21531c2465d6ef0
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: a597f72a664dbd293f3bfc51f9ba69816f84dcd403cdac7066cb3f6003f3ab47
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: "280421b416b32de31405b0a25c3bd42dfcef2538dfbb20c03019e02a5ed55ed0"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: "36a78898198386d36d4e152b8cb46059b18f0e2017f813a0e833e216199f8950"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.32"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: e424ebf90d5233452be146d4a7da4bcd7a70278b67791592f3fde1bda8eef9e2
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "9e160d59ef0743e35f1b50f4fb84dc64f55676b1b8071e319ef35e7f3bc13367"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.7"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: "19323b75ab781d5362dbb15dcb7e0916d2431c7a6dbdda016ec9708689877f73"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "0e0a020085b65b6083975e499759762399b4475f766c21668c4ecca34ea74e5a"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.0"
  material_symbols_icons:
    dependency: "direct main"
    description:
      name: material_symbols_icons
      sha256: "6102193e0c451426e9bb5e6cbc8533609e464bb2afa7fce42d9d79790e012beb"
      url: "https://pub.dev"
    source: hosted
    version: "4.2768.0"
  math_expressions:
    dependency: "direct main"
    description:
      name: math_expressions
      sha256: db0b72d867491c4e53a1c773e2708d5d6e94bbe06be07080fc9f896766b9cd3d
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: d584fa6707a52763a52446f02cc621b077888fb63b93bbcb1143a7be5a0c0c04
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  notification_listener_service:
    dependency: "direct main"
    description:
      name: notification_listener_service
      sha256: "75c01f1d7317ec432aee15e2dc18444be22f6c7b0d6d0edd5481bba19b27ad90"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: b93d8b4d624b4ea19b0a5a208b2d6eff06004bc3ce74c06040b120eeadd00ce0
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: f49918f3433a3146047372f9d4f1f847511f2acd5cd030e1f44fe5a50036b70e
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: c9e7d3a4cd1410877472158bee69963a4579f78b68c65a2b7d40d1a7a88bb161
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  pausable_timer:
    dependency: "direct main"
    description:
      name: pausable_timer
      sha256: "6ef1a95441ec3439de6fb63f39a011b67e693198e7dae14e20675c3c00e86074"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: cb3798bef7fc021ac45b308f4b51208a152792445cce0448c9a4ba5879dd8750
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.dev"
    source: hosted
    version: "3.9.1"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: "direct main"
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  quick_actions:
    dependency: "direct main"
    description:
      name: quick_actions
      sha256: b17da113df7a7005977f64adfa58ccc49c829d3ccc6e8e770079a8c7fbf2da9e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.7"
  quick_actions_android:
    dependency: transitive
    description:
      name: quick_actions_android
      sha256: e31ae2181d1bd3b07e375feda452ae573c90b65fee6d4ac64cf113c789bff8c3
      url: "https://pub.dev"
    source: hosted
    version: "1.0.7"
  quick_actions_ios:
    dependency: transitive
    description:
      name: quick_actions_ios
      sha256: "9ed8b003a65034de9f36a7f593026bf114c8796a38011b23240f8bf7e4668e2b"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  quick_actions_platform_interface:
    dependency: transitive
    description:
      name: quick_actions_platform_interface
      sha256: "2985e12b5fecb5715a35cc0a3b2127b4391e1969e62bd0a4a721b4de21d5fedb"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: b1c1ac5ce6688d77f65f3375a9abb9319b3cb32486bdc7a1e0fdf004d7ba4e47
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  recaptcha_enterprise_flutter:
    dependency: "direct main"
    description:
      name: recaptcha_enterprise_flutter
      sha256: "2b53a990c686272f4e6d6f614ce7b6d5d4ccd6468ae7300d3b6e0af940852b2e"
      url: "https://pub.dev"
    source: hosted
    version: "18.5.1"
  recase:
    dependency: transitive
    description:
      name: recase
      sha256: e4eb4ec2dcdee52dcf99cb4ceabaffc631d7424ee55e56f280bc039737f89213
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  reorderable_grid_view:
    dependency: "direct main"
    description:
      path: "."
      ref: HEAD
      resolved-ref: ad913fc157323c3dca9e05628e575f54b3c2b8b6
      url: "https://github.com/jameskokoska/reorderable_grid_view"
    source: git
    version: "2.2.6"
  sa3_liquid:
    dependency: "direct main"
    description:
      name: sa3_liquid
      sha256: "609742dd35e6fd97812eda55262c50468521eb2ffc47e50e625b4ba418648bc8"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  scrollable_positioned_list:
    dependency: "direct main"
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: "59dfd53f497340a0c3a81909b220cfdb9b8973a91055c4e5ab9b9b9ad7c513c0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.0"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "6ababf341050edff57da8b6990f11f4e99eaba837865e2e6defe16d039619db5"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: d3bbe5553a986e83980916ded2f0b435ef2e1893dfaa29d5a7a790d0eca12180
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "6478c6bbbecfe9aced34c483171e90d7c078f5883558b30ec3163cf18402c749"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "0a8a893bf4fd1152f93fec03a415d11c27c74454d96e2318a7ac38dd18683ab7"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9d387433ca65717bbf1be88f4d5bb18f10508917a8fa2fb02e0fd0d7479a9afa"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "22e2ecac9419b4246d7c22bfbbda589e3acf5c0351137d87dd2939d984d37c3b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: "74083203a8eae241e0de4a0d597dbedab3b8fef5563f33cf3c12d7e93c655ca5"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "5e588e2efef56916a3b229c3bfe81e6a525665a454519ca51dbcc4236a274173"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  simple_animations:
    dependency: "direct main"
    description:
      name: simple_animations
      sha256: "1ea7b93fb98e2a611b6865d632de55607b766328d14700143353129ee0559d3a"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliding_sheet:
    dependency: "direct main"
    description:
      path: "packages/sliding_sheet-0.5.2-modified"
      relative: true
    source: path
    version: "0.5.2"
  sliver_tools:
    dependency: "direct main"
    description:
      name: sliver_tools
      sha256: eae28220badfb9d0559207badcbbc9ad5331aac829a88cb0964d330d2a4636a6
      url: "https://pub.dev"
    source: hosted
    version: "0.2.12"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "373f96cf5a8744bc9816c1ff41cf5391bbdbe3d7a96fe98c622b6738a8a7bd33"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sqlite3:
    dependency: transitive
    description:
      name: sqlite3
      sha256: b384f598b813b347c5a7e5ffad82cbaff1bec3d1561af267041e66f6f0899295
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  sqlite3_flutter_libs:
    dependency: "direct main"
    description:
      name: sqlite3_flutter_libs
      sha256: "1e20a88d5c7ae8400e009f38ddbe8b001800a6dffa37832481a86a219bc904c7"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.15"
  sqlparser:
    dependency: transitive
    description:
      name: sqlparser
      sha256: ade9a67fd70d0369329ed3373208de7ebd8662470e8c396fc8d0d60f9acdfc9f
      url: "https://pub.dev"
    source: hosted
    version: "0.36.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  sticky_and_expandable_list:
    dependency: "direct main"
    description:
      name: sticky_and_expandable_list
      sha256: "42886b52d659f1fb0f6c355192d8e4e919bdee4eb9510049b2e3d05ea988b7ed"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  supercharged:
    dependency: transitive
    description:
      name: supercharged
      sha256: ab49c848b33e28243f5ce82b976736de17d0852b71d0dfbde53fbb5e2ecca7cb
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  supercharged_dart:
    dependency: transitive
    description:
      name: supercharged_dart
      sha256: cb95edda32eacd27664089700a750120be41daa84aa6cd2aeded46227c16b867
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  system_theme:
    dependency: "direct main"
    description:
      name: system_theme
      sha256: a32db6caa3a5129d02c03443121662959fba7ec1a8b01c78ee9a42718fbb3ef6
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  system_theme_web:
    dependency: transitive
    description:
      name: system_theme_web
      sha256: "7566f5a928f6d28d7a60c97bea8a851d1c6bc9b86a4df2366230a97458489219"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5c2f730018264d276c20e4f1503fd1308dfbbae39ec8ee63c5236311ac06954b"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  timer_builder:
    dependency: "direct main"
    description:
      name: timer_builder
      sha256: "67c5653a8d9f6ce62fe9121e520736e9da74418a919fe1c0e181b5d2627dbc4a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  transparent_image:
    dependency: "direct main"
    description:
      name: transparent_image
      sha256: e8991d955a2094e197ca24c645efec2faf4285772a4746126ca12875e54ca02f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  universal_html:
    dependency: "direct main"
    description:
      name: universal_html
      sha256: "56536254004e24d9d8cfdb7dbbf09b74cf8df96729f38a2f5c238163e3d58971"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "21b704ce5fa560ea9f3b525b43601c678728ba46725bab9b01187b4831377ed3"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "17cd5e205ea615e2c6ea7a77323a11712dffa0720a8a90540db57a01347f9ad9"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: ab360eb661f8879369acac07b6bb3ff09d9471155357da8443fd5d3cf7363811
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "9a1a42d5d2d95400c795b2914c36fdcb525870c752569438e4ebb09a2b5d90de"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "8d9e750d8c9338601e709cd0885f95825086bd8b642547f26bda435aade95d8a"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: ecf9725510600aa2bb6d7ddabe16357691b6d2805f66216a97d1b881e21beff7
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  value_layout_builder:
    dependency: transitive
    description:
      name: value_layout_builder
      sha256: "98202ec1807e94ac72725b7f0d15027afde513c55c69ff3f41bcfccb950831bc"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  visibility_detector:
    dependency: "direct main"
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: b3d56ff4341b8f182b96aceb2fa20e3dcb336b9f867bc0eafc0de10f1048e957
      url: "https://pub.dev"
    source: hosted
    version: "13.0.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "3d2ad6751b3c16cf07c7fca317a1413b3f26530319181b37e3b9039b84fc01d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: f2add6fa510d3ae152903412227bda57d0d5a8da61d2c39c1fb022c9429a41c0
      url: "https://pub.dev"
    source: hosted
    version: "5.0.6"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: e4506d60b7244251bc59df15656a3093501c37fb5af02105a944d73eb95be4c9
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: ee1505df1426458f7f60aac270645098d318a8b4766d85fde75f76f2e21807d1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "5bc72e1e45e941d825fd7468b9b4cc3b9327942649aeb6fc5cdbf135f0a86e84"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=3.3.0 <4.0.0"
  flutter: ">=3.19.0"
