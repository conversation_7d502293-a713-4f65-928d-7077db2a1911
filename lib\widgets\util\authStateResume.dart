import 'package:budget/struct/settings.dart';
import 'package:budget/widgets/globalSnackbar.dart';
import 'package:flutter/material.dart';
import 'package:budget/main.dart';
import 'package:budget/functions.dart';
import 'package:budget/struct/initializeBiometrics.dart';
import 'package:budget/struct/firebaseAuthGlobal.dart';
import 'package:budget/widgets/accountAndBackup.dart';
import 'package:budget/widgets/openSnackbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:budget/struct/syncClient.dart';
import 'package:budget/widgets/navigationFramework.dart';
import 'package:budget/pages/settingsPage.dart';
import 'dart:async';

import 'onAppResume.dart';

/// A widget that handles app lifecycle events and preserves navigation state
/// across authentication.
///
/// This widget extends the functionality of OnAppResume by specifically
/// handling the restoration of the last active page after authentication.
class AuthStateResume extends StatefulWidget {
  const AuthStateResume({
    super.key,
    required this.child,
    this.onAppResume,
    this.onAppPaused,
    this.onAppInactive,
    this.updateGlobalAppLifecycleState = false,
  });

  final Widget child;
  final VoidCallback? onAppResume;
  final VoidCallback? onAppPaused;
  final VoidCallback? onAppInactive;
  final bool updateGlobalAppLifecycleState;

  @override
  State<AuthStateResume> createState() => _AuthStateResumeState();
}

class _AuthStateResumeState extends State<AuthStateResume> with WidgetsBindingObserver {
  // Store the last active page index
  int _lastActivePage = 0;
  AppLifecycleState? _lastState;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Check connectivity periodically instead of using ConnectivityManager
    _startConnectivityChecks();
  }

  void _startConnectivityChecks() {
    // Check connectivity every 30 seconds when app is active (much less frequent to avoid issues)
    Timer.periodic(Duration(seconds: 30), (timer) {
      if (mounted) {
        _checkAndHandleConnectivity();
      } else {
        timer.cancel();
      }
    });
  }

  void _checkAndHandleConnectivity() async {
    try {
      // Skip connectivity checks during debug mode on web to prevent issues
      if (kDebugMode && kIsWeb) {
        return;
      }

      // Skip connectivity checks if disabled or if we're in a stable offline state
      bool disableChecks = appStateSettings["disablePeriodicConnectivityChecks"] ?? false;
      bool isCurrentlyOffline = appStateSettings["isOfflineMode"] ?? false;

      if (disableChecks || (isCurrentlyOffline && !entireAppLoaded)) {
        return;
      }

      bool isOnline = await checkInternetConnection();
      _handleConnectivityChange(isOnline);
    } catch (e) {
      print("Error checking connectivity: $e");
    }
  }

  void _showConnectivitySnackbar() {
    openSnackbar(
      SnackbarMessage(
        title: "online-mode".tr(),
        icon: Icons.cloud_done,
        timeout: const Duration(milliseconds: 2500),
      ),
    );
  }

  void _forceUIRefresh() {
    try {
      // Force refresh of key UI components
      accountsPageStateKey.currentState?.refreshState();
      settingsPageStateKey.currentState?.refreshState();

      // Force refresh of settings framework (for debug/pro settings)
      settingsPageFrameworkStateKey.currentState?.refreshState();

      // Force refresh of More Actions page (for Cashew Pro banner and debug settings)
      MoreActionsPageState.refreshActiveInstance();

      // Force refresh of Settings Page Framework (for debug settings)
      SettingsPageFrameworkState.refreshActiveInstance();

      // Force refresh of the main app state
      appStateKey.currentState?.refreshAppState();

      // Force refresh of the navigation framework
      pageNavigationFrameworkKey.currentState?.setState(() {});

      // Force refresh all main pages to ensure debug settings are applied
      homePageStateKey.currentState?.refreshState();
      transactionsListPageStateKey.currentState?.refreshState();
      budgetsListPageStateKey.currentState?.refreshState();

      print("Forced comprehensive UI refresh after connectivity change");
    } catch (e) {
      print("Error during UI refresh: $e");
    }
  }

  void _refreshDebugAndProSettings() {
    try {
      // Force refresh of debug and pro settings by triggering a settings update
      // This ensures that any debug flags or pro settings are properly applied
      updateSettings("_connectivityRefreshTrigger", DateTime.now().toString(),
          pagesNeedingRefresh: [0, 1, 2, 3], // Refresh all main pages
          updateGlobalState: false);

      print("Triggered debug and pro settings refresh after connectivity change");
    } catch (e) {
      print("Error refreshing debug and pro settings: $e");
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Handle connectivity changes
  void _handleConnectivityChange(bool isOnline) async {
    try {
      // Add a delay to prevent rapid connectivity changes during app startup
      if (!entireAppLoaded) {
        await Future.delayed(Duration(seconds: 2));
      }

      if (isOnline && appStateSettings["isOfflineMode"] == true) {
        // We've gone from offline to online
        print("Connectivity restored, attempting to refresh authentication");

        // Update offline mode status
        updateSettings("isOfflineMode", false, updateGlobalState: true);

        // Re-enable token validation when going back online
        updateSettings("skipTokenValidation", false, updateGlobalState: true);

        // Re-enable background sync operations
        updateSettings("pauseBackgroundSync", false, updateGlobalState: true);

        // Re-enable periodic connectivity checks
        updateSettings("disablePeriodicConnectivityChecks", false, updateGlobalState: true);

        // Show a notification with the appropriate message, but not during startup
        if (entireAppLoaded) {
          _showConnectivitySnackbar();
        }

        // Force UI refresh when going back online
        _forceUIRefresh();

        // Trigger refresh of debug and pro settings
        _refreshDebugAndProSettings();

        // If we have a user or preserved authentication, try to refresh
        try {
          bool hasPreservedAuth = isUserAuthenticated();

          if (googleUser != null || hasPreservedAuth) {
            // Add a delay to ensure the app is fully initialized
            await Future.delayed(Duration(milliseconds: 1000));

            // Only attempt to sign in if we're still online
            if (await checkInternetConnection()) {
              try {
                // Try to refresh authentication
                if (googleUser != null) {
                  await signInGoogle(silentSignIn: true);
                } else if (hasPreservedAuth) {
                  // Try to restore authentication from preserved state
                  await signInGoogle(silentSignIn: true);
                }
                print("Successfully refreshed authentication after going online");

                // Trigger a sync after successful authentication refresh
                if (appStateSettings["backupSync"] == true) {
                  Future.delayed(Duration(milliseconds: 2000), () async {
                    try {
                      await syncData(navigatorKey.currentContext!);
                      print("Auto-sync completed after going online");
                    } catch (e) {
                      print("Auto-sync failed after going online: $e");
                    }
                  });
                }
              } catch (e) {
                print("Failed to refresh authentication after going online: $e");
                // Don't throw the error - we'll handle it gracefully
              }
            } else {
              print("Skipping authentication refresh - connection lost again");
            }
          } else {
            // If we don't have a user but should be online, check Firebase Auth
            print("No user available, checking Firebase Auth state");

            final firebaseUser = FirebaseAuthManager.instance.currentUser;
            if (firebaseUser != null) {
              print("Firebase Auth: Found cached user - ${firebaseUser.email}");
              // Firebase Auth will handle the state automatically
            }
          }
        } catch (e) {
          print("Error refreshing authentication: $e");
          // Don't let authentication errors crash the app
        }
      } else if (!isOnline && appStateSettings["isOfflineMode"] == false) {
        // We've gone from online to offline
        print("Connectivity lost, switching to offline mode");

        // Update offline mode status
        updateSettings("isOfflineMode", true, updateGlobalState: true);

        // CRITICAL: Disable token validation when going offline
        // This prevents authentication errors when offline
        updateSettings("skipTokenValidation", true, updateGlobalState: true);

        // Also disable any background sync operations
        updateSettings("pauseBackgroundSync", true, updateGlobalState: true);

        // Disable periodic connectivity checks when offline to prevent auth errors
        updateSettings("disablePeriodicConnectivityChecks", true, updateGlobalState: true);

        // Check Firebase Auth state for offline mode
        try {
          final firebaseUser = FirebaseAuthManager.instance.currentUser;
          if (firebaseUser != null) {
            print("Firebase Auth: User available for offline mode - ${firebaseUser.email}");

            // Ensure user data is preserved in offline mode
            updateSettings("currentUserEmail", firebaseUser.email ?? "", updateGlobalState: true);
            updateSettings("currentUserName", firebaseUser.displayName ?? "", updateGlobalState: true);
            updateSettings("currentUserPhotoUrl", firebaseUser.photoURL ?? "", updateGlobalState: true);
            updateSettings("hasSignedIn", true, updateGlobalState: true);

            print("Preserved user authentication state for offline mode");
          } else if (googleUser != null) {
            // If Firebase user is not available but Google user is, preserve Google user data
            print("Preserving Google user data for offline mode - ${googleUser?.email}");
            updateSettings("currentUserEmail", googleUser?.email ?? "", updateGlobalState: true);
            updateSettings("currentUserName", googleUser?.displayName ?? "", updateGlobalState: true);
            updateSettings("currentUserPhotoUrl", googleUser?.photoUrl ?? "", updateGlobalState: true);
            updateSettings("hasSignedIn", true, updateGlobalState: true);
          } else {
            print("No user data available for offline mode");
          }
        } catch (e) {
          print("Error checking Firebase Auth state: $e");
          // Even if there's an error, try to preserve existing user state
          if (appStateSettings["hasSignedIn"] == true && appStateSettings["currentUserEmail"] != "") {
            print("Preserving existing user state despite Firebase error");
          }
        }
      }
    } catch (e) {
      print("Error in _handleConnectivityChange: $e");
      // Don't let connectivity errors crash the app
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (_lastState == null) {
      _lastState = state;
    }

    // When app is paused (goes to background)
    if (state == AppLifecycleState.paused) {
      _saveCurrentPage();
      if (widget.onAppPaused != null) {
        widget.onAppPaused!();
      }
    }

    // When app becomes inactive (e.g., when showing authentication dialog)
    if (state == AppLifecycleState.inactive) {
      _saveCurrentPage();
      if (widget.onAppInactive != null) {
        widget.onAppInactive!();
      }
    }

    // When app is resumed from background
    if (state == AppLifecycleState.resumed &&
        (_lastState == AppLifecycleState.paused ||
            _lastState == AppLifecycleState.inactive)) {

      // Call the custom resume handler if provided
      if (widget.onAppResume != null) {
        widget.onAppResume!();
      }

      // Check connectivity when resuming
      checkInternetConnection().then((isOnline) {
        if (isOnline && appStateSettings["isOfflineMode"] == true) {
          _handleConnectivityChange(true);
        }
      });

      // Also refresh debug and pro settings when resuming
      Future.delayed(Duration(milliseconds: 500), () {
        _forceUIRefresh();
        _refreshDebugAndProSettings();
      });

      // Restore the last active page after a short delay
      // to ensure authentication has completed
      Future.delayed(Duration(milliseconds: 500), () {
        _restoreLastActivePage();
      });
    }

    _lastState = state;
    if (widget.updateGlobalAppLifecycleState) {
      appLifecycleState = state;
    }
  }

  // Save the current page index
  void _saveCurrentPage() {
    if (pageNavigationFrameworkKey.currentState != null) {
      _lastActivePage = pageNavigationFrameworkKey.currentState!.currentPage;
      // Also update the global variable for compatibility with existing code
      lastActivePage = _lastActivePage;
      print("AuthStateResume: Saved last active page: $_lastActivePage");
    }
  }

  // Restore the last active page if authentication was successful
  void _restoreLastActivePage() {
    // Only restore if we have a valid page index and authentication is complete
    if (_lastActivePage != 0 &&
        pageNavigationFrameworkKey.currentState != null &&
        authResult == AuthResult.authenticated) {

      print("AuthStateResume: Restoring to last active page: $_lastActivePage");

      // Check if we're already on the correct page to avoid unnecessary navigation
      // This helps preserve form data by not triggering unnecessary rebuilds
      if (pageNavigationFrameworkKey.currentState!.currentPage != _lastActivePage) {
        // Add a small delay to ensure smooth transition after authentication
        // This makes the animation feel more natural and less jarring
        Future.delayed(Duration(milliseconds: 350), () {
          // Use a custom page transition that's smoother than the default
          pageNavigationFrameworkKey.currentState!.changePage(
            _lastActivePage,
            // The page transition is handled by the PageNavigationFramework
            // but we're signaling that this is a restoration after authentication
            // which can be used to apply special transition effects
            isRestoringAfterAuth: true,
          );

          // Show a subtle visual indicator that the app state has been restored
          if (navigatorKey.currentContext != null) {
            ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
              SnackBar(
                content: Text("Restored to previous screen"),
                duration: Duration(seconds: 2),
                behavior: SnackBarBehavior.floating,
                margin: EdgeInsets.only(bottom: 10, left: 10, right: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        });
      } else {
        print("AuthStateResume: Already on the correct page, no navigation needed");
      }

      // Reset the saved page after restoration
      Future.delayed(Duration(milliseconds: 500), () {
        _lastActivePage = 0;
        lastActivePage = 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
