                        -HD:\App\Android Studio\FlutterSDK\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=26
-D<PERSON><PERSON>OID_PLATFORM=android-26
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\App\Android Studio\AndoirdSDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\App\Android Studio\AndoirdSDK\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\App\Android Studio\AndoirdSDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\App\Android Studio\AndoirdSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\App\Android Studio\Projects\budget\build\app\intermediates\cxx\RelWithDebInfo\3x194437\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\App\Android Studio\Projects\budget\build\app\intermediates\cxx\RelWithDebInfo\3x194437\obj\x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\App\Android Studio\Projects\budget\build\.cxx\RelWithDebInfo\3x194437\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2